'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { Eye, EyeOff, Mail, Lock, User, ArrowLeft } from 'lucide-react'
import { motion } from 'framer-motion'
import { toast } from 'sonner'
import { authHelpers } from '@/lib/supabase'
import { validateEmail, validatePassword, validateConfirmPassword, validateUsername } from '@/lib/validation'
import PasswordStrengthIndicator from '@/components/ui/PasswordStrengthIndicator'
import GoogleSignInButton from '@/app/auth/GoogleSignInButton'

export default function RegisterPage() {
  const router = useRouter()
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeToTerms: false
  })
  const [errors, setErrors] = useState<{[key: string]: string}>({})
  const [passwordStrength, setPasswordStrength] = useState({
    score: 0,
    label: 'ضعيف جداً',
    color: 'bg-red-500',
    percentage: 0
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Enhanced form validation
    const newErrors: {[key: string]: string} = {}

    // Username validation
    const usernameValidation = validateUsername(formData.username)
    if (!usernameValidation.isValid) {
      newErrors.username = usernameValidation.message
    }

    // Email validation
    const emailValidation = validateEmail(formData.email)
    if (!emailValidation.isValid) {
      newErrors.email = emailValidation.message
    }

    // Password validation
    const passwordValidation = validatePassword(formData.password)
    if (!passwordValidation.isValid) {
      newErrors.password = passwordValidation.message
    }

    // Confirm password validation
    const confirmPasswordValidation = validateConfirmPassword(formData.password, formData.confirmPassword)
    if (!confirmPasswordValidation.isValid) {
      newErrors.confirmPassword = confirmPasswordValidation.message
    }

    // Terms agreement validation
    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = 'يجب الموافقة على الشروط والأحكام'
    }

    setErrors(newErrors)

    if (Object.keys(newErrors).length === 0) {
      setIsLoading(true)

      try {
        const { data, error } = await authHelpers.signUp(formData.email, formData.password)

        if (error) {
          toast.error(error.message === 'User already registered'
            ? 'هذا البريد الإلكتروني مسجل بالفعل'
            : 'حدث خطأ أثناء إنشاء الحساب')
        } else {
          toast.success('تم إنشاء الحساب بنجاح! تحقق من بريدك الإلكتروني لتأكيد الحساب')
          router.push('/auth/login')
        }
      } catch (error) {
        toast.error('حدث خطأ غير متوقع')
      } finally {
        setIsLoading(false)
      }
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))

    // Real-time validation
    if (name === 'email' && value) {
      const emailValidation = validateEmail(value)
      if (!emailValidation.isValid) {
        setErrors(prev => ({ ...prev, email: emailValidation.message }))
      } else {
        setErrors(prev => ({ ...prev, email: '' }))
      }
    } else if (name === 'username' && value) {
      const usernameValidation = validateUsername(value)
      if (!usernameValidation.isValid) {
        setErrors(prev => ({ ...prev, username: usernameValidation.message }))
      } else {
        setErrors(prev => ({ ...prev, username: '' }))
      }
    } else if (name === 'password') {
      const passwordValidation = validatePassword(value)
      setPasswordStrength(passwordValidation.strength)
      if (value && !passwordValidation.isValid) {
        setErrors(prev => ({ ...prev, password: passwordValidation.message }))
      } else {
        setErrors(prev => ({ ...prev, password: '' }))
      }

      // Also validate confirm password if it exists
      if (formData.confirmPassword) {
        const confirmValidation = validateConfirmPassword(value, formData.confirmPassword)
        if (!confirmValidation.isValid) {
          setErrors(prev => ({ ...prev, confirmPassword: confirmValidation.message }))
        } else {
          setErrors(prev => ({ ...prev, confirmPassword: '' }))
        }
      }
    } else if (name === 'confirmPassword' && value) {
      const confirmValidation = validateConfirmPassword(formData.password, value)
      if (!confirmValidation.isValid) {
        setErrors(prev => ({ ...prev, confirmPassword: confirmValidation.message }))
      } else {
        setErrors(prev => ({ ...prev, confirmPassword: '' }))
      }
    }

    // Clear other errors when user starts typing
    if (errors[name] && !['email', 'username', 'password', 'confirmPassword'].includes(name)) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-white mb-2 font-vazirmatn">
          إنشاء حساب جديد
        </h1>
        <p className="text-slate-400 font-vazirmatn">
          انضم إلى BuildDocwithai وابدأ رحلتك
        </p>
      </div>

      {/* Google Sign-In */}
      <div className="space-y-4">
        <GoogleSignInButton>
          إنشاء حساب بـ Google
        </GoogleSignInButton>

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-slate-600"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-slate-800 text-slate-400 font-vazirmatn">أو</span>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Username Field */}
        <div>
          <label htmlFor="username" className="block text-sm font-medium text-slate-300 mb-2 font-vazirmatn">
            اسم المستخدم
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 right-0 rtl:left-0 rtl:right-auto pr-3 rtl:pl-3 rtl:pr-0 flex items-center pointer-events-none">
              <User className="h-5 w-5 text-slate-400" />
            </div>
            <input
              id="username"
              name="username"
              type="text"
              value={formData.username}
              onChange={handleInputChange}
              className={`block w-full pr-10 rtl:pl-10 rtl:pr-3 py-3 px-3 border rounded-lg bg-slate-700/50 text-white placeholder-slate-400 font-vazirmatn focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 ${
                errors.username ? 'border-red-500' : 'border-slate-600'
              }`}
              placeholder="أحمد محمد"
            />
          </div>
          {errors.username && (
            <p className="mt-1 text-sm text-red-400 font-vazirmatn">{errors.username}</p>
          )}
        </div>

        {/* Email Field */}
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-slate-300 mb-2 font-vazirmatn">
            البريد الإلكتروني
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 right-0 rtl:left-0 rtl:right-auto pr-3 rtl:pl-3 rtl:pr-0 flex items-center pointer-events-none">
              <Mail className="h-5 w-5 text-slate-400" />
            </div>
            <input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              className={`block w-full pr-10 rtl:pl-10 rtl:pr-3 py-3 px-3 border rounded-lg bg-slate-700/50 text-white placeholder-slate-400 font-vazirmatn focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 ${
                errors.email ? 'border-red-500' : 'border-slate-600'
              }`}
              placeholder="<EMAIL>"
            />
          </div>
          {errors.email && (
            <p className="mt-1 text-sm text-red-400 font-vazirmatn">{errors.email}</p>
          )}
        </div>

        {/* Password Fields */}
        <div className="space-y-6">
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-slate-300 mb-2 font-vazirmatn">
              كلمة المرور
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 right-0 rtl:left-0 rtl:right-auto pr-3 rtl:pl-3 rtl:pr-0 flex items-center pointer-events-none">
                <Lock className="h-5 w-5 text-slate-400" />
              </div>
              <input
                id="password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={handleInputChange}
                className={`block w-full pr-10 rtl:pl-10 rtl:pr-3 py-3 px-3 border rounded-lg bg-slate-700/50 text-white placeholder-slate-400 font-vazirmatn focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 ${
                  errors.password ? 'border-red-500' : 'border-slate-600'
                }`}
                placeholder="كلمة مرور قوية"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 left-0 rtl:right-0 rtl:left-auto pl-3 rtl:pr-3 rtl:pl-0 flex items-center"
              >
                {showPassword ? (
                  <EyeOff className="h-5 w-5 text-slate-400 hover:text-slate-300" />
                ) : (
                  <Eye className="h-5 w-5 text-slate-400 hover:text-slate-300" />
                )}
              </button>
            </div>
            {errors.password && (
              <p className="mt-1 text-sm text-red-400 font-vazirmatn">{errors.password}</p>
            )}

            {/* Password Strength Indicator */}
            <PasswordStrengthIndicator
              strength={passwordStrength}
              password={formData.password}
              className="mt-2"
            />
          </div>

          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-medium text-slate-300 mb-2 font-vazirmatn">
              تأكيد كلمة المرور
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 right-0 rtl:left-0 rtl:right-auto pr-3 rtl:pl-3 rtl:pr-0 flex items-center pointer-events-none">
                <Lock className="h-5 w-5 text-slate-400" />
              </div>
              <input
                id="confirmPassword"
                name="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                value={formData.confirmPassword}
                onChange={handleInputChange}
                className={`block w-full pr-10 rtl:pl-10 rtl:pr-3 py-3 px-3 border rounded-lg bg-slate-700/50 text-white placeholder-slate-400 font-vazirmatn focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 ${
                  errors.confirmPassword ? 'border-red-500' : 'border-slate-600'
                }`}
                placeholder="أعد كتابة كلمة المرور"
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute inset-y-0 left-0 rtl:right-0 rtl:left-auto pl-3 rtl:pr-3 rtl:pl-0 flex items-center"
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-5 w-5 text-slate-400 hover:text-slate-300" />
                ) : (
                  <Eye className="h-5 w-5 text-slate-400 hover:text-slate-300" />
                )}
              </button>
            </div>
            {errors.confirmPassword && (
              <p className="mt-1 text-sm text-red-400 font-vazirmatn">{errors.confirmPassword}</p>
            )}
          </div>
        </div>

        {/* Terms Agreement */}
        <div>
          <div className="flex items-start">
            <input
              id="agreeToTerms"
              name="agreeToTerms"
              type="checkbox"
              checked={formData.agreeToTerms}
              onChange={handleInputChange}
              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-slate-600 bg-slate-700 rounded mt-1"
            />
            <label htmlFor="agreeToTerms" className="mr-2 rtl:ml-2 rtl:mr-0 block text-sm text-slate-300 font-vazirmatn">
              أوافق على{' '}
              <Link href="/terms" className="text-indigo-400 hover:text-indigo-300 underline">
                الشروط والأحكام
              </Link>
              {' '}و{' '}
              <Link href="/privacy" className="text-indigo-400 hover:text-indigo-300 underline">
                سياسة الخصوصية
              </Link>
            </label>
          </div>
          {errors.agreeToTerms && (
            <p className="mt-1 text-sm text-red-400 font-vazirmatn">{errors.agreeToTerms}</p>
          )}
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          variant="gradient"
          size="lg"
          disabled={isLoading}
          className="w-full font-vazirmatn text-lg py-3 shadow-2xl shadow-indigo-500/25 hover:shadow-indigo-500/40 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'جاري إنشاء الحساب...' : 'إنشاء الحساب'}
          {!isLoading && <ArrowLeft className="w-5 h-5 mr-2 group-hover:translate-x-1 transition-transform" />}
        </Button>
      </form>

      {/* Login Link */}
      <div className="mt-8 text-center">
        <p className="text-slate-400 font-vazirmatn">
          لديك حساب بالفعل؟{' '}
          <Link
            href="/auth/login"
            className="text-indigo-400 hover:text-indigo-300 font-medium transition-colors duration-200"
          >
            تسجيل الدخول
          </Link>
        </p>
      </div>
    </motion.div>
  )
}
