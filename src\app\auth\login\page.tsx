'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { Eye, EyeOff, Mail, Lock, ArrowLeft } from 'lucide-react'
import { motion } from 'framer-motion'
import { toast } from 'sonner'
import { authHelpers } from '@/lib/supabase'
import { validateEmail } from '@/lib/validation'
import GoogleSignInButton from '@/app/auth/GoogleSignInButton'

export default function LoginPage() {
  const router = useRouter()
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  })
  const [errors, setErrors] = useState<{[key: string]: string}>({})

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Enhanced form validation
    const newErrors: {[key: string]: string} = {}

    // Email validation
    const emailValidation = validateEmail(formData.email)
    if (!emailValidation.isValid) {
      newErrors.email = emailValidation.message
    }

    // Password validation (basic for login - just check if not empty)
    if (!formData.password) {
      newErrors.password = 'كلمة المرور مطلوبة'
    }

    setErrors(newErrors)

    if (Object.keys(newErrors).length === 0) {
      setIsLoading(true)

      try {
        const { error } = await authHelpers.signIn(formData.email, formData.password)

        if (error) {
          toast.error(error.message === 'Invalid login credentials'
            ? 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
            : 'حدث خطأ أثناء تسجيل الدخول')
        } else {
          toast.success('تم تسجيل الدخول بنجاح!')
          router.push('/')
        }
      } catch (error) {
        toast.error('حدث خطأ غير متوقع')
      } finally {
        setIsLoading(false)
      }
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))

    // Real-time validation
    if (name === 'email' && value) {
      const emailValidation = validateEmail(value)
      if (!emailValidation.isValid) {
        setErrors(prev => ({ ...prev, email: emailValidation.message }))
      } else {
        setErrors(prev => ({ ...prev, email: '' }))
      }
    } else if (name === 'email' && !value) {
      setErrors(prev => ({ ...prev, email: '' }))
    }

    // Clear other errors when user starts typing
    if (errors[name] && name !== 'email') {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-white mb-2 font-vazirmatn">
          مرحباً بك مرة أخرى
        </h1>
        <p className="text-slate-400 font-vazirmatn">
          سجل دخولك للوصول إلى حسابك
        </p>
      </div>

      {/* Google Sign-In */}
      <div className="space-y-4">
        <GoogleSignInButton />

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-slate-600"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-slate-800 text-slate-400 font-vazirmatn">أو</span>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Email Field */}
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-slate-300 mb-2 font-vazirmatn">
            البريد الإلكتروني
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 right-0 rtl:left-0 rtl:right-auto pr-3 rtl:pl-3 rtl:pr-0 flex items-center pointer-events-none">
              <Mail className="h-5 w-5 text-slate-400" />
            </div>
            <input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              className={`block w-full pr-10 rtl:pl-10 rtl:pr-3 py-3 px-3 border rounded-lg bg-slate-700/50 text-white placeholder-slate-400 font-vazirmatn focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 ${
                errors.email ? 'border-red-500' : 'border-slate-600'
              }`}
              placeholder="أدخل بريدك الإلكتروني"
            />
          </div>
          {errors.email && (
            <p className="mt-1 text-sm text-red-400 font-vazirmatn">{errors.email}</p>
          )}
        </div>

        {/* Password Field */}
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-slate-300 mb-2 font-vazirmatn">
            كلمة المرور
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 right-0 rtl:left-0 rtl:right-auto pr-3 rtl:pl-3 rtl:pr-0 flex items-center pointer-events-none">
              <Lock className="h-5 w-5 text-slate-400" />
            </div>
            <input
              id="password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={handleInputChange}
              className={`block w-full pr-10 rtl:pl-10 rtl:pr-3 py-3 px-3 border rounded-lg bg-slate-700/50 text-white placeholder-slate-400 font-vazirmatn focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 ${
                errors.password ? 'border-red-500' : 'border-slate-600'
              }`}
              placeholder="أدخل كلمة المرور"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute inset-y-0 left-0 rtl:right-0 rtl:left-auto pl-3 rtl:pr-3 rtl:pl-0 flex items-center"
            >
              {showPassword ? (
                <EyeOff className="h-5 w-5 text-slate-400 hover:text-slate-300" />
              ) : (
                <Eye className="h-5 w-5 text-slate-400 hover:text-slate-300" />
              )}
            </button>
          </div>
          {errors.password && (
            <p className="mt-1 text-sm text-red-400 font-vazirmatn">{errors.password}</p>
          )}
        </div>

        {/* Remember Me & Forgot Password */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <input
              id="rememberMe"
              name="rememberMe"
              type="checkbox"
              checked={formData.rememberMe}
              onChange={handleInputChange}
              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-slate-600 bg-slate-700 rounded"
            />
            <label htmlFor="rememberMe" className="mr-2 rtl:ml-2 rtl:mr-0 block text-sm text-slate-300 font-vazirmatn">
              تذكرني
            </label>
          </div>
          <Link
            href="/auth/forgot-password"
            className="text-sm text-indigo-400 hover:text-indigo-300 font-vazirmatn transition-colors duration-200"
          >
            نسيت كلمة المرور؟
          </Link>
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          variant="gradient"
          size="lg"
          disabled={isLoading}
          className="w-full font-vazirmatn text-lg py-3 shadow-2xl shadow-indigo-500/25 hover:shadow-indigo-500/40 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
          {!isLoading && <ArrowLeft className="w-5 h-5 mr-2 group-hover:translate-x-1 transition-transform" />}
        </Button>
      </form>

      {/* Register Link */}
      <div className="mt-8 text-center">
        <p className="text-slate-400 font-vazirmatn">
          ليس لديك حساب؟{' '}
          <Link
            href="/auth/register"
            className="text-indigo-400 hover:text-indigo-300 font-medium transition-colors duration-200"
          >
            إنشاء حساب جديد
          </Link>
        </p>
      </div>
    </motion.div>
  )
}
