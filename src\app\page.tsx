import type { Metadata } from 'next'

// Layout components
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

// Section components
import HeroSection from '@/components/sections/HeroSection'
import FeaturesSection from '@/components/sections/FeaturesSection'
import TemplatesSection from '@/components/sections/TemplatesSection'
import StepsSection from '@/components/sections/StepsSection'
import IntegrationSection from '@/components/sections/IntegrationSection'
import TestimonialsSection from '@/components/sections/TestimonialsSection'
import CTASection from '@/components/sections/CTASection'

export const metadata: Metadata = {
  title: 'BuildDocwithai - أنشئ مستندات عملك بالذكاء الاصطناعي',
  description: 'منصة متطورة تساعدك في إنشاء خطط العمل ودراسات الجدوى والعروض التقديمية في دقائق معدودة',
  keywords: ['ذكاء اصطناعي', 'مستندات', 'خطط عمل', 'دراسات جدوى', 'عروض تقديمية'],
  authors: [{ name: 'BuildDocwithai Team' }],
  creator: 'BuildDocwithai',
  publisher: 'BuildDocwithai',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://builddocwithai.com'),
  openGraph: {
    title: 'BuildDocwithai - أنشئ مستندات عملك بالذكاء الاصطناعي',
    description: 'منصة متطورة تساعدك في إنشاء خطط العمل ودراسات الجدوى والعروض التقديمية في دقائق معدودة',
    url: 'https://builddocwithai.com',
    siteName: 'BuildDocwithai',
    locale: 'ar_SA',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'BuildDocwithai - أنشئ مستندات عملك بالذكاء الاصطناعي',
    description: 'منصة متطورة تساعدك في إنشاء خطط العمل ودراسات الجدوى والعروض التقديمية في دقائق معدودة',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function HomePage() {
  return (
    <div className="min-h-screen bg-slate-900">
      <Header />

      <main>
        <HeroSection />
        <FeaturesSection />
        <TemplatesSection />
        <StepsSection />
        <IntegrationSection />
        <TestimonialsSection />
        <CTASection />
      </main>

      <Footer />
    </div>
  )
}
