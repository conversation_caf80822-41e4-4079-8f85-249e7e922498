'use client'

import { motion } from 'framer-motion'
import { 
  BarChart3, 
  Presentation, 
  FileCheck, 
  Sparkles,
  ArrowLeft
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Recommendation } from '@/lib/dashboard-data'

interface RecommendationCardProps {
  recommendations: Recommendation[]
}

const iconMap = {
  BarChart3,
  Presentation,
  FileCheck
}

export default function RecommendationCard({ recommendations }: RecommendationCardProps) {
  return (
    <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6">
      {/* Header */}
      <div className="flex items-center space-x-3 rtl:space-x-reverse mb-6">
        <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
          <Sparkles className="w-4 h-4 text-white" />
        </div>
        <h2 className="text-xl font-semibold text-white font-vazirmatn">
          اقتراحات ذكية
        </h2>
      </div>

      {/* Recommendations */}
      <div className="space-y-4">
        {recommendations.map((recommendation, index) => {
          const Icon = iconMap[recommendation.icon as keyof typeof iconMap] || BarChart3
          
          return (
            <motion.div
              key={recommendation.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="p-4 bg-gradient-to-l from-indigo-500/5 to-purple-500/5 border border-indigo-500/10 rounded-lg hover:border-indigo-500/20 transition-all duration-300 group"
            >
              <div className="flex items-start space-x-4 rtl:space-x-reverse">
                {/* Icon */}
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-gradient-to-r from-indigo-500/20 to-purple-500/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                    <Icon className="w-5 h-5 text-indigo-400" />
                  </div>
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-semibold text-white font-vazirmatn mb-1">
                    {recommendation.title}
                  </h3>
                  <p className="text-xs text-slate-400 font-vazirmatn leading-relaxed mb-3">
                    {recommendation.description}
                  </p>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs font-vazirmatn border-indigo-500/30 text-indigo-400 hover:bg-indigo-500/10 hover:border-indigo-500/50"
                  >
                    <span>{recommendation.action}</span>
                    <ArrowLeft className="w-3 h-3 mr-1 rtl:ml-1 rtl:mr-0 group-hover:translate-x-1 rtl:group-hover:-translate-x-1 transition-transform duration-200" />
                  </Button>
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* AI Badge */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="mt-6 pt-4 border-t border-slate-700"
      >
        <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse">
          <div className="w-4 h-4 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center">
            <Sparkles className="w-2 h-2 text-white" />
          </div>
          <span className="text-xs text-slate-400 font-vazirmatn">
            مدعوم بالذكاء الاصطناعي
          </span>
        </div>
      </motion.div>
    </div>
  )
}
