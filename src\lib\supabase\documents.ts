import { createServerSupabaseClient, getAuthenticatedUser } from '@/lib/supabase-server'
import { GeneratedDocument } from '@/types/document-generation'

export interface DatabaseDocument {
  id: string
  user_id: string
  title: string
  description: string
  sections: any
  tier: 'quick' | 'professional'
  template_id: string
  project_id: string
  status: 'generating' | 'completed' | 'error'
  progress: number
  metadata: any
  created_at: string
  updated_at: string
}

// Save a generated document to the database
export async function saveGeneratedDocument(document: GeneratedDocument): Promise<{ data: DatabaseDocument | null; error: any }> {
  console.log('💾 saveGeneratedDocument called:', { documentId: document.id })

  // For testing purposes, allow saving without authentication
  const TESTING_MODE = false // Set to false in production

  try {
    let user = null
    let authError = null

    if (!TESTING_MODE) {
      const authResult = await getAuthenticatedUser()
      user = authResult.user
      authError = authResult.error

      console.log('👤 User authentication check:', {
        authenticated: !!user,
        userId: user?.id || 'none',
        authError: authError ? 'error' : 'none'
      })

      if (!user || authError) {
        console.log('❌ User not authenticated in saveGeneratedDocument')
        return { data: null, error: { message: 'User not authenticated' } }
      }
    } else {
      console.log('🧪 Testing mode: Skipping authentication check')
      // Use a proper UUID format for testing
      user = { id: '00000000-0000-0000-0000-000000000001', email: '<EMAIL>' }
    }

    const supabase = createServerSupabaseClient()

    const documentData = {
      id: document.id,
      user_id: user.id,
      title: document.title,
      description: document.description,
      sections: document.sections,
      tier: document.tier,
      template_id: document.templateId,
      project_id: document.projectId,
      status: document.status,
      progress: document.progress,
      metadata: document.metadata || {},
      created_at: document.createdAt,
      updated_at: document.updatedAt
    }

    console.log('📝 Inserting document data:', {
      id: documentData.id,
      user_id: documentData.user_id,
      title: documentData.title,
      sectionsCount: documentData.sections?.length || 0
    })

    const { data: savedDocument, error: saveError } = await supabase
      .from('generated_documents')
      .insert(documentData)
      .select()
      .single()

    if (saveError) {
      console.error('❌ Error saving document to Supabase:', saveError)
      return { data: null, error: saveError }
    }

    console.log('✅ Document successfully saved to Supabase:', {
      savedId: savedDocument?.id,
      title: savedDocument?.title
    })

    return { data: savedDocument, error: null }
  } catch (error) {
    console.error('Error in saveGeneratedDocument:', error)
    return { data: null, error }
  }
}

// Get a document by ID
export async function getGeneratedDocument(documentId: string): Promise<{ data: GeneratedDocument | null; error: any }> {
  console.log('🔍 getGeneratedDocument called:', { documentId })

  // For testing purposes, allow retrieval without authentication
  const TESTING_MODE = false // Set to false in production

  try {
    let user = null
    let authError = null

    if (!TESTING_MODE) {
      const authResult = await getAuthenticatedUser()
      user = authResult.user
      authError = authResult.error

      console.log('👤 User authentication check in getGeneratedDocument:', {
        authenticated: !!user,
        userId: user?.id || 'none',
        authError: authError ? 'error' : 'none'
      })

      if (!user || authError) {
        console.log('❌ User not authenticated in getGeneratedDocument')
        return { data: null, error: { message: 'User not authenticated' } }
      }
    } else {
      console.log('🧪 Testing mode: Skipping authentication check in getGeneratedDocument')
      // Use a proper UUID format for testing
      user = { id: '00000000-0000-0000-0000-000000000001', email: '<EMAIL>' }
    }

    const supabase = createServerSupabaseClient()

    console.log('🔍 Querying database for document:', {
      documentId,
      userId: user.id
    })

    const { data: document, error: fetchError } = await supabase
      .from('generated_documents')
      .select('*')
      .eq('id', documentId)
      .eq('user_id', user.id)
      .single()

    console.log('📊 Database query result:', {
      found: !!document,
      error: fetchError?.message || 'none',
      documentTitle: document?.title || 'N/A'
    })

    if (fetchError) {
      console.error('❌ Error fetching document from Supabase:', fetchError)
      return { data: null, error: fetchError }
    }

    if (!document) {
      console.log('❌ Document not found in database')
      return { data: null, error: { message: 'Document not found' } }
    }

    // Convert database document to GeneratedDocument format
    const generatedDocument: GeneratedDocument = {
      id: document.id,
      title: document.title,
      description: document.description,
      sections: document.sections,
      tier: document.tier,
      templateId: document.template_id,
      projectId: document.project_id,
      status: document.status,
      progress: document.progress,
      createdAt: document.created_at,
      updatedAt: document.updated_at,
      metadata: document.metadata
    }

    return { data: generatedDocument, error: null }
  } catch (error) {
    console.error('Error in getGeneratedDocument:', error)
    return { data: null, error }
  }
}

// Get all documents for the current user
export async function getUserDocuments(): Promise<{ data: GeneratedDocument[] | null; error: any }> {
  try {
    const { user, error: authError } = await getAuthenticatedUser()

    if (!user || authError) {
      return { data: null, error: { message: 'User not authenticated' } }
    }

    const supabase = createServerSupabaseClient()
    const { data: documents, error: fetchError } = await supabase
      .from('generated_documents')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (fetchError) {
      console.error('Error fetching user documents:', fetchError)
      return { data: null, error: fetchError }
    }

    // Convert database documents to GeneratedDocument format
    const generatedDocuments: GeneratedDocument[] = (documents || []).map(doc => ({
      id: doc.id,
      title: doc.title,
      description: doc.description,
      sections: doc.sections,
      tier: doc.tier,
      templateId: doc.template_id,
      projectId: doc.project_id,
      status: doc.status,
      progress: doc.progress,
      createdAt: doc.created_at,
      updatedAt: doc.updated_at,
      metadata: doc.metadata
    }))

    return { data: generatedDocuments, error: null }
  } catch (error) {
    console.error('Error in getUserDocuments:', error)
    return { data: null, error }
  }
}

// Update a document
export async function updateGeneratedDocument(documentId: string, updates: Partial<GeneratedDocument>): Promise<{ data: GeneratedDocument | null; error: any }> {
  try {
    const { user, error: authError } = await getAuthenticatedUser()

    if (!user || authError) {
      return { data: null, error: { message: 'User not authenticated' } }
    }

    const supabase = createServerSupabaseClient()

    const updateData: any = {
      updated_at: new Date().toISOString()
    }

    if (updates.title) updateData.title = updates.title
    if (updates.description) updateData.description = updates.description
    if (updates.sections) updateData.sections = updates.sections
    if (updates.status) updateData.status = updates.status
    if (updates.progress !== undefined) updateData.progress = updates.progress
    if (updates.metadata) updateData.metadata = updates.metadata

    const { data: updatedDocument, error: updateError } = await supabase
      .from('generated_documents')
      .update(updateData)
      .eq('id', documentId)
      .eq('user_id', user.id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating document:', updateError)
      return { data: null, error: updateError }
    }

    // Convert back to GeneratedDocument format
    const generatedDocument: GeneratedDocument = {
      id: updatedDocument.id,
      title: updatedDocument.title,
      description: updatedDocument.description,
      sections: updatedDocument.sections,
      tier: updatedDocument.tier,
      templateId: updatedDocument.template_id,
      projectId: updatedDocument.project_id,
      status: updatedDocument.status,
      progress: updatedDocument.progress,
      createdAt: updatedDocument.created_at,
      updatedAt: updatedDocument.updated_at,
      metadata: updatedDocument.metadata
    }

    return { data: generatedDocument, error: null }
  } catch (error) {
    console.error('Error in updateGeneratedDocument:', error)
    return { data: null, error }
  }
}

// Delete a document
export async function deleteGeneratedDocument(documentId: string): Promise<{ error: any }> {
  try {
    const { user, error: authError } = await getAuthenticatedUser()

    if (!user || authError) {
      return { error: { message: 'User not authenticated' } }
    }

    const supabase = createServerSupabaseClient()
    const { error: deleteError } = await supabase
      .from('generated_documents')
      .delete()
      .eq('id', documentId)
      .eq('user_id', user.id)

    if (deleteError) {
      console.error('Error deleting document:', deleteError)
      return { error: deleteError }
    }

    return { error: null }
  } catch (error) {
    console.error('Error in deleteGeneratedDocument:', error)
    return { error }
  }
}
