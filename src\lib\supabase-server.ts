import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

// Server-side Supabase client for API routes
export function createServerSupabaseClient() {
  return createRouteHandlerClient({ cookies })
}

// Helper function to get authenticated user in API routes
export async function getAuthenticatedUser() {
  const supabase = createServerSupabaseClient()
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error) {
      console.error('Error getting authenticated user:', error)
      return { user: null, error }
    }
    
    return { user, error: null }
  } catch (error) {
    console.error('Exception getting authenticated user:', error)
    return { user: null, error }
  }
}
