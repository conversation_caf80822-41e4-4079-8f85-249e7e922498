'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { Eye, EyeOff, Lock, ArrowLeft, CheckCircle, AlertCircle } from 'lucide-react'
import { motion } from 'framer-motion'

export default function ResetPasswordPage() {
  const searchParams = useSearchParams()
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: ''
  })
  const [errors, setErrors] = useState<{[key: string]: string}>({})
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [isValidToken, setIsValidToken] = useState(true)

  // Get token from URL parameters
  const token = searchParams.get('token')

  useEffect(() => {
    // In a real app, you would validate the token here
    if (!token) {
      setIsValidToken(false)
    }
  }, [token])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Form validation (UI only)
    const newErrors: {[key: string]: string} = {}
    
    if (!formData.password) {
      newErrors.password = 'كلمة المرور الجديدة مطلوبة'
    } else if (formData.password.length < 8) {
      newErrors.password = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل'
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = 'كلمة المرور يجب أن تحتوي على حروف كبيرة وصغيرة وأرقام'
    }
    
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'تأكيد كلمة المرور مطلوب'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'كلمة المرور غير متطابقة'
    }
    
    setErrors(newErrors)
    
    if (Object.keys(newErrors).length === 0) {
      // Here would be the actual password reset logic
      console.log('Password reset for token:', token, 'New password:', formData.password)
      setIsSubmitted(true)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  // Invalid token state
  if (!isValidToken) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="text-center"
      >
        <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
          <AlertCircle className="w-8 h-8 text-red-400" />
        </div>
        
        <h1 className="text-3xl font-bold text-white mb-4 font-vazirmatn">
          رابط غير صالح
        </h1>
        
        <p className="text-slate-400 mb-8 font-vazirmatn leading-relaxed">
          رابط إعادة تعيين كلمة المرور غير صالح أو منتهي الصلاحية.
          <br />
          يرجى طلب رابط جديد.
        </p>
        
        <div className="space-y-4">
          <Link href="/auth/forgot-password">
            <Button
              variant="gradient"
              size="lg"
              className="w-full font-vazirmatn"
            >
              طلب رابط جديد
              <ArrowLeft className="w-5 h-5 mr-2" />
            </Button>
          </Link>
          
          <Link href="/auth/login">
            <Button
              variant="ghost"
              size="lg"
              className="w-full font-vazirmatn"
            >
              العودة لتسجيل الدخول
            </Button>
          </Link>
        </div>
      </motion.div>
    )
  }

  // Success state
  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="text-center"
      >
        <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
          <CheckCircle className="w-8 h-8 text-green-400" />
        </div>
        
        <h1 className="text-3xl font-bold text-white mb-4 font-vazirmatn">
          تم تغيير كلمة المرور
        </h1>
        
        <p className="text-slate-400 mb-8 font-vazirmatn leading-relaxed">
          تم تغيير كلمة المرور بنجاح.
          <br />
          يمكنك الآن تسجيل الدخول بكلمة المرور الجديدة.
        </p>
        
        <Link href="/auth/login">
          <Button
            variant="gradient"
            size="lg"
            className="w-full font-vazirmatn"
          >
            تسجيل الدخول
            <ArrowLeft className="w-5 h-5 mr-2" />
          </Button>
        </Link>
      </motion.div>
    )
  }

  // Reset password form
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-white mb-2 font-vazirmatn">
          إعادة تعيين كلمة المرور
        </h1>
        <p className="text-slate-400 font-vazirmatn">
          أدخل كلمة المرور الجديدة لحسابك
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Password Field */}
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-slate-300 mb-2 font-vazirmatn">
            كلمة المرور الجديدة
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 right-0 rtl:left-0 rtl:right-auto pr-3 rtl:pl-3 rtl:pr-0 flex items-center pointer-events-none">
              <Lock className="h-5 w-5 text-slate-400" />
            </div>
            <input
              id="password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={handleInputChange}
              className={`block w-full pr-10 rtl:pl-10 rtl:pr-3 py-3 px-3 border rounded-lg bg-slate-700/50 text-white placeholder-slate-400 font-vazirmatn focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 ${
                errors.password ? 'border-red-500' : 'border-slate-600'
              }`}
              placeholder="أدخل كلمة مرور قوية"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute inset-y-0 left-0 rtl:right-0 rtl:left-auto pl-3 rtl:pr-3 rtl:pl-0 flex items-center"
            >
              {showPassword ? (
                <EyeOff className="h-5 w-5 text-slate-400 hover:text-slate-300" />
              ) : (
                <Eye className="h-5 w-5 text-slate-400 hover:text-slate-300" />
              )}
            </button>
          </div>
          {errors.password && (
            <p className="mt-1 text-sm text-red-400 font-vazirmatn">{errors.password}</p>
          )}
        </div>

        {/* Confirm Password Field */}
        <div>
          <label htmlFor="confirmPassword" className="block text-sm font-medium text-slate-300 mb-2 font-vazirmatn">
            تأكيد كلمة المرور
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 right-0 rtl:left-0 rtl:right-auto pr-3 rtl:pl-3 rtl:pr-0 flex items-center pointer-events-none">
              <Lock className="h-5 w-5 text-slate-400" />
            </div>
            <input
              id="confirmPassword"
              name="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              value={formData.confirmPassword}
              onChange={handleInputChange}
              className={`block w-full pr-10 rtl:pl-10 rtl:pr-3 py-3 px-3 border rounded-lg bg-slate-700/50 text-white placeholder-slate-400 font-vazirmatn focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 ${
                errors.confirmPassword ? 'border-red-500' : 'border-slate-600'
              }`}
              placeholder="أعد كتابة كلمة المرور"
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute inset-y-0 left-0 rtl:right-0 rtl:left-auto pl-3 rtl:pr-3 rtl:pl-0 flex items-center"
            >
              {showConfirmPassword ? (
                <EyeOff className="h-5 w-5 text-slate-400 hover:text-slate-300" />
              ) : (
                <Eye className="h-5 w-5 text-slate-400 hover:text-slate-300" />
              )}
            </button>
          </div>
          {errors.confirmPassword && (
            <p className="mt-1 text-sm text-red-400 font-vazirmatn">{errors.confirmPassword}</p>
          )}
        </div>

        {/* Password Requirements */}
        <div className="p-4 bg-slate-700/30 rounded-lg border border-slate-600/50">
          <h3 className="text-sm font-medium text-slate-300 mb-2 font-vazirmatn">
            متطلبات كلمة المرور:
          </h3>
          <ul className="text-sm text-slate-400 space-y-1 font-vazirmatn">
            <li className={formData.password.length >= 8 ? 'text-green-400' : ''}>
              • 8 أحرف على الأقل
            </li>
            <li className={/(?=.*[a-z])/.test(formData.password) ? 'text-green-400' : ''}>
              • حرف صغير واحد على الأقل
            </li>
            <li className={/(?=.*[A-Z])/.test(formData.password) ? 'text-green-400' : ''}>
              • حرف كبير واحد على الأقل
            </li>
            <li className={/(?=.*\d)/.test(formData.password) ? 'text-green-400' : ''}>
              • رقم واحد على الأقل
            </li>
          </ul>
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          variant="gradient"
          size="lg"
          className="w-full font-vazirmatn text-lg py-3 shadow-2xl shadow-indigo-500/25 hover:shadow-indigo-500/40 transition-all duration-300"
        >
          تغيير كلمة المرور
          <ArrowLeft className="w-5 h-5 mr-2 group-hover:translate-x-1 transition-transform" />
        </Button>
      </form>

      {/* Back to Login */}
      <div className="mt-8 text-center">
        <Link
          href="/auth/login"
          className="text-slate-400 hover:text-slate-300 font-vazirmatn transition-colors duration-200"
        >
          العودة لتسجيل الدخول
        </Link>
      </div>
    </motion.div>
  )
}
