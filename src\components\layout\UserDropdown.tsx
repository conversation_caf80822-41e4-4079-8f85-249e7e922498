'use client'

import { useState, useRef, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { User, LogOut, LayoutDashboard } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'sonner'

export default function UserDropdown() {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const router = useRouter()
  const { user, signOut } = useAuth()

  const handleSignOut = async () => {
    try {
      await signOut()
      toast.success('تم تسجيل الخروج بنجاح')
      setIsOpen(false)
    } catch (error) {
      toast.error('حدث خطأ أثناء تسجيل الخروج')
    }
  }

  const handleDashboard = () => {
    router.push('/dashboard')
    setIsOpen(false)
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  if (!user) return null

  const displayName = user.user_metadata?.full_name || user.email?.split('@')[0] || 'مستخدم'
  const userEmail = user.email || ''

  return (
    <div className="relative" ref={dropdownRef}>
      {/* User Avatar/Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 rtl:space-x-reverse bg-slate-800/50 hover:bg-slate-700/50 rounded-lg px-3 py-2 transition-all duration-200 border border-slate-700/50 hover:border-slate-600"
      >
        <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-violet-500 rounded-full flex items-center justify-center">
          <User className="w-4 h-4 text-white" />
        </div>
        <div className="hidden sm:block text-right rtl:text-left">
          <div className="text-sm font-medium text-white font-vazirmatn truncate max-w-32">
            {displayName}
          </div>
          <div className="text-xs text-slate-400 font-vazirmatn truncate max-w-32">
            {userEmail}
          </div>
        </div>
        <svg
          className={`w-4 h-4 text-slate-400 transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute left-0 rtl:right-0 rtl:left-auto mt-2 w-72 bg-slate-800 border border-slate-700 rounded-lg shadow-2xl z-50 overflow-hidden">
          {/* User Info Header */}
          <div className="px-4 py-3 border-b border-slate-700 bg-slate-800/50">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-violet-500 rounded-full flex items-center justify-center">
                <User className="w-5 h-5 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-white font-vazirmatn truncate">
                  {displayName}
                </div>
                <div className="text-xs text-slate-400 font-vazirmatn truncate">
                  {userEmail}
                </div>
              </div>
            </div>
          </div>

          {/* Menu Items */}
          <div className="py-2">
            <button
              onClick={handleDashboard}
              className="w-full px-4 py-3 text-right rtl:text-left hover:bg-slate-700/50 transition-colors duration-200 flex items-center space-x-3 rtl:space-x-reverse group"
            >
              <LayoutDashboard className="w-4 h-4 text-slate-400 group-hover:text-indigo-400 transition-colors" />
              <span className="text-sm text-slate-300 group-hover:text-white font-vazirmatn">
                الذهاب إلى لوحة التحكم
              </span>
            </button>

            <button
              onClick={handleSignOut}
              className="w-full px-4 py-3 text-right rtl:text-left hover:bg-red-500/10 transition-colors duration-200 flex items-center space-x-3 rtl:space-x-reverse group"
            >
              <LogOut className="w-4 h-4 text-slate-400 group-hover:text-red-400 transition-colors" />
              <span className="text-sm text-slate-300 group-hover:text-red-300 font-vazirmatn">
                تسجيل الخروج
              </span>
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
