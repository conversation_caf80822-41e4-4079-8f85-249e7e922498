'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import {
  Plus,
  Search,
  Filter,
  FileText,
  Calendar,
  Eye,
  Download,
  Edit,
  MoreVertical,
  Grid3X3,
  List
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { mockUserDocuments, Document } from '@/lib/document-types'

export default function DocumentsPage() {
  const [documents] = useState<Document[]>(mockUserDocuments)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         doc.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || doc.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const categories = [
    { id: 'all', name: 'جميع المستندات' },
    { id: 'مجموعة إطلاق المشاريع', name: 'إطلاق المشاريع' },
    { id: 'مجموعة الانسجام التشغيلي', name: 'الانسجام التشغيلي' },
    { id: 'مجموعة أساس للشركة', name: 'أساس الشركة' }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400 bg-green-500/10 border-green-500/20'
      case 'draft': return 'text-amber-400 bg-amber-500/10 border-amber-500/20'
      case 'published': return 'text-blue-400 bg-blue-500/10 border-blue-500/20'
      default: return 'text-slate-400 bg-slate-500/10 border-slate-500/20'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'مكتمل'
      case 'draft': return 'مسودة'
      case 'published': return 'منشور'
      default: return status
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
      >
        <div>
          <h1 className="text-3xl font-bold text-white font-vazirmatn mb-2">
            مستنداتي
          </h1>
          <p className="text-slate-400 font-vazirmatn">
            إدارة وتنظيم جميع مستنداتك المنشأة بالذكاء الاصطناعي
          </p>
        </div>
        
        <Link href="/dashboard/discover-documents">
          <Button className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 font-vazirmatn">
            <Plus className="w-4 h-4 ml-2" />
            إنشاء مستند جديد
          </Button>
        </Link>
      </motion.div>

      {/* Filters and Search */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6"
      >
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
            <Input
              placeholder="البحث في المستندات..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pr-10 bg-slate-700/50 border-slate-600 text-white placeholder-slate-400 font-vazirmatn"
            />
          </div>

          {/* Category Filter */}
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-slate-400" />
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white font-vazirmatn text-sm"
            >
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center gap-1 bg-slate-700/30 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-all duration-200 ${
                viewMode === 'grid' 
                  ? 'bg-indigo-500 text-white' 
                  : 'text-slate-400 hover:text-white'
              }`}
            >
              <Grid3X3 className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-all duration-200 ${
                viewMode === 'list' 
                  ? 'bg-indigo-500 text-white' 
                  : 'text-slate-400 hover:text-white'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>
      </motion.div>

      {/* Documents Grid/List */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        {filteredDocuments.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="w-16 h-16 text-slate-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-slate-300 font-vazirmatn mb-2">
              لا توجد مستندات
            </h3>
            <p className="text-slate-500 font-vazirmatn mb-6">
              ابدأ بإنشاء مستندك الأول باستخدام قوالبنا المتنوعة
            </p>
            <Link href="/dashboard/discover-documents">
              <Button className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 font-vazirmatn">
                <Plus className="w-4 h-4 ml-2" />
                إنشاء مستند جديد
              </Button>
            </Link>
          </div>
        ) : (
          <div className={
            viewMode === 'grid' 
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
              : 'space-y-4'
          }>
            {filteredDocuments.map((document, index) => (
              <motion.div
                key={document.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className={`bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 hover:border-indigo-500/30 transition-all duration-300 group ${
                  viewMode === 'list' ? 'flex items-center gap-6' : ''
                }`}
              >
                {/* Document Icon */}
                <div className={`w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center ${
                  viewMode === 'list' ? 'flex-shrink-0' : 'mb-4'
                }`}>
                  <FileText className="w-6 h-6 text-white" />
                </div>

                {/* Document Info */}
                <div className={viewMode === 'list' ? 'flex-1' : ''}>
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-lg font-semibold text-white font-vazirmatn group-hover:text-indigo-300 transition-colors duration-300">
                      {document.title}
                    </h3>
                    <button className="text-slate-400 hover:text-white transition-colors duration-200">
                      <MoreVertical className="w-4 h-4" />
                    </button>
                  </div>

                  <p className="text-slate-400 text-sm font-vazirmatn mb-4 line-clamp-2">
                    {document.description}
                  </p>

                  {/* Status and Meta */}
                  <div className="flex items-center gap-4 mb-4 text-xs">
                    <span className={`px-2 py-1 rounded-full border font-vazirmatn ${getStatusColor(document.status)}`}>
                      {getStatusText(document.status)}
                    </span>
                    <span className="text-slate-500 font-vazirmatn">
                      {document.pages} صفحة
                    </span>
                    <span className="text-slate-500 font-vazirmatn">
                      {document.size}
                    </span>
                  </div>

                  {/* Date */}
                  <div className="flex items-center text-xs text-slate-500 mb-4">
                    <Calendar className="w-3 h-3 ml-1" />
                    <span className="font-vazirmatn">
                      آخر تحديث: {new Date(document.updatedAt).toLocaleDateString('ar-SA')}
                    </span>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2">
                    <Button size="sm" variant="ghost" className="text-indigo-400 hover:text-indigo-300 hover:bg-indigo-500/10 font-vazirmatn">
                      <Eye className="w-3 h-3 ml-1" />
                      عرض
                    </Button>
                    <Button size="sm" variant="ghost" className="text-slate-400 hover:text-white hover:bg-slate-700/50 font-vazirmatn">
                      <Edit className="w-3 h-3 ml-1" />
                      تحرير
                    </Button>
                    <Button size="sm" variant="ghost" className="text-slate-400 hover:text-white hover:bg-slate-700/50 font-vazirmatn">
                      <Download className="w-3 h-3 ml-1" />
                      تحميل
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>
    </div>
  )
}
