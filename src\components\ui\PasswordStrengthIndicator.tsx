'use client'

import { motion } from 'framer-motion'
import { PasswordStrength } from '@/lib/validation'

interface PasswordStrengthIndicatorProps {
  strength: PasswordStrength
  password: string
  className?: string
}

export default function PasswordStrengthIndicator({ 
  strength, 
  password, 
  className = '' 
}: PasswordStrengthIndicatorProps) {
  if (!password) return null

  return (
    <div className={`mt-2 ${className}`}>
      {/* Progress Bar */}
      <div className="w-full bg-slate-700 rounded-full h-2 mb-2">
        <motion.div
          className={`h-2 rounded-full transition-all duration-300 ${strength.color}`}
          initial={{ width: 0 }}
          animate={{ width: `${strength.percentage}%` }}
          transition={{ duration: 0.3 }}
        />
      </div>

      {/* Strength Label */}
      <div className="flex items-center justify-between text-sm">
        <span className="text-slate-400 font-vazirmatn">قوة كلمة المرور:</span>
        <motion.span
          className={`font-medium font-vazirmatn ${
            strength.score === 0 ? 'text-red-400' :
            strength.score === 1 ? 'text-orange-400' :
            strength.score === 2 ? 'text-yellow-400' :
            strength.score === 3 ? 'text-blue-400' :
            'text-green-400'
          }`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        >
          {strength.label}
        </motion.span>
      </div>

      {/* Password Requirements Checklist */}
      <div className="mt-3 space-y-1">
        <div className="text-xs text-slate-400 font-vazirmatn mb-2">متطلبات كلمة المرور:</div>
        
        <PasswordRequirement
          met={password.length >= 8}
          text="8 أحرف على الأقل"
        />
        
        <PasswordRequirement
          met={/[A-Z]/.test(password)}
          text="حرف كبير واحد على الأقل"
        />
        
        <PasswordRequirement
          met={/[a-z]/.test(password)}
          text="حرف صغير واحد على الأقل"
        />
        
        <PasswordRequirement
          met={/\d/.test(password)}
          text="رقم واحد على الأقل"
        />
        
        <PasswordRequirement
          met={/[!@#$%^&*(),.?":{}|<>]/.test(password)}
          text="رمز خاص واحد على الأقل"
        />
      </div>
    </div>
  )
}

interface PasswordRequirementProps {
  met: boolean
  text: string
}

function PasswordRequirement({ met, text }: PasswordRequirementProps) {
  return (
    <motion.div
      className="flex items-center space-x-2 rtl:space-x-reverse text-xs"
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.2 }}
    >
      <div className={`w-3 h-3 rounded-full flex items-center justify-center ${
        met ? 'bg-green-500' : 'bg-slate-600'
      }`}>
        {met && (
          <motion.svg
            className="w-2 h-2 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.2 }}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={3}
              d="M5 13l4 4L19 7"
            />
          </motion.svg>
        )}
      </div>
      <span className={`font-vazirmatn ${
        met ? 'text-green-400' : 'text-slate-400'
      }`}>
        {text}
      </span>
    </motion.div>
  )
}
