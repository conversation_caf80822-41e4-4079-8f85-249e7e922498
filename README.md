
# BuildDocwithai - AI Document Builder

A modern Next.js 14+ application with full Arabic and English language support, built for creating and managing documents with AI assistance.

## 🚀 Features

### Core Technologies
- **Next.js 14+** with App Router and TypeScript
- **Tailwind CSS** with full RTL support
- **shadcn/ui** component library
- **Framer Motion** for animations
- **Supabase** for authentication and database
- **next-intl** for internationalization
- **Zustand** for state management
- **Vazirmatn** Google Font for Arabic text

### Language Support
- **Arabic (RTL)** - Primary language
- **English (LTR)** - Secondary language
- Dynamic language switching
- RTL/LTR layout adaptation
- Modular translation files

### UI/UX Features
- 🌙 Dark/Light theme toggle
- 📱 Fully responsive design
- 🎨 Modern, clean interface
- ⚡ Fast loading with optimized fonts
- 🔄 Smooth animations and transitions
- 🎭 Toast notifications with Sonner

## 📁 Project Structure

```
├── app/                    # Next.js app directory
├── components/            # Reusable components
│   └── ui/               # shadcn/ui components
├── lib/                  # Utility functions
│   ├── fonts.ts         # Google Fonts configuration
│   ├── i18n.ts          # Internationalization setup
│   └── supabase.ts      # Supabase client
├── store/                # Zustand store
│   └── useStore.ts      # Global state management
├── language/             # Translation files
│   ├── ar/              # Arabic translations
│   │   └── home.json    
│   └── en/              # English translations
│       └── home.json    
├── styles/              # Global styles
│   └── globals.css      # Tailwind + custom CSS
├── middleware.ts        # Next.js middleware for i18n
└── next.config.js       # Next.js configuration
```

## 🛠️ Installation & Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start development server:**
   ```bash
   npm run dev
   ```

3. **Build for production:**
   ```bash
   npm run build
   ```

## 🌐 Internationalization

### Adding New Languages
1. Create new translation files in `/language/{locale}/`
2. Update the locales array in `middleware.ts`
3. Add the locale to the Zustand store type

### Translation Structure
```json
{
  "hero_title": "Welcome Message",
  "navigation": {
    "home": "Home",
    "about": "About"
  },
  "actions": {
    "get_started": "Get Started"
  }
}
```

## 🎨 Theming & RTL Support

### Custom CSS Variables
The project uses CSS custom properties for theming:
```css
:root {
  --primary: 220 90% 56%;
  --background: 0 0% 100%;
  /* ... */
}
```

### RTL Utilities
- Use `me-*` instead of `mr-*` (margin-end)
- Use `ms-*` instead of `ml-*` (margin-start)
- Use `pe-*` instead of `pr-*` (padding-end)
- Use `ps-*` instead of `pl-*` (padding-start)

## 🔐 Authentication (Supabase)

The project is configured with Supabase for authentication:

```typescript
import { authHelpers } from '@/lib/supabase';

// Sign up
const { data, error } = await authHelpers.signUp(email, password);

// Sign in  
const { data, error } = await authHelpers.signIn(email, password);

// Sign out
const { error } = await authHelpers.signOut();
```

## 📦 State Management

Using Zustand for global state:

```typescript
import { useStore } from '@/store/useStore';

const { locale, setLocale, theme, setTheme } = useStore();
```

## 🎯 Next Steps

This foundation is ready for building:
- User authentication pages
- Document creation interface
- AI integration features
- File upload/management
- User dashboard
- Billing/subscription system

## 📄 License

MIT License - feel free to use this starter template for your projects.
