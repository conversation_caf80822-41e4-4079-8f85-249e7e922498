'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import {
  ArrowLeft,
  Calendar,
  DollarSign,
  Building,
  Users,
  FileText,
  Settings,
  Edit3,
  Download,
  Trash2,
  Search,
  Filter,
  Plus,
  Clock,
  TrendingUp,
  Activity,
  Share2,
  Archive,
  GitBranch,
  Target,
  BarChart3,
  Zap,
  Shield,
  Globe,
  Database,
  Layers,
  CheckCircle,
  AlertCircle,
  XCircle,
  PlayCircle,
  PauseCircle,
  RotateCcw,
  Star,
  Eye,
  MessageSquare,
  Bell,
  Link,
  ExternalLink,
  Copy,
  Upload,
  FolderOpen,
  History,
  UserPlus,
  Mail,
  Phone
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { getProject, deleteProject } from '@/lib/supabase/projects'
import { Project as DatabaseProject } from '@/types/database'
import { toast } from 'sonner'

// Use the database Project type directly
type Project = DatabaseProject

interface Document {
  id: string
  title: string
  type: string
  status: string
  createdAt: string
  lastModified: string
  size: string
}

interface TeamMember {
  id: string
  name: string
  email: string
  role: string
  avatar?: string
  status: 'active' | 'inactive'
  joinedAt: string
}

interface ActivityItem {
  id: string
  type: 'document_created' | 'document_updated' | 'member_added' | 'status_changed' | 'milestone_reached'
  title: string
  description: string
  timestamp: string
  user: string
  icon: string
}

// ProjectMilestone interface removed as milestones feature was removed

interface ProjectAnalytics {
  documentsCreated: number
  totalViews: number
  collaborators: number
  completionRate: number
  averageResponseTime: string
  lastBackup: string
}

export default function ProjectDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const [project, setProject] = useState<Project | null>(null)
  const [documents, setDocuments] = useState<Document[]>([])
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([])
  const [activities, setActivities] = useState<ActivityItem[]>([])
  const [analytics, setAnalytics] = useState<ProjectAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')
  const [activeTab, setActiveTab] = useState('overview')
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [deleteConfirmText, setDeleteConfirmText] = useState('')

  // Helper function to decode URL-encoded project ID/name
  const getProjectIdentifier = () => {
    return decodeURIComponent(params.id as string)
  }

  useEffect(() => {
    fetchProjectDetails()
  }, [params.id])

  const fetchProjectDetails = async () => {
    try {
      setLoading(true)
      const projectId = getProjectIdentifier()

      // Fetch project data from database
      const { data: project, error } = await getProject(projectId)

      if (error) {
        console.error('Error fetching project:', error)
        toast.error('حدث خطأ في تحميل بيانات المشروع')
        return
      }

      if (!project) {
        toast.error('المشروع غير موجود')
        return
      }

      setProject(project)

      const mockDocuments: Document[] = [
        {
          id: '1',
          title: 'خطة العمل الشاملة',
          type: 'خطة عمل',
          status: 'مكتمل',
          createdAt: '2024-01-15',
          lastModified: '2024-01-17',
          size: '2.4 MB'
        },
        {
          id: '2',
          title: 'دراسة الجدوى المالية',
          type: 'دراسة جدوى',
          status: 'قيد المراجعة',
          createdAt: '2024-01-16',
          lastModified: '2024-01-18',
          size: '1.8 MB'
        },
        {
          id: '3',
          title: 'عقد العمل - مطور واجهات',
          type: 'عقد عمل',
          status: 'مسودة',
          createdAt: '2024-01-17',
          lastModified: '2024-01-17',
          size: '0.5 MB'
        }
      ]

      // Mock team members
      const mockTeamMembers: TeamMember[] = [
        {
          id: '1',
          name: 'أحمد محمد',
          email: '<EMAIL>',
          role: 'مدير المشروع',
          status: 'active',
          joinedAt: '2024-01-10'
        },
        {
          id: '2',
          name: 'فاطمة علي',
          email: '<EMAIL>',
          role: 'مطورة واجهات',
          status: 'active',
          joinedAt: '2024-01-12'
        },
        {
          id: '3',
          name: 'محمد سالم',
          email: '<EMAIL>',
          role: 'مصمم UI/UX',
          status: 'inactive',
          joinedAt: '2024-01-15'
        }
      ]

      // Mock activities
      const mockActivities: ActivityItem[] = [
        {
          id: '1',
          type: 'document_created',
          title: 'تم إنشاء خطة العمل',
          description: 'قام أحمد محمد بإنشاء مستند خطة العمل الشاملة',
          timestamp: '2024-01-18T10:30:00Z',
          user: 'أحمد محمد',
          icon: 'FileText'
        },
        {
          id: '2',
          type: 'member_added',
          title: 'انضمام عضو جديد',
          description: 'انضمت فاطمة علي إلى فريق المشروع',
          timestamp: '2024-01-17T14:20:00Z',
          user: 'أحمد محمد',
          icon: 'UserPlus'
        },
        {
          id: '3',
          type: 'status_changed',
          title: 'تغيير حالة المشروع',
          description: 'تم تغيير حالة المشروع إلى "قيد التطوير"',
          timestamp: '2024-01-16T09:15:00Z',
          user: 'أحمد محمد',
          icon: 'Activity'
        }
      ]

      // Milestones removed as requested

      // Mock analytics
      const mockAnalytics: ProjectAnalytics = {
        documentsCreated: 8,
        totalViews: 156,
        collaborators: 3,
        completionRate: 65,
        averageResponseTime: '2.5 ساعة',
        lastBackup: '2024-01-18T08:00:00Z'
      }

      // Project already set above
      setDocuments(mockDocuments)
      setTeamMembers(mockTeamMembers)
      setActivities(mockActivities)
      // Milestones removed as requested
      setAnalytics(mockAnalytics)
    } catch (error) {
      console.error('Error fetching project details:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.type.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = filterType === 'all' || doc.type === filterType
    return matchesSearch && matchesFilter
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'مكتمل': return 'text-green-400 bg-green-500/20'
      case 'قيد المراجعة': return 'text-yellow-400 bg-yellow-500/20'
      case 'مسودة': return 'text-blue-400 bg-blue-500/20'
      default: return 'text-slate-400 bg-slate-500/20'
    }
  }

  const handleDeleteProject = async () => {
    if (!project || deleteConfirmText !== project.name) return

    try {
      const { error } = await deleteProject(project.id)

      if (error) {
        console.error('Error deleting project:', error)
        toast.error('حدث خطأ في حذف المشروع')
        return
      }

      toast.success(`تم حذف المشروع "${project.name}" بنجاح`)
      setShowDeleteModal(false)
      setDeleteConfirmText('')
      router.push('/dashboard/projects')
    } catch (error) {
      console.error('Error in handleDeleteProject:', error)
      toast.error('حدث خطأ في حذف المشروع')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="w-8 h-8 animate-spin rounded-full border-2 border-indigo-500 border-t-transparent" />
      </div>
    )
  }

  if (!project) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h1 className="text-2xl font-bold text-white font-vazirmatn mb-4">
          المشروع غير موجود
        </h1>
        <Button onClick={() => router.push('/dashboard/projects')}>
          العودة للمشاريع
        </Button>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between"
        >
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              onClick={() => router.push('/dashboard/projects')}
              className="text-slate-400 hover:text-white"
            >
              <ArrowLeft className="w-5 h-5 ml-2" />
              العودة للمشاريع
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-white font-vazirmatn">
                {project.name}
              </h1>
              <p className="text-slate-400 font-vazirmatn mt-1">
                تفاصيل المشروع والمستندات المرتبطة
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              className="text-slate-400 hover:text-white"
            >
              <Share2 className="w-5 h-5 ml-2" />
              مشاركة
            </Button>
            <Button className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600">
              <Plus className="w-5 h-5 ml-2" />
              إنشاء مستند جديد
            </Button>
          </div>
        </motion.div>

        {/* Tab Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-2 border border-slate-700"
        >
          <div className="flex items-center gap-2">
            {[
              { id: 'overview', label: 'نظرة عامة', icon: Eye },
              { id: 'documents', label: 'المستندات', icon: FileText },
              { id: 'activity', label: 'النشاط', icon: Activity },
              { id: 'analytics', label: 'التحليلات', icon: BarChart3 },
              { id: 'settings', label: 'الإعدادات', icon: Settings }
            ].map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-xl font-vazirmatn transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white shadow-lg'
                      : 'text-slate-400 hover:text-white hover:bg-slate-700/50'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                </button>
              )
            })}
          </div>
        </motion.div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            {/* Project Basic Information */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 border border-slate-700">
                <h2 className="text-xl font-bold text-white font-vazirmatn mb-4 flex items-center gap-2">
                  <FileText className="w-6 h-6 text-indigo-400" />
                  المعلومات الأساسية
                </h2>

                <div className="space-y-4">
                  <div>
                    <label className="text-slate-400 font-vazirmatn text-sm">اسم المشروع</label>
                    <div className="text-white font-vazirmatn font-semibold mt-1">{project.name}</div>
                  </div>

                  <div>
                    <label className="text-slate-400 font-vazirmatn text-sm">وصف المشروع</label>
                    <div className="text-slate-300 font-vazirmatn mt-1 leading-relaxed">{project.description}</div>
                  </div>

                  {project.profile && (
                    <div>
                      <label className="text-slate-400 font-vazirmatn text-sm">ملف المشروع</label>
                      <div className="text-slate-300 font-vazirmatn mt-1 leading-relaxed text-sm">{project.profile}</div>
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 border border-slate-700">
                <h2 className="text-xl font-bold text-white font-vazirmatn mb-4 flex items-center gap-2">
                  <Target className="w-6 h-6 text-purple-400" />
                  السوق والعملاء
                </h2>

                <div className="space-y-4">
                  {project.target_customers && (
                    <div>
                      <label className="text-slate-400 font-vazirmatn text-sm">الشريحة المستهدفة من العملاء</label>
                      <div className="text-slate-300 font-vazirmatn mt-1 leading-relaxed text-sm">{project.target_customers}</div>
                    </div>
                  )}

                  {project.customer_problem && (
                    <div>
                      <label className="text-slate-400 font-vazirmatn text-sm">مشكلة العميل</label>
                      <div className="text-slate-300 font-vazirmatn mt-1 leading-relaxed text-sm">{project.customer_problem}</div>
                    </div>
                  )}

                  {project.solution && (
                    <div>
                      <label className="text-slate-400 font-vazirmatn text-sm">الحل الخاص بك</label>
                      <div className="text-slate-300 font-vazirmatn mt-1 leading-relaxed text-sm">{project.solution}</div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Project Details */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 border border-slate-700">
                <h2 className="text-xl font-bold text-white font-vazirmatn mb-4 flex items-center gap-2">
                  <Building className="w-6 h-6 text-green-400" />
                  تفاصيل المشروع
                </h2>

                <div className="grid grid-cols-1 gap-4">
                  <div className="flex items-center gap-3 p-3 bg-slate-700/30 rounded-lg">
                    <Building className="w-5 h-5 text-indigo-400" />
                    <div>
                      <span className="text-slate-500 font-vazirmatn text-sm">القطاع</span>
                      <div className="text-slate-200 font-vazirmatn">{project.industry}</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 p-3 bg-slate-700/30 rounded-lg">
                    <Users className="w-5 h-5 text-purple-400" />
                    <div>
                      <span className="text-slate-500 font-vazirmatn text-sm">السوق المستهدف</span>
                      <div className="text-slate-200 font-vazirmatn">{project.target_market}</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 p-3 bg-slate-700/30 rounded-lg">
                    <DollarSign className="w-5 h-5 text-green-400" />
                    <div>
                      <span className="text-slate-500 font-vazirmatn text-sm">الميزانية</span>
                      <div className="text-slate-200 font-vazirmatn">{project.budget}</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 p-3 bg-slate-700/30 rounded-lg">
                    <Clock className="w-5 h-5 text-amber-400" />
                    <div>
                      <span className="text-slate-500 font-vazirmatn text-sm">المدة الزمنية</span>
                      <div className="text-slate-200 font-vazirmatn">{project.timeline}</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 border border-slate-700">
                <h2 className="text-xl font-bold text-white font-vazirmatn mb-4 flex items-center gap-2">
                  <Globe className="w-6 h-6 text-blue-400" />
                  الموقع والمؤسسون
                </h2>

                <div className="space-y-4">
                  {project.location && (
                    <div>
                      <label className="text-slate-400 font-vazirmatn text-sm">موقع المشروع</label>
                      <div className="text-slate-300 font-vazirmatn mt-1 text-sm">{project.location}</div>
                    </div>
                  )}

                  {project.founders && project.founders.length > 0 && (
                    <div>
                      <label className="text-slate-400 font-vazirmatn text-sm mb-2 block">المؤسسون</label>
                      <div className="space-y-2">
                        {project.founders.map((founder) => (
                          <div key={founder.id} className="flex items-center justify-between p-2 bg-slate-700/30 rounded-lg">
                            <span className="text-slate-300 font-vazirmatn text-sm">{founder.name}</span>
                            {founder.ownership_percentage && (
                              <span className="text-indigo-400 font-vazirmatn text-sm font-semibold">
                                {founder.ownership_percentage}%
                              </span>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Project Statistics */}
            <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 border border-slate-700">
              <h2 className="text-xl font-bold text-white font-vazirmatn mb-4 flex items-center gap-2">
                <BarChart3 className="w-6 h-6 text-amber-400" />
                إحصائيات المشروع
              </h2>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-slate-700/30 rounded-lg">
                  <FileText className="w-8 h-8 text-indigo-400 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-white font-vazirmatn">
                    {project.documents_count}
                  </div>
                  <div className="text-slate-400 font-vazirmatn text-sm">المستندات</div>
                </div>

                <div className="text-center p-4 bg-slate-700/30 rounded-lg">
                  <Users className="w-8 h-8 text-purple-400 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-white font-vazirmatn">
                    {teamMembers.length}
                  </div>
                  <div className="text-slate-400 font-vazirmatn text-sm">أعضاء الفريق</div>
                </div>

                <div className="text-center p-4 bg-slate-700/30 rounded-lg">
                  <Calendar className="w-8 h-8 text-green-400 mx-auto mb-2" />
                  <div className="text-lg font-bold text-white font-vazirmatn">
                    {new Date(project.created_at).toLocaleDateString('ar-SA', { day: 'numeric', month: 'short' })}
                  </div>
                  <div className="text-slate-400 font-vazirmatn text-sm">تاريخ الإنشاء</div>
                </div>

                <div className="text-center p-4 bg-slate-700/30 rounded-lg">
                  <Activity className="w-8 h-8 text-amber-400 mx-auto mb-2" />
                  <div className="text-lg font-bold text-white font-vazirmatn">
                    {new Date(project.last_activity).toLocaleDateString('ar-SA', { day: 'numeric', month: 'short' })}
                  </div>
                  <div className="text-slate-400 font-vazirmatn text-sm">آخر نشاط</div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-xl border border-indigo-500/20">
                <div className="flex items-center gap-2 mb-2">
                  <TrendingUp className="w-4 h-4 text-indigo-400" />
                  <span className="text-indigo-300 font-vazirmatn font-semibold">حالة المشروع</span>
                </div>
                <span className={`px-3 py-1 rounded-full text-sm font-vazirmatn ${getStatusColor(project.status)}`}>
                  {project.status}
                </span>
              </div>
            </div>
          </motion.div>
        )}

        {/* Documents Tab */}
        {activeTab === 'documents' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 border border-slate-700"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white font-vazirmatn">
              مستندات المشروع ({filteredDocuments.length})
            </h2>
            <div className="flex items-center gap-3">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                <Input
                  placeholder="البحث في المستندات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10 bg-slate-700/50 border-slate-600 text-white placeholder-slate-400 font-vazirmatn"
                />
              </div>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white font-vazirmatn"
              >
                <option value="all">جميع الأنواع</option>
                <option value="خطة عمل">خطة عمل</option>
                <option value="دراسة جدوى">دراسة جدوى</option>
                <option value="عقد عمل">عقد عمل</option>
                <option value="اتفاقية">اتفاقية</option>
              </select>
            </div>
          </div>

          {filteredDocuments.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="w-16 h-16 text-slate-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-slate-400 font-vazirmatn mb-2">
                لا توجد مستندات
              </h3>
              <p className="text-slate-500 font-vazirmatn mb-4">
                لم يتم العثور على مستندات تطابق البحث الحالي
              </p>
              <Button className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600">
                <Plus className="w-4 h-4 ml-2" />
                إنشاء مستند جديد
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredDocuments.map((document, index) => (
                <motion.div
                  key={document.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 * index }}
                  className="bg-gradient-to-br from-slate-700/50 to-slate-800/50 rounded-xl p-4 border border-slate-600/30 hover:border-indigo-500/30 transition-all duration-200 group"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <FileText className="w-5 h-5 text-indigo-400" />
                      <span className={`px-2 py-1 rounded-full text-xs font-vazirmatn ${getStatusColor(document.status)}`}>
                        {document.status}
                      </span>
                    </div>
                    <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button size="sm" variant="ghost" className="text-slate-400 hover:text-white p-1">
                        <Edit3 className="w-4 h-4" />
                      </Button>
                      <Button size="sm" variant="ghost" className="text-slate-400 hover:text-white p-1">
                        <Download className="w-4 h-4" />
                      </Button>
                      <Button size="sm" variant="ghost" className="text-slate-400 hover:text-red-400 p-1">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  <h3 className="text-white font-vazirmatn font-semibold mb-2 line-clamp-2">
                    {document.title}
                  </h3>

                  <div className="space-y-2 text-sm">
                    <div className="flex items-center justify-between">
                      <span className="text-slate-500 font-vazirmatn">النوع</span>
                      <span className="text-slate-300 font-vazirmatn">{document.type}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-slate-500 font-vazirmatn">الحجم</span>
                      <span className="text-slate-300 font-vazirmatn">{document.size}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-slate-500 font-vazirmatn">آخر تعديل</span>
                      <span className="text-slate-300 font-vazirmatn">
                        {new Date(document.lastModified).toLocaleDateString('ar-SA')}
                      </span>
                    </div>
                  </div>

                  <div className="mt-4 pt-3 border-t border-slate-600/30">
                    <Button
                      size="sm"
                      className="w-full bg-gradient-to-r from-indigo-500/20 to-purple-500/20 hover:from-indigo-500/30 hover:to-purple-500/30 text-indigo-300 border border-indigo-500/30"
                    >
                      فتح المستند
                    </Button>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
        )}



        {/* Activity Tab */}
        {activeTab === 'activity' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 border border-slate-700"
          >
            <h2 className="text-xl font-bold text-white font-vazirmatn mb-6">
              سجل النشاط
            </h2>

            <div className="space-y-4">
              {activities.map((activity, index) => (
                <motion.div
                  key={activity.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 * index }}
                  className="flex items-start gap-4 p-4 bg-gradient-to-r from-slate-700/30 to-slate-800/30 rounded-xl border border-slate-600/20"
                >
                  <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <Activity className="w-5 h-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-white font-vazirmatn font-semibold mb-1">
                      {activity.title}
                    </h3>
                    <p className="text-slate-300 font-vazirmatn text-sm mb-2">
                      {activity.description}
                    </p>
                    <div className="flex items-center gap-4 text-xs text-slate-400 font-vazirmatn">
                      <span>{activity.user}</span>
                      <span>•</span>
                      <span>{new Date(activity.timestamp).toLocaleDateString('ar-SA')}</span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}



        {/* Analytics Tab */}
        {activeTab === 'analytics' && analytics && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 border border-slate-700">
                <div className="flex items-center gap-3 mb-2">
                  <FileText className="w-6 h-6 text-indigo-400" />
                  <span className="text-slate-300 font-vazirmatn">المستندات</span>
                </div>
                <div className="text-3xl font-bold text-white font-vazirmatn">
                  {analytics.documentsCreated}
                </div>
              </div>

              <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 border border-slate-700">
                <div className="flex items-center gap-3 mb-2">
                  <Eye className="w-6 h-6 text-purple-400" />
                  <span className="text-slate-300 font-vazirmatn">المشاهدات</span>
                </div>
                <div className="text-3xl font-bold text-white font-vazirmatn">
                  {analytics.totalViews}
                </div>
              </div>

              <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 border border-slate-700">
                <div className="flex items-center gap-3 mb-2">
                  <Users className="w-6 h-6 text-green-400" />
                  <span className="text-slate-300 font-vazirmatn">المتعاونون</span>
                </div>
                <div className="text-3xl font-bold text-white font-vazirmatn">
                  {analytics.collaborators}
                </div>
              </div>

              <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 border border-slate-700">
                <div className="flex items-center gap-3 mb-2">
                  <TrendingUp className="w-6 h-6 text-amber-400" />
                  <span className="text-slate-300 font-vazirmatn">معدل الإنجاز</span>
                </div>
                <div className="text-3xl font-bold text-white font-vazirmatn">
                  {analytics.completionRate}%
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 border border-slate-700">
              <h3 className="text-xl font-bold text-white font-vazirmatn mb-4">
                معلومات إضافية
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                  <span className="text-slate-300 font-vazirmatn">متوسط وقت الاستجابة</span>
                  <span className="text-white font-vazirmatn">{analytics.averageResponseTime}</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                  <span className="text-slate-300 font-vazirmatn">آخر نسخة احتياطية</span>
                  <span className="text-white font-vazirmatn">
                    {new Date(analytics.lastBackup).toLocaleDateString('ar-SA')}
                  </span>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Settings Tab */}
        {activeTab === 'settings' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 border border-slate-700">
              <h2 className="text-xl font-bold text-white font-vazirmatn mb-6 flex items-center gap-2">
                <Settings className="w-6 h-6 text-indigo-400" />
                إعدادات المشروع
              </h2>

              <div className="space-y-6">
                {/* Project Management Section */}
                <div>
                  <h3 className="text-lg font-semibold text-white font-vazirmatn mb-4">إدارة المشروع</h3>
                  <div className="space-y-3">
                    <Button
                      variant="ghost"
                      className="w-full justify-start text-slate-300 hover:text-white hover:bg-slate-700/50"
                    >
                      <Edit3 className="w-5 h-5 ml-2" />
                      تعديل المشروع
                    </Button>
                    <Button
                      variant="ghost"
                      className="w-full justify-start text-slate-300 hover:text-white hover:bg-slate-700/50"
                    >
                      <Archive className="w-5 h-5 ml-2" />
                      أرشفة المشروع
                    </Button>
                  </div>
                </div>

                {/* Privacy Settings Section */}
                <div>
                  <h3 className="text-lg font-semibold text-white font-vazirmatn mb-4">إعدادات الخصوصية</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                      <div className="flex items-center gap-3">
                        <Shield className="w-5 h-5 text-green-400" />
                        <div>
                          <div className="text-white font-vazirmatn">مشروع خاص</div>
                          <div className="text-slate-400 font-vazirmatn text-sm">يمكن للأعضاء المدعوين فقط الوصول</div>
                        </div>
                      </div>
                      <div className="w-12 h-6 bg-indigo-500 rounded-full relative">
                        <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 right-0.5 transition-transform"></div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                      <div className="flex items-center gap-3">
                        <Globe className="w-5 h-5 text-blue-400" />
                        <div>
                          <div className="text-white font-vazirmatn">السماح بالمشاركة العامة</div>
                          <div className="text-slate-400 font-vazirmatn text-sm">إنشاء روابط مشاركة للمستندات</div>
                        </div>
                      </div>
                      <div className="w-12 h-6 bg-slate-600 rounded-full relative">
                        <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 left-0.5 transition-transform"></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Danger Zone */}
                <div className="border-t border-slate-700 pt-6">
                  <h3 className="text-lg font-semibold text-red-400 font-vazirmatn mb-4">منطقة الخطر</h3>
                  <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <AlertCircle className="w-5 h-5 text-red-400 mt-0.5" />
                      <div className="flex-1">
                        <h4 className="text-red-300 font-vazirmatn font-semibold mb-2">حذف المشروع</h4>
                        <p className="text-red-200/80 font-vazirmatn text-sm mb-4">
                          سيتم حذف المشروع وجميع المستندات المرتبطة به نهائياً. هذا الإجراء لا يمكن التراجع عنه.
                        </p>
                        <Button
                          variant="ghost"
                          className="text-red-400 hover:text-red-300 hover:bg-red-500/10 border border-red-500/30"
                          onClick={() => setShowDeleteModal(true)}
                        >
                          <Trash2 className="w-4 h-4 ml-2" />
                          حذف المشروع
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Delete Confirmation Modal */}
        {showDeleteModal && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 border border-slate-700 max-w-md w-full mx-4"
            >
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center">
                  <AlertCircle className="w-6 h-6 text-red-400" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white font-vazirmatn">تأكيد حذف المشروع</h3>
                  <p className="text-slate-400 font-vazirmatn text-sm">هذا الإجراء لا يمكن التراجع عنه</p>
                </div>
              </div>

              <div className="mb-6">
                <p className="text-slate-300 font-vazirmatn mb-4">
                  لتأكيد الحذف، يرجى كتابة اسم المشروع <span className="font-semibold text-white">"{project?.name}"</span> في الحقل أدناه:
                </p>
                <Input
                  value={deleteConfirmText}
                  onChange={(e) => setDeleteConfirmText(e.target.value)}
                  placeholder="اكتب اسم المشروع هنا..."
                  className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400 font-vazirmatn"
                />
              </div>

              <div className="flex items-center gap-3">
                <Button
                  onClick={handleDeleteProject}
                  disabled={deleteConfirmText !== project?.name}
                  className="flex-1 bg-red-500 hover:bg-red-600 disabled:bg-slate-600 disabled:cursor-not-allowed"
                >
                  <Trash2 className="w-4 h-4 ml-2" />
                  تأكيد الحذف
                </Button>
                <Button
                  variant="ghost"
                  onClick={() => {
                    setShowDeleteModal(false)
                    setDeleteConfirmText('')
                  }}
                  className="flex-1 text-slate-400 hover:text-white"
                >
                  إلغاء
                </Button>
              </div>
            </motion.div>
          </div>
        )}
    </div>
  )
}
