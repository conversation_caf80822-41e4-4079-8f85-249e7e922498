{"navigation": {"home": "Home", "features": "Features", "templates": "Templates", "pricing": "Pricing", "contact": "Contact", "login": "<PERSON><PERSON>", "signup": "Sign Up", "language": "Language"}, "hero": {"title": "Create Professional Business Documents with AI", "subtitle": "Advanced platform that helps you create business plans, feasibility studies, and presentations in minutes", "primaryCTA": "Start Free", "secondaryCTA": "Watch Demo"}, "features": {"title": "Features that make your work easier", "subtitle": "Discover how AI can transform the way you create your documents", "aiGeneration": {"title": "Smart AI Generation", "description": "Get professional documents customized for your business in seconds"}, "pdfExport": {"title": "High-Quality PDF Export", "description": "Export your documents in professional PDF format ready for printing and sharing"}, "templates": {"title": "Diverse Specialized Templates", "description": "Choose from a wide range of templates designed specifically for different industries"}}, "templates": {"title": "Specialized Document Templates", "subtitle": "Choose from a wide range of templates designed to meet your business needs", "businessPlan": "Business Plan", "feasibilityStudy": "Feasibility Study", "presentation": "Presentation", "proposal": "Project Proposal", "report": "Report", "contract": "Contract"}, "steps": {"title": "Three Simple Steps to Get Started", "subtitle": "Easy and fast process to create your professional documents", "step1": {"title": "<PERSON><PERSON>", "description": "Select the type of document you want to create from our wide collection"}, "step2": {"title": "Answer Questions", "description": "Answer simple questions about your project or business"}, "step3": {"title": "Download Document", "description": "Get your professional document ready to use"}}, "integrations": {"title": "Integrates with your favorite tools", "subtitle": "Connect BuildDocwithai with the work platforms you use daily"}, "testimonials": {"title": "What our customers say", "subtitle": "Discover how our platform has helped thousands develop their businesses", "testimonial1": {"text": "BuildDocwithai saved me hours of work in preparing the business plan. The results were professional and detailed.", "author": "<PERSON>", "position": "Tech Company Founder", "company": "Tech Innovation"}, "testimonial2": {"text": "The best platform I've used for creating feasibility studies. Easy to use with amazing results.", "author": "<PERSON><PERSON>", "position": "Business Consultant", "company": "Advanced Consulting"}, "testimonial3": {"text": "Helped me create professional presentations for my clients in record time.", "author": "<PERSON>", "position": "Sales Manager", "company": "Smart Solutions"}}, "cta": {"title": "Start your journey to success today", "subtitle": "Join thousands of entrepreneurs who trust BuildDocwithai to create their professional documents", "primaryCTA": "Start Free Now", "secondaryCTA": "Talk to Sales Team", "features": {"feature1": "14-day free trial", "feature2": "No credit card required", "feature3": "24/7 technical support"}}, "footer": {"description": "BuildDocwithai platform helps you create professional business documents with AI", "product": {"title": "Product", "features": "Features", "templates": "Templates", "pricing": "Pricing", "integrations": "Integrations"}, "company": {"title": "Company", "about": "About Us", "careers": "Careers", "contact": "Contact", "blog": "Blog"}, "support": {"title": "Support", "help": "Help Center", "documentation": "Documentation", "community": "Community", "status": "Service Status"}, "legal": {"title": "Legal", "privacy": "Privacy Policy", "terms": "Terms of Service", "cookies": "<PERSON><PERSON>"}, "copyright": "© 2024 BuildDocwithai. All rights reserved.", "social": {"twitter": "Twitter", "linkedin": "LinkedIn", "facebook": "Facebook", "instagram": "Instagram"}}, "common": {"loading": "Loading...", "error": "An error occurred", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "next": "Next", "previous": "Previous", "submit": "Submit"}}