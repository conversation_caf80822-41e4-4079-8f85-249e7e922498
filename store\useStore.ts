
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface StoreState {
  // Language state
  locale: 'ar' | 'en';
  setLocale: (locale: 'ar' | 'en') => void;
  
  // User session placeholder for Supabase
  user: any | null;
  session: any | null;
  setUser: (user: any) => void;
  setSession: (session: any) => void;
  
  // Theme state
  theme: 'light' | 'dark';
  setTheme: (theme: 'light' | 'dark') => void;
}

export const useStore = create<StoreState>()(
  persist(
    (set) => ({
      // Language state
      locale: 'ar',
      setLocale: (locale) => set({ locale }),
      
      // User session
      user: null,
      session: null,
      setUser: (user) => set({ user }),
      setSession: (session) => set({ session }),
      
      // Theme
      theme: 'light',
      setTheme: (theme) => set({ theme }),
    }),
    {
      name: 'builddocwithai-storage',
      partialize: (state) => ({ 
        locale: state.locale,
        theme: state.theme 
      }),
    }
  )
);
