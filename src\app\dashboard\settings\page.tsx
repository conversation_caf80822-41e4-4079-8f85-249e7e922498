'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  User,
  Mail,
  Phone,
  MapPin,
  Globe,
  Building,
  Briefcase,
  Linkedin,
  Twitter,
  Github,

  Trash2,
  AlertTriangle,
  Loader2,
  Camera,
  Edit3,
  Eye,
  EyeOff,
  Shield,
  Clock,
  Settings,
  Bell,
  Palette,
  Languages,
  Crown,
  Check,
  X
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Textarea } from '@/components/ui/Textarea'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import { supabase } from '@/lib/supabase'
import {
  getUserProfile,
  createUserProfile,
  updateUserProfile,
  deleteUserProfile,
  getOrCreateUserProfile
} from '@/lib/supabase/user-profiles'
import { UserProfile } from '@/types/database'

export default function SettingsPage() {
  const router = useRouter()
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [deleting, setDeleting] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [user, setUser] = useState<any>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [activeTab, setActiveTab] = useState('profile')

  // Form state
  const [formData, setFormData] = useState({
    username: '',
    phone_number: '',
    timezone: '',
    notes: '',
    two_factor_enabled: false,
    preferences: {
      theme: 'dark',
      language: 'ar',
      rtl_layout: true,
      notifications: true
    }
  })

  // Load user and profile data
  useEffect(() => {
    const loadUserData = async () => {
      try {
        setLoading(true)

        // Get authenticated user
        const { data: { user: authUser } } = await supabase.auth.getUser()
        if (!authUser) {
          router.push('/auth/login')
          return
        }
        setUser(authUser)

        // Get or create user profile
        const { data: userProfile, error } = await getOrCreateUserProfile()
        if (error) {
          console.error('Error loading profile:', error)
          toast.error('خطأ في تحميل البيانات الشخصية')
        } else if (userProfile) {
          setProfile(userProfile)
          setFormData({
            username: userProfile.username || '',
            phone_number: userProfile.phone_number || '',
            timezone: userProfile.timezone || '',
            notes: userProfile.notes || '',
            two_factor_enabled: userProfile.two_factor_enabled || false,
            preferences: userProfile.preferences || {
              theme: 'dark',
              language: 'ar',
              rtl_layout: true,
              notifications: true
            }
          })
        }
      } catch (error) {
        console.error('Error in loadUserData:', error)
        toast.error('خطأ في تحميل البيانات')
      } finally {
        setLoading(false)
      }
    }

    loadUserData()
  }, [router])

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.')
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof typeof prev] as any),
          [child]: value
        }
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }))
    }
  }

  const handleEdit = () => {
    setIsEditing(true)
  }

  const handleCancel = () => {
    if (profile) {
      setFormData({
        username: profile.username || '',
        phone_number: profile.phone_number || '',
        timezone: profile.timezone || '',
        notes: profile.notes || '',
        two_factor_enabled: profile.two_factor_enabled || false,
        preferences: profile.preferences || {
          theme: 'dark',
          language: 'ar',
          rtl_layout: true,
          notifications: true
        }
      })
    }
    setIsEditing(false)
  }

  const handleSave = async () => {
    if (!profile || !user) return

    try {
      setSaving(true)

      const { data: updatedProfile, error } = await updateUserProfile({
        id: profile.id,
        ...formData
      })

      if (error) {
        console.error('Error updating profile:', error)
        toast.error('خطأ في حفظ البيانات')
      } else {
        setProfile(updatedProfile)
        setIsEditing(false)
        toast.success('تم حفظ البيانات بنجاح')
      }
    } catch (error) {
      console.error('Error in handleSave:', error)
      toast.error('خطأ في حفظ البيانات')
    } finally {
      setSaving(false)
    }
  }

  const handleDeleteAccount = async () => {
    if (!user) return

    try {
      setDeleting(true)
      
      const { error } = await deleteUserProfile(true) // Delete both profile and auth user
      
      if (error) {
        console.error('Error deleting account:', error)
        toast.error('خطأ في حذف الحساب')
      } else {
        toast.success('تم حذف الحساب بنجاح')
        router.push('/')
      }
    } catch (error) {
      console.error('Error in handleDeleteAccount:', error)
      toast.error('خطأ في حذف الحساب')
    } finally {
      setDeleting(false)
      setShowDeleteModal(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-indigo-500 mx-auto mb-4" />
          <p className="text-slate-400 font-vazirmatn">جاري تحميل الإعدادات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-900 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white font-vazirmatn mb-2">
                إعدادات الحساب
              </h1>
              <p className="text-slate-400 font-vazirmatn">
                إدارة معلوماتك الشخصية وإعدادات الحساب
              </p>
            </div>
            {!isEditing ? (
              <Button
                onClick={handleEdit}
                className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 font-vazirmatn"
              >
                <Edit3 className="w-4 h-4 ml-2" />
                تعديل البيانات
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button
                  onClick={handleCancel}
                  variant="ghost"
                  className="font-vazirmatn text-slate-400 hover:text-white"
                >
                  <X className="w-4 h-4 ml-2" />
                  إلغاء
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={saving}
                  className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 font-vazirmatn"
                >
                  {saving ? (
                    <Loader2 className="w-4 h-4 animate-spin ml-2" />
                  ) : (
                    <Check className="w-4 h-4 ml-2" />
                  )}
                  {saving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
                </Button>
              </div>
            )}
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Profile Information */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="lg:col-span-2 space-y-6"
          >
            {/* Basic Information */}
            <div className="bg-slate-800 rounded-2xl p-6 border border-slate-700">
              <h2 className="text-xl font-bold text-white font-vazirmatn mb-6 flex items-center gap-3">
                <User className="w-5 h-5 text-indigo-400" />
                المعلومات الأساسية
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-slate-300 font-vazirmatn text-sm mb-2">
                    اسم المستخدم
                  </label>
                  {isEditing ? (
                    <Input
                      value={formData.username}
                      onChange={(e) => handleInputChange('username', e.target.value)}
                      placeholder="اسم المستخدم"
                      className="bg-slate-700 border-slate-600 text-white font-vazirmatn"
                    />
                  ) : (
                    <div className="p-3 bg-slate-700/50 rounded-lg text-white font-vazirmatn">
                      {profile?.username || 'غير محدد'}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-slate-300 font-vazirmatn text-sm mb-2">
                    رقم الهاتف
                  </label>
                  {isEditing ? (
                    <Input
                      value={formData.phone_number}
                      onChange={(e) => handleInputChange('phone_number', e.target.value)}
                      placeholder="+966 50 123 4567"
                      className="bg-slate-700 border-slate-600 text-white font-vazirmatn"
                    />
                  ) : (
                    <div className="p-3 bg-slate-700/50 rounded-lg text-white font-vazirmatn">
                      {profile?.phone_number || 'غير محدد'}
                    </div>
                  )}
                </div>
              </div>

              <div className="mt-4">
                <label className="block text-slate-300 font-vazirmatn text-sm mb-2">
                  المنطقة الزمنية
                </label>
                {isEditing ? (
                  <Input
                    value={formData.timezone}
                    onChange={(e) => handleInputChange('timezone', e.target.value)}
                    placeholder="Asia/Riyadh"
                    className="bg-slate-700 border-slate-600 text-white font-vazirmatn"
                  />
                ) : (
                  <div className="p-3 bg-slate-700/50 rounded-lg text-white font-vazirmatn">
                    {profile?.timezone || 'غير محدد'}
                  </div>
                )}
              </div>

              <div className="mt-4">
                <label className="block text-slate-300 font-vazirmatn text-sm mb-2">
                  ملاحظات
                </label>
                {isEditing ? (
                  <Textarea
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    placeholder="ملاحظات إضافية..."
                    rows={3}
                    className="bg-slate-700 border-slate-600 text-white font-vazirmatn"
                  />
                ) : (
                  <div className="p-3 bg-slate-700/50 rounded-lg text-white font-vazirmatn min-h-[80px]">
                    {profile?.notes || 'لا توجد ملاحظات'}
                  </div>
                )}
              </div>
            </div>

            {/* Account Information */}
            <div className="bg-slate-800 rounded-2xl p-6 border border-slate-700">
              <h2 className="text-xl font-bold text-white font-vazirmatn mb-6 flex items-center gap-3">
                <Mail className="w-5 h-5 text-indigo-400" />
                معلومات الحساب
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-slate-300 font-vazirmatn text-sm mb-2">
                    البريد الإلكتروني
                  </label>
                  <div className="p-3 bg-slate-700/50 rounded-lg text-white font-vazirmatn">
                    {user?.email || 'غير محدد'}
                  </div>
                  <p className="text-xs text-slate-500 font-vazirmatn mt-1">
                    لا يمكن تغيير البريد الإلكتروني
                  </p>
                </div>

                <div>
                  <label className="block text-slate-300 font-vazirmatn text-sm mb-2">
                    نوع الخطة
                  </label>
                  <div className="p-3 bg-slate-700/50 rounded-lg text-white font-vazirmatn flex items-center gap-2">
                    <Crown className={`w-4 h-4 ${profile?.plan_type === 'premium' ? 'text-yellow-400' : 'text-slate-400'}`} />
                    {profile?.plan_type === 'premium' ? 'خطة مميزة' : 'خطة مجانية'}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div>
                  <label className="block text-slate-300 font-vazirmatn text-sm mb-2">
                    تاريخ الإنشاء
                  </label>
                  <div className="p-3 bg-slate-700/50 rounded-lg text-white font-vazirmatn">
                    {profile?.created_at ? new Date(profile.created_at).toLocaleDateString('ar-SA') : 'غير محدد'}
                  </div>
                </div>

                <div>
                  <label className="block text-slate-300 font-vazirmatn text-sm mb-2">
                    آخر تسجيل دخول
                  </label>
                  <div className="p-3 bg-slate-700/50 rounded-lg text-white font-vazirmatn">
                    {profile?.last_login ? new Date(profile.last_login).toLocaleDateString('ar-SA') : 'غير محدد'}
                  </div>
                </div>
              </div>
            </div>

            {/* Security Settings */}
            <div className="bg-slate-800 rounded-2xl p-6 border border-slate-700">
              <h2 className="text-xl font-bold text-white font-vazirmatn mb-6 flex items-center gap-3">
                <Shield className="w-5 h-5 text-indigo-400" />
                إعدادات الأمان
              </h2>

              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg">
                  <div>
                    <h3 className="text-white font-vazirmatn font-medium">
                      المصادقة الثنائية
                    </h3>
                    <p className="text-slate-400 font-vazirmatn text-sm">
                      تعزيز أمان حسابك بخطوة إضافية للتحقق
                    </p>
                  </div>
                  {isEditing ? (
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.two_factor_enabled}
                        onChange={(e) => handleInputChange('two_factor_enabled', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-500"></div>
                    </label>
                  ) : (
                    <div className={`px-3 py-1 rounded-full text-xs font-vazirmatn ${
                      profile?.two_factor_enabled
                        ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                        : 'bg-red-500/20 text-red-300 border border-red-500/30'
                    }`}>
                      {profile?.two_factor_enabled ? 'مفعل' : 'غير مفعل'}
                    </div>
                  )}
                </div>

                <div className="p-4 bg-slate-700/30 rounded-lg">
                  <div className="flex items-center gap-3 mb-2">
                    <Clock className="w-4 h-4 text-slate-400" />
                    <h3 className="text-white font-vazirmatn font-medium">
                      آخر تغيير لكلمة المرور
                    </h3>
                  </div>
                  <p className="text-slate-400 font-vazirmatn text-sm">
                    {profile?.last_password_change
                      ? new Date(profile.last_password_change).toLocaleDateString('ar-SA')
                      : 'غير محدد'
                    }
                  </p>
                </div>
              </div>
            </div>

            {/* Preferences */}
            <div className="bg-slate-800 rounded-2xl p-6 border border-slate-700">
              <h2 className="text-xl font-bold text-white font-vazirmatn mb-6 flex items-center gap-3">
                <Settings className="w-5 h-5 text-indigo-400" />
                التفضيلات
              </h2>

              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Bell className="w-4 h-4 text-slate-400" />
                    <div>
                      <h3 className="text-white font-vazirmatn font-medium">
                        الإشعارات
                      </h3>
                      <p className="text-slate-400 font-vazirmatn text-sm">
                        تلقي إشعارات حول التحديثات والأنشطة
                      </p>
                    </div>
                  </div>
                  {isEditing ? (
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.preferences.notifications}
                        onChange={(e) => handleInputChange('preferences.notifications', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-500"></div>
                    </label>
                  ) : (
                    <div className={`px-3 py-1 rounded-full text-xs font-vazirmatn ${
                      profile?.preferences?.notifications
                        ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                        : 'bg-red-500/20 text-red-300 border border-red-500/30'
                    }`}>
                      {profile?.preferences?.notifications ? 'مفعل' : 'غير مفعل'}
                    </div>
                  )}
                </div>

                <div className="p-4 bg-slate-700/30 rounded-lg">
                  <div className="flex items-center gap-3 mb-2">
                    <Languages className="w-4 h-4 text-slate-400" />
                    <h3 className="text-white font-vazirmatn font-medium">
                      اللغة
                    </h3>
                  </div>
                  <p className="text-slate-400 font-vazirmatn text-sm">
                    العربية (افتراضي)
                  </p>
                </div>

                <div className="p-4 bg-slate-700/30 rounded-lg">
                  <div className="flex items-center gap-3 mb-2">
                    <Palette className="w-4 h-4 text-slate-400" />
                    <h3 className="text-white font-vazirmatn font-medium">
                      المظهر
                    </h3>
                  </div>
                  <p className="text-slate-400 font-vazirmatn text-sm">
                    المظهر الداكن (افتراضي)
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Sidebar */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            {/* Profile Summary */}
            <div className="bg-slate-800 rounded-2xl p-6 border border-slate-700">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <User className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-lg font-bold text-white font-vazirmatn">
                  {profile?.username || 'المستخدم'}
                </h3>
                <p className="text-slate-400 font-vazirmatn text-sm">
                  {user?.email}
                </p>
                <div className="flex items-center justify-center gap-2 mt-2">
                  <Crown className={`w-4 h-4 ${profile?.plan_type === 'premium' ? 'text-yellow-400' : 'text-slate-400'}`} />
                  <span className="text-xs text-slate-400 font-vazirmatn">
                    {profile?.plan_type === 'premium' ? 'خطة مميزة' : 'خطة مجانية'}
                  </span>
                </div>
              </div>
            </div>

            {/* Usage Stats */}
            <div className="bg-slate-800 rounded-2xl p-6 border border-slate-700">
              <h3 className="text-lg font-bold text-white font-vazirmatn mb-4">
                إحصائيات الاستخدام
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-slate-400 font-vazirmatn text-sm">المستندات المنشأة</span>
                  <span className="text-white font-vazirmatn font-bold">
                    {profile?.usage_limits?.documents_created || 0}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-400 font-vazirmatn text-sm">الحد الأقصى للمستندات</span>
                  <span className="text-white font-vazirmatn font-bold">
                    {profile?.usage_limits?.max_documents || 10}
                  </span>
                </div>
                <div className="w-full bg-slate-700/50 rounded-full h-2.5 overflow-hidden">
                  <div
                    className="bg-gradient-to-r from-indigo-500 to-purple-500 h-2.5 rounded-full transition-all duration-500"
                    style={{
                      width: `${Math.min(100, ((profile?.usage_limits?.documents_created || 0) / (profile?.usage_limits?.max_documents || 10)) * 100)}%`
                    }}
                  ></div>
                </div>
              </div>
            </div>

            {/* Danger Zone */}
            <div className="bg-slate-800 rounded-2xl p-6 border border-red-500/20">
              <h3 className="text-lg font-bold text-red-400 font-vazirmatn mb-4 flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                منطقة الخطر
              </h3>
              <Button
                onClick={() => setShowDeleteModal(true)}
                variant="ghost"
                className="w-full text-red-400 hover:text-red-300 hover:bg-red-500/10 font-vazirmatn border border-red-500/30"
              >
                <Trash2 className="w-4 h-4 ml-2" />
                حذف الحساب نهائياً
              </Button>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-slate-800 rounded-2xl p-6 max-w-md w-full border border-slate-700"
          >
            <div className="text-center">
              <AlertTriangle className="w-12 h-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-bold text-white font-vazirmatn mb-2">
                تأكيد حذف الحساب
              </h3>
              <p className="text-slate-400 font-vazirmatn mb-6">
                هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع بياناتك نهائياً.
              </p>
              
              <div className="flex gap-3">
                <Button
                  onClick={() => setShowDeleteModal(false)}
                  variant="ghost"
                  className="flex-1 font-vazirmatn"
                >
                  إلغاء
                </Button>
                <Button
                  onClick={handleDeleteAccount}
                  disabled={deleting}
                  className="flex-1 bg-red-600 hover:bg-red-700 font-vazirmatn"
                >
                  {deleting ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    'حذف الحساب'
                  )}
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  )
}
