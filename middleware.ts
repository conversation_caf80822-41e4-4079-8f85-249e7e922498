import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // DASHBOARD ROUTE PROTECTION
  // Note: This is a basic check. The actual auth verification happens in the dashboard layout
  if (pathname.startsWith('/dashboard')) {
    // Check for basic auth indicators (this will be enhanced by client-side auth)
    const hasAuthCookie = request.cookies.has('sb-access-token') ||
                         request.cookies.has('supabase-auth-token') ||
                         request.cookies.has('sb-zkjasrxgkkhfcafwilsb-auth-token')

    if (!hasAuthCookie) {
      // Redirect to login if no auth cookies found
      return NextResponse.redirect(new URL('/auth/login', request.url))
    }
  }

  // AGGRESSIVE LOCALE ROUTE BLOCKING
  // Block any route that starts with /ar or /en immediately
  if (pathname === '/ar' || pathname === '/en' ||
      pathname.startsWith('/ar/') || pathname.startsWith('/en/')) {

    // For /ar routes, try to preserve the path structure when possible
    if (pathname.startsWith('/ar/')) {
      const newPath = pathname.replace(/^\/ar/, '') || '/'
      return NextResponse.redirect(new URL(newPath, request.url), 301)
    }

    // For /ar, /en, or /en/* routes, redirect to root
    return NextResponse.redirect(new URL('/', request.url), 301)
  }

  return NextResponse.next()
}

// Configure middleware to run on dashboard and locale routes
export const config = {
  matcher: [
    // Dashboard routes (protected)
    '/dashboard/:path*',
    // Locale-based routes (blocked)
    '/ar/:path*',
    '/en/:path*',
  ],
}
