export interface Project {
  id: string
  name: string
  description: string
  type: ProjectType
  status: ProjectStatus
  createdAt: string
  updatedAt: string
  documentsCount: number
  progress: number
  color: string
  icon: string
}

export type ProjectType = 
  | 'business-plan' 
  | 'feasibility-study' 
  | 'marketing-plan' 
  | 'financial-analysis' 
  | 'research-project'
  | 'legal-documents'

export type ProjectStatus = 'active' | 'completed' | 'paused' | 'draft'

export interface CreateProjectData {
  name: string
  description: string
  type: ProjectType
}

export const PROJECT_TYPES: { value: ProjectType; label: string; description: string }[] = [
  {
    value: 'business-plan',
    label: 'خطة عمل',
    description: 'خطة شاملة لبدء أو تطوير مشروع تجاري'
  },
  {
    value: 'feasibility-study',
    label: 'دراسة جدوى',
    description: 'تحليل إمكانية نجاح المشروع من الناحية المالية والتقنية'
  },
  {
    value: 'marketing-plan',
    label: 'خطة تسويقية',
    description: 'استراتيجية شاملة للترويج والتسويق'
  },
  {
    value: 'financial-analysis',
    label: 'تحليل مالي',
    description: 'دراسة مالية مفصلة للمشروع أو الشركة'
  },
  {
    value: 'research-project',
    label: 'مشروع بحثي',
    description: 'بحث أكاديمي أو علمي متخصص'
  },
  {
    value: 'legal-documents',
    label: 'وثائق قانونية',
    description: 'عقود واتفاقيات ووثائق قانونية'
  }
]

export const PROJECT_STATUS_LABELS: Record<ProjectStatus, string> = {
  active: 'نشط',
  completed: 'مكتمل',
  paused: 'متوقف',
  draft: 'مسودة'
}

export const PROJECT_STATUS_COLORS: Record<ProjectStatus, string> = {
  active: 'bg-green-500/10 text-green-400 border-green-500/20',
  completed: 'bg-blue-500/10 text-blue-400 border-blue-500/20',
  paused: 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20',
  draft: 'bg-slate-500/10 text-slate-400 border-slate-500/20'
}

// Mock projects data
export function getProjects(): Project[] {
  return [
    {
      id: '1',
      name: 'مشروع متجر إلكتروني للأزياء',
      description: 'خطة عمل شاملة لإطلاق متجر إلكتروني متخصص في بيع الأزياء النسائية والرجالية مع التركيز على الأزياء العربية التراثية.',
      type: 'business-plan',
      status: 'active',
      createdAt: 'منذ 3 أيام',
      updatedAt: 'منذ ساعتين',
      documentsCount: 8,
      progress: 75,
      color: 'bg-gradient-to-r from-blue-500 to-indigo-500',
      icon: 'Briefcase'
    },
    {
      id: '2',
      name: 'دراسة جدوى مطعم شعبي',
      description: 'دراسة جدوى اقتصادية مفصلة لافتتاح مطعم يقدم الأكلات الشعبية السعودية في منطقة الرياض.',
      type: 'feasibility-study',
      status: 'active',
      createdAt: 'منذ أسبوع',
      updatedAt: 'أمس',
      documentsCount: 12,
      progress: 60,
      color: 'bg-gradient-to-r from-green-500 to-emerald-500',
      icon: 'BarChart3'
    },
    {
      id: '3',
      name: 'خطة تسويقية لتطبيق توصيل',
      description: 'استراتيجية تسويقية شاملة لإطلاق تطبيق توصيل الطعام في المدن الكبرى مع التركيز على المطاعم المحلية.',
      type: 'marketing-plan',
      status: 'completed',
      createdAt: 'منذ شهر',
      updatedAt: 'منذ أسبوعين',
      documentsCount: 15,
      progress: 100,
      color: 'bg-gradient-to-r from-purple-500 to-pink-500',
      icon: 'TrendingUp'
    },
    {
      id: '4',
      name: 'تحليل مالي لشركة تقنية',
      description: 'تحليل مالي مفصل لشركة تقنية ناشئة متخصصة في حلول الذكاء الاصطناعي للشركات الصغيرة والمتوسطة.',
      type: 'financial-analysis',
      status: 'paused',
      createdAt: 'منذ شهرين',
      updatedAt: 'منذ 3 أسابيع',
      documentsCount: 6,
      progress: 40,
      color: 'bg-gradient-to-r from-orange-500 to-red-500',
      icon: 'Calculator'
    },
    {
      id: '5',
      name: 'بحث حول التجارة الإلكترونية',
      description: 'دراسة بحثية حول تأثير التجارة الإلكترونية على الاقتصاد السعودي ومستقبل التسوق الرقمي.',
      type: 'research-project',
      status: 'draft',
      createdAt: 'منذ 3 أسابيع',
      updatedAt: 'منذ أسبوع',
      documentsCount: 3,
      progress: 25,
      color: 'bg-gradient-to-r from-cyan-500 to-blue-500',
      icon: 'BookOpen'
    },
    {
      id: '6',
      name: 'عقود شراكة تجارية',
      description: 'مجموعة من العقود والاتفاقيات القانونية للشراكة التجارية مع موردين محليين ودوليين.',
      type: 'legal-documents',
      status: 'active',
      createdAt: 'منذ 5 أيام',
      updatedAt: 'منذ يوم',
      documentsCount: 4,
      progress: 80,
      color: 'bg-gradient-to-r from-violet-500 to-purple-500',
      icon: 'FileText'
    }
  ]
}

export function getProjectById(id: string): Project | undefined {
  return getProjects().find(project => project.id === id)
}

export function getProjectsByStatus(status: ProjectStatus): Project[] {
  return getProjects().filter(project => project.status === status)
}

export function getProjectsByType(type: ProjectType): Project[] {
  return getProjects().filter(project => project.type === type)
}
