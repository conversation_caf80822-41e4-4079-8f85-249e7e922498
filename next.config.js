
/** @type {import('next').NextConfig} */
// const withNextIntl = require('next-intl/plugin')('./src/lib/i18n.ts');

const nextConfig = {
  typescript: {
    ignoreBuildErrors: false,
  },
  eslint: {
    ignoreDuringBuilds: false,
  },
  images: {
    domains: ['images.unsplash.com', 'via.placeholder.com'],
  },
  // AGGRESSIVE LOCALE ROUTE ELIMINATION
  async redirects() {
    return [
      // Redirect /ar to root (highest priority)
      {
        source: '/ar',
        destination: '/',
        permanent: true,
      },
      // Redirect /en to root
      {
        source: '/en',
        destination: '/',
        permanent: true,
      },
      // Redirect any /ar/* paths to corresponding root paths
      {
        source: '/ar/:path*',
        destination: '/:path*',
        permanent: true,
      },
      // Redirect any /en/* paths to root
      {
        source: '/en/:path*',
        destination: '/',
        permanent: true,
      },
    ];
  },
};

module.exports = nextConfig;
