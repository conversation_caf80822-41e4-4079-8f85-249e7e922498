import { NextRequest, NextResponse } from 'next/server'
import { getGeneratedDocument, updateGeneratedDocument, deleteGeneratedDocument } from '@/lib/supabase/documents'

// GET /api/documents/[id] - Fetch a document by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  console.log('🔍 Document retrieval API called')

  try {
    const documentId = params.id
    console.log('📄 Attempting to fetch document:', { documentId })

    if (!documentId) {
      console.log('❌ No document ID provided')
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    const { data: document, error } = await getGeneratedDocument(documentId)
    console.log('🔍 Database query result:', {
      found: !!document,
      error: error?.message || 'none',
      documentTitle: document?.title || 'N/A'
    })

    if (error) {
      if (error.message === 'Document not found') {
        console.log('❌ Document not found in database')
        return NextResponse.json(
          { error: 'Document not found' },
          { status: 404 }
        )
      }

      if (error.message === 'User not authenticated') {
        console.log('❌ User not authenticated')
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        )
      }

      console.error('❌ Error fetching document:', error)
      return NextResponse.json(
        { error: 'Failed to fetch document' },
        { status: 500 }
      )
    }

    console.log('✅ Document successfully retrieved:', {
      documentId: document.id,
      title: document.title,
      sectionsCount: document.sections?.length || 0
    })

    return NextResponse.json({ document })

  } catch (error) {
    console.error('Error in GET /api/documents/[id]:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/documents/[id] - Update a document
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const documentId = params.id
    const updates = await request.json()

    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    const { data: document, error } = await updateGeneratedDocument(documentId, updates)

    if (error) {
      if (error.message === 'User not authenticated') {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        )
      }

      console.error('Error updating document:', error)
      return NextResponse.json(
        { error: 'Failed to update document' },
        { status: 500 }
      )
    }

    return NextResponse.json({ document })

  } catch (error) {
    console.error('Error in PUT /api/documents/[id]:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/documents/[id] - Delete a document
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const documentId = params.id

    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    const { error } = await deleteGeneratedDocument(documentId)

    if (error) {
      if (error.message === 'User not authenticated') {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        )
      }

      console.error('Error deleting document:', error)
      return NextResponse.json(
        { error: 'Failed to delete document' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Error in DELETE /api/documents/[id]:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
