'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Plus,
  Folder,
  Search,
  Filter,
  Grid3X3,
  List,
  SortDesc,
  Loader2
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/Button'
import ProjectCard from './ProjectCard'
import CreateProjectModal from './CreateProjectModal'
import { Project } from '@/types/database'
import { getProjects, deleteProject } from '@/lib/supabase/projects'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'

type ProjectStatus = 'active' | 'paused' | 'draft' | 'completed'

const PROJECT_STATUS_LABELS: Record<ProjectStatus, string> = {
  active: 'نشط',
  paused: 'متوقف',
  draft: 'مسودة',
  completed: 'مكتمل'
}

export default function ProjectsView() {
  const router = useRouter()
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<ProjectStatus | 'all'>('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  // Fetch projects on component mount
  useEffect(() => {
    fetchProjects()
  }, [])

  const fetchProjects = async () => {
    try {
      setLoading(true)
      const { data, error } = await getProjects()

      if (error) {
        console.error('Error fetching projects:', error)
        toast.error('حدث خطأ في تحميل المشاريع')
        return
      }

      setProjects(data || [])
    } catch (error) {
      console.error('Error in fetchProjects:', error)
      toast.error('حدث خطأ في تحميل المشاريع')
    } finally {
      setLoading(false)
    }
  }

  const handleEditProject = (project: Project) => {
    // Navigate to edit page with project ID
    router.push(`/dashboard/projects/create/manual-entry?edit=${project.id}`)
  }

  const handleDeleteProject = async (project: Project) => {
    if (!confirm(`هل أنت متأكد من حذف المشروع "${project.name}"؟ هذا الإجراء لا يمكن التراجع عنه.`)) {
      return
    }

    try {
      const { error } = await deleteProject(project.id)

      if (error) {
        console.error('Error deleting project:', error)
        toast.error('حدث خطأ في حذف المشروع')
        return
      }

      toast.success(`تم حذف المشروع "${project.name}" بنجاح`)
      // Refresh projects list
      fetchProjects()
    } catch (error) {
      console.error('Error in handleDeleteProject:', error)
      toast.error('حدث خطأ في حذف المشروع')
    }
  }

  const handleViewProject = (project: Project) => {
    router.push(`/dashboard/projects/${project.id}`)
  }

  const handleDuplicateProject = (project: Project) => {
    toast.success(`سيتم نسخ المشروع "${project.name}" قريباً`)
  }

  const handleArchiveProject = (project: Project) => {
    toast.success(`تم أرشفة المشروع: ${project.name}`)
  }

  const handleShareProject = (project: Project) => {
    toast.success(`تم نسخ رابط المشروع: ${project.name}`)
  }

  // Filter projects based on search and status
  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = statusFilter === 'all' || project.status === statusFilter
    return matchesSearch && matchesStatus
  })

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-indigo-500 mx-auto mb-4" />
          <p className="text-slate-400 font-vazirmatn">جاري تحميل المشاريع...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="relative bg-gradient-to-br from-indigo-500/10 via-purple-500/10 to-pink-500/10 border border-indigo-500/20 rounded-2xl p-8 overflow-hidden backdrop-blur-sm"
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5 opacity-50"></div>
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-full blur-3xl"></div>
        
        <div className="relative z-10 flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-3 rtl:space-x-reverse mb-3">
              <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center shadow-lg">
                <Folder className="w-6 h-6 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-white font-vazirmatn bg-gradient-to-l from-white to-indigo-200 bg-clip-text text-transparent">
                مشاريعي
              </h1>
            </div>
            <p className="text-slate-300 font-vazirmatn text-lg leading-relaxed">
              إدارة وتنظيم جميع مشاريعك في مكان واحد
            </p>
          </div>
          
          <div className="hidden md:flex items-center space-x-4 rtl:space-x-reverse">
            <div className="text-center p-4 bg-white/5 rounded-xl backdrop-blur-sm border border-white/10">
              <div className="text-3xl font-bold text-white mb-1">{projects.length}</div>
              <div className="text-sm text-slate-400 font-vazirmatn">إجمالي المشاريع</div>
            </div>
            <div className="text-center p-4 bg-white/5 rounded-xl backdrop-blur-sm border border-white/10">
              <div className="text-3xl font-bold text-white mb-1">
                {projects.filter(p => p.status === 'active').length}
              </div>
              <div className="text-sm text-slate-400 font-vazirmatn">مشاريع نشطة</div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Create Project Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="flex justify-center"
      >
        <Button
          variant="gradient"
          size="xl"
          onClick={() => setIsCreateModalOpen(true)}
          className="font-vazirmatn text-lg px-10 py-5 shadow-2xl shadow-indigo-500/30 hover:shadow-indigo-500/50 transition-all duration-300 hover:scale-105 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 hover:from-indigo-600 hover:via-purple-600 hover:to-pink-600 border border-indigo-400/20 hover:border-indigo-400/40"
        >
          <Plus className="w-6 h-6 ml-2 rtl:mr-2 rtl:ml-0" />
          إنشاء مشروع جديد
        </Button>
      </motion.div>

      {/* Filters and Search */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 sm:space-x-4 rtl:sm:space-x-reverse"
      >
        {/* Search */}
        <div className="relative flex-1 max-w-md">
          <Search className="absolute right-3 rtl:left-3 rtl:right-auto top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
          <input
            type="text"
            placeholder="البحث في المشاريع..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 rtl:pr-10 rtl:pl-4 pr-4 py-3 bg-slate-800/50 border border-slate-700/50 rounded-xl text-white placeholder-slate-400 font-vazirmatn focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
          />
        </div>

        {/* Filters */}
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as ProjectStatus | 'all')}
            className="px-4 py-3 bg-slate-800/50 border border-slate-700/50 rounded-xl text-white font-vazirmatn focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
          >
            <option value="all">جميع الحالات</option>
            {Object.entries(PROJECT_STATUS_LABELS).map(([status, label]) => (
              <option key={status} value={status}>{label}</option>
            ))}
          </select>

          {/* View Mode Toggle */}
          <div className="flex items-center bg-slate-800/50 border border-slate-700/50 rounded-xl p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-lg transition-colors duration-200 ${
                viewMode === 'grid' 
                  ? 'bg-indigo-500 text-white' 
                  : 'text-slate-400 hover:text-white'
              }`}
            >
              <Grid3X3 className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-lg transition-colors duration-200 ${
                viewMode === 'list' 
                  ? 'bg-indigo-500 text-white' 
                  : 'text-slate-400 hover:text-white'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>
      </motion.div>

      {/* Projects Grid/List */}
      {filteredProjects.length > 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className={`grid gap-6 ${
            viewMode === 'grid' 
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
              : 'grid-cols-1'
          }`}
        >
          {filteredProjects.map((project, index) => (
            <ProjectCard
              key={project.id}
              project={project}
              index={index}
              onEdit={handleEditProject}
              onDelete={handleDeleteProject}
              onView={handleViewProject}
              onDuplicate={handleDuplicateProject}
              onArchive={handleArchiveProject}
              onShare={handleShareProject}
            />
          ))}
        </motion.div>
      ) : (
        /* Empty State */
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="text-center py-16"
        >
          <div className="w-24 h-24 bg-gradient-to-r from-slate-700 to-slate-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <Folder className="w-12 h-12 text-slate-400" />
          </div>
          <h3 className="text-xl font-semibold text-white font-vazirmatn mb-2">
            {searchQuery || statusFilter !== 'all' ? 'لا توجد مشاريع مطابقة' : 'لا توجد مشاريع حتى الآن'}
          </h3>
          <p className="text-slate-400 font-vazirmatn mb-6">
            {searchQuery || statusFilter !== 'all' 
              ? 'جرب تغيير معايير البحث أو الفلترة'
              : 'ابدأ بإنشاء مشروعك الأول لتنظيم مستنداتك'
            }
          </p>
          {(!searchQuery && statusFilter === 'all') && (
            <Button
              variant="gradient"
              size="lg"
              onClick={() => setIsCreateModalOpen(true)}
              className="font-vazirmatn"
            >
              <Plus className="w-5 h-5 ml-2 rtl:mr-2 rtl:ml-0" />
              إنشاء مشروع جديد
            </Button>
          )}
        </motion.div>
      )}

      {/* Create Project Modal */}
      <CreateProjectModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onProjectCreated={() => {
          setIsCreateModalOpen(false)
          fetchProjects() // Refresh projects list
        }}
      />
    </div>
  )
}
