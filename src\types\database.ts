// Database types for BuildDocwithai project management system

export interface Database {
  public: {
    Tables: {
      generated_documents: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string
          sections: any // JSON array of document sections
          tier: 'quick' | 'professional'
          template_id: string
          project_id: string
          status: 'generating' | 'completed' | 'error'
          progress: number
          metadata: any // JSON object with generation metadata
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description: string
          sections: any
          tier: 'quick' | 'professional'
          template_id: string
          project_id: string
          status?: 'generating' | 'completed' | 'error'
          progress?: number
          metadata?: any
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string
          sections?: any
          tier?: 'quick' | 'professional'
          template_id?: string
          project_id?: string
          status?: 'generating' | 'completed' | 'error'
          progress?: number
          metadata?: any
          created_at?: string
          updated_at?: string
        }
      }
      projects: {
        Row: {
          id: string
          user_id: string
          name: string
          description: string
          profile: string | null
          website: string | null
          location: string | null
          target_customers: string | null
          customer_problem: string | null
          solution: string | null
          industry: string | null
          target_market: string | null
          budget: string | null
          timeline: string | null
          status: 'active' | 'paused' | 'draft' | 'completed'
          documents_count: number
          last_activity: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          description: string
          profile?: string | null
          website?: string | null
          location?: string | null
          target_customers?: string | null
          customer_problem?: string | null
          solution?: string | null
          industry?: string | null
          target_market?: string | null
          budget?: string | null
          timeline?: string | null
          status?: 'active' | 'paused' | 'draft' | 'completed'
          documents_count?: number
          last_activity?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          description?: string
          profile?: string | null
          website?: string | null
          location?: string | null
          target_customers?: string | null
          customer_problem?: string | null
          solution?: string | null
          industry?: string | null
          target_market?: string | null
          budget?: string | null
          timeline?: string | null
          status?: 'active' | 'paused' | 'draft' | 'completed'
          documents_count?: number
          last_activity?: string
          created_at?: string
          updated_at?: string
        }
      }
      project_founders: {
        Row: {
          id: string
          project_id: string
          name: string
          ownership_percentage: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          name: string
          ownership_percentage?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          name?: string
          ownership_percentage?: number | null
          created_at?: string
          updated_at?: string
        }
      }
      user_profiles: {
        Row: {
          id: string
          user_id: string | null
          username: string
          plan_type: string
          created_at: string | null
          last_login: string | null
          device_info: any | null
          is_active: boolean | null
          email_verified: boolean | null
          phone_number: string | null
          profile_picture_url: string | null
          timezone: string | null
          locale: string | null
          subscription_expiry: string | null
          usage_limits: any | null
          roles: string[] | null
          preferences: any | null
          last_password_change: string | null
          two_factor_enabled: boolean | null
          device_sessions: any | null
          signup_source: string | null
          notes: string | null
        }
        Insert: {
          id?: string
          user_id?: string | null
          username: string
          plan_type?: string
          created_at?: string | null
          last_login?: string | null
          device_info?: any | null
          is_active?: boolean | null
          email_verified?: boolean | null
          phone_number?: string | null
          profile_picture_url?: string | null
          timezone?: string | null
          locale?: string | null
          subscription_expiry?: string | null
          usage_limits?: any | null
          roles?: string[] | null
          preferences?: any | null
          last_password_change?: string | null
          two_factor_enabled?: boolean | null
          device_sessions?: any | null
          signup_source?: string | null
          notes?: string | null
        }
        Update: {
          id?: string
          user_id?: string | null
          username?: string
          plan_type?: string
          created_at?: string | null
          last_login?: string | null
          device_info?: any | null
          is_active?: boolean | null
          email_verified?: boolean | null
          phone_number?: string | null
          profile_picture_url?: string | null
          timezone?: string | null
          locale?: string | null
          subscription_expiry?: string | null
          usage_limits?: any | null
          roles?: string[] | null
          preferences?: any | null
          last_password_change?: string | null
          two_factor_enabled?: boolean | null
          device_sessions?: any | null
          signup_source?: string | null
          notes?: string | null
        }
      }
    }
  }
}

// Application types
export interface Project {
  id: string
  user_id: string
  name: string
  description: string
  profile?: string | null
  website?: string | null
  location?: string | null
  target_customers?: string | null
  customer_problem?: string | null
  solution?: string | null
  industry?: string | null
  target_market?: string | null
  budget?: string | null
  timeline?: string | null
  status: 'active' | 'paused' | 'draft' | 'completed'
  documents_count: number
  last_activity: string
  created_at: string
  updated_at: string
  founders?: ProjectFounder[]
}

export interface ProjectFounder {
  id: string
  project_id: string
  name: string
  ownership_percentage?: number | null
  created_at: string
  updated_at: string
}

export interface CreateProjectData {
  name: string
  description: string
  profile?: string
  website?: string
  location?: string
  target_customers?: string
  customer_problem?: string
  solution?: string
  industry?: string
  target_market?: string
  budget?: string
  timeline?: string
  status?: 'active' | 'paused' | 'draft' | 'completed'
  founders?: Array<{
    name: string
    ownership_percentage?: number
  }>
}

export interface UpdateProjectData extends Partial<CreateProjectData> {
  id: string
}

// User Profile types
export interface UserProfile {
  id: string
  user_id: string | null
  username: string
  plan_type: string
  created_at: string | null
  last_login: string | null
  device_info?: any | null
  is_active?: boolean | null
  email_verified?: boolean | null
  phone_number?: string | null
  profile_picture_url?: string | null
  timezone?: string | null
  locale?: string | null
  subscription_expiry?: string | null
  usage_limits?: any | null
  roles?: string[] | null
  preferences?: any | null
  last_password_change?: string | null
  two_factor_enabled?: boolean | null
  device_sessions?: any | null
  signup_source?: string | null
  notes?: string | null
}

export interface CreateUserProfileData {
  user_id?: string | null
  username: string
  plan_type?: string
  phone_number?: string | null
  profile_picture_url?: string | null
  timezone?: string | null
  locale?: string | null
  preferences?: any | null
  two_factor_enabled?: boolean | null
  notes?: string | null
}

export interface UpdateUserProfileData extends Partial<CreateUserProfileData> {
  id: string
}
