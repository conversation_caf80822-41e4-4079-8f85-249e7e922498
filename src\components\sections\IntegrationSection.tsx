'use client'

import { motion } from 'framer-motion'

export default function IntegrationSection() {

  const integrations = [
    {
      name: 'Google Docs',
      logo: '📄',
      description: 'تصدير مباشر إلى Google Docs',
      color: 'from-blue-500 to-indigo-500',
    },
    {
      name: 'Microsoft Office',
      logo: '📊',
      description: 'تكامل مع Word و PowerPoint',
      color: 'from-orange-500 to-red-500',
    },
    {
      name: 'Notion',
      logo: '📝',
      description: 'حفظ المستندات في Notion',
      color: 'from-gray-600 to-gray-800',
    },
    {
      name: 'Dropbox',
      logo: '📦',
      description: 'تخزين سحابي آمن',
      color: 'from-blue-600 to-blue-800',
    },
    {
      name: 'Slack',
      logo: '💬',
      description: 'مشاركة فورية مع الفريق',
      color: 'from-purple-500 to-pink-500',
    },
    {
      name: 'Airtable',
      logo: '🗂️',
      description: 'إدارة البيانات والمشاريع',
      color: 'from-yellow-500 to-orange-500',
    },
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
      },
    },
  }

  return (
    <section id="integrations" className="section-padding bg-slate-800/30">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6">
            يتكامل مع أدواتك المفضلة
          </h2>
          <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
            اربط BuildDocwithai مع منصات العمل التي تستخدمها يومياً
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6"
        >
          {integrations.map((integration, index) => (
            <motion.div key={index} variants={itemVariants}>
              <div className="group cursor-pointer">
                <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 text-center hover:scale-105 transition-all duration-300 hover:shadow-xl hover:shadow-indigo-500/10 hover:border-indigo-500/30">
                  <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                    {integration.logo}
                  </div>
                  <h3 className="text-white font-semibold mb-2 group-hover:text-indigo-400 transition-colors duration-300">
                    {integration.name}
                  </h3>
                  <p className="text-slate-400 text-sm leading-relaxed">
                    {integration.description}
                  </p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Additional integrations info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-16"
        >
          <div className="bg-gradient-to-r from-indigo-500/10 to-violet-500/10 rounded-3xl p-8 border border-indigo-500/20 text-center">
            <h3 className="text-2xl font-bold text-white mb-4">
              المزيد من التكاملات قريباً
            </h3>
            <p className="text-slate-400 mb-6 max-w-2xl mx-auto leading-relaxed">
              نعمل باستمرار على إضافة المزيد من التكاملات مع الأدوات والمنصات الشائعة لتسهيل سير عملك.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
              <div className="text-center">
                <div className="text-2xl font-bold text-indigo-400 mb-2">API</div>
                <div className="text-sm text-slate-400">واجهة برمجة تطبيقات مفتوحة</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-violet-400 mb-2">Zapier</div>
                <div className="text-sm text-slate-400">أتمتة سير العمل</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-emerald-400 mb-2">Webhooks</div>
                <div className="text-sm text-slate-400">تكامل مخصص</div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Integration benefits */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-16 grid md:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          {[
            { icon: '🔄', title: 'مزامنة تلقائية', description: 'تحديث المستندات تلقائياً' },
            { icon: '🔒', title: 'أمان عالي', description: 'حماية بيانات متقدمة' },
            { icon: '⚡', title: 'سرعة فائقة', description: 'نقل البيانات بسرعة' },
            { icon: '🎯', title: 'دقة عالية', description: 'تكامل خالي من الأخطاء' },
          ].map((benefit, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="text-3xl mb-3">{benefit.icon}</div>
              <h4 className="text-white font-semibold mb-2">{benefit.title}</h4>
              <p className="text-slate-400 text-sm">{benefit.description}</p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
