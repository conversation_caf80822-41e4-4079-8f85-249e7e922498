@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --radius: 0.75rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-slate-900 text-white;
    font-family: 'Cairo', 'Inter', system-ui, sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }

  /* RTL-specific styles */
  [dir="rtl"] {
    text-align: right;
  }

  [dir="ltr"] {
    text-align: left;
  }

  /* Custom scrollbar for RTL */
  [dir="rtl"] ::-webkit-scrollbar {
    width: 8px;
  }

  [dir="rtl"] ::-webkit-scrollbar-track {
    background: #1e293b;
  }

  [dir="rtl"] ::-webkit-scrollbar-thumb {
    background: #475569;
    border-radius: 4px;
  }

  [dir="rtl"] ::-webkit-scrollbar-thumb:hover {
    background: #64748b;
  }
}

@layer components {
  .container {
    @apply mx-auto px-4 sm:px-6 lg:px-8;
    max-width: 1200px;
  }

  .btn-primary {
    @apply bg-indigo-500 hover:bg-indigo-600 text-white font-medium px-6 py-3 rounded-lg transition-colors duration-200 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-transparent border-2 border-white/20 hover:border-white/40 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200;
  }

  .card {
    @apply bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-xl;
  }

  .section-padding {
    @apply py-16 sm:py-20 lg:py-24;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-indigo-400 to-violet-400 bg-clip-text text-transparent;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
