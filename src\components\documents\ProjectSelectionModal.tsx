'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, ArrowLeft, ChevronDown, Plus, FileText, Building, Calendar, DollarSign } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Textarea } from '@/components/ui/Textarea'
import { DocumentTemplate } from '@/lib/document-types'
import { useRouter } from 'next/navigation'
import { getProjects } from '@/lib/supabase/projects'
import { Project as DatabaseProject } from '@/types/database'

// Use the database Project type
type Project = DatabaseProject

interface ProjectSelectionModalProps {
  isOpen: boolean
  onClose: () => void
  onBack: () => void
  template: DocumentTemplate | null
  selectedTier: 'quick' | 'professional'
  onCreateDocument: (data: DocumentCreationData) => void
}

export interface DocumentCreationData {
  templateId: string
  title: string
  description: string
  tier: 'quick' | 'professional'
  projectData: {
    projectName: string
    projectDescription: string
    industry: string
    targetMarket: string
    budget: string
    timeline: string
  }
}

export default function ProjectSelectionModal({
  isOpen,
  onClose,
  onBack,
  template,
  selectedTier,
  onCreateDocument
}: ProjectSelectionModalProps) {
  const router = useRouter()
  const [isCreating, setIsCreating] = useState(false)
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [documentNotes, setDocumentNotes] = useState('')
  const [projects, setProjects] = useState<Project[]>([])
  const [loadingProjects, setLoadingProjects] = useState(true)

  // Fetch projects from database
  useEffect(() => {
    const fetchProjects = async () => {
      if (!isOpen) return

      setLoadingProjects(true)
      try {
        const { data, error } = await getProjects()
        if (error) {
          console.error('Error fetching projects:', error)
        } else {
          setProjects(data || [])
        }
      } catch (error) {
        console.error('Error fetching projects:', error)
      } finally {
        setLoadingProjects(false)
      }
    }

    fetchProjects()
  }, [isOpen])

  const handleProjectSelect = (project: Project) => {
    setSelectedProject(project)
  }

  const handleNewProjectNavigation = () => {
    router.push('/dashboard/projects')
  }

  const handleCreateDocument = async () => {
    if (!template || !selectedProject) return

    setIsCreating(true)

    try {
      const projectData = {
        projectName: selectedProject.name,
        projectDescription: selectedProject.description,
        industry: selectedProject.industry || 'غير محدد',
        targetMarket: selectedProject.target_market || 'غير محدد',
        budget: selectedProject.budget || 'غير محدد',
        timeline: selectedProject.timeline || 'غير محدد'
      }

      const documentData: DocumentCreationData = {
        templateId: template.id,
        title: `${template.title} - ${projectData.projectName}`,
        description: documentNotes || template.description,
        tier: selectedTier,
        projectData
      }

      onCreateDocument(documentData)
    } catch (error) {
      console.error('Error creating document:', error)
    } finally {
      setIsCreating(false)
    }
  }

  const canCreateDocument = () => {
    return selectedProject !== null
  }

  if (!isOpen || !template) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-2 sm:p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.3 }}
          className="bg-gradient-to-br from-slate-800 via-slate-800 to-slate-900 border border-slate-700 rounded-2xl max-w-4xl w-full max-h-[90vh] sm:max-h-[80vh] overflow-hidden shadow-2xl mx-2 sm:mx-0"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="relative p-4 border-b border-slate-700/50 bg-gradient-to-r from-slate-800/50 to-slate-900/50">
            {/* Progress Indicator */}
            <div className="absolute top-0 left-0 right-0 h-1 bg-slate-700">
              <div className="h-full w-full bg-gradient-to-r from-indigo-500 to-purple-500"></div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <button
                  onClick={onBack}
                  className="text-slate-400 hover:text-white transition-colors duration-200 p-2 hover:bg-slate-700/50 rounded-lg"
                >
                  <ArrowLeft className="w-4 h-4" />
                </button>
                <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center shadow-lg">
                  <FileText className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <h2 className="text-xl font-bold text-white font-vazirmatn">
                      إنشاء {template.title} ({selectedTier === 'professional' ? 'احترافية' : 'سريعة'})
                    </h2>
                    <span className="text-xs bg-green-500/20 text-green-300 px-2 py-1 rounded-full font-vazirmatn">
                      2/2
                    </span>
                  </div>
                  <p className="text-slate-400 font-vazirmatn text-sm">
                    اختر مشروعاً موجوداً أو أنشئ مشروعاً جديداً
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-slate-400 hover:text-white transition-colors duration-200 p-2 hover:bg-slate-700/50 rounded-lg"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-4 overflow-y-auto" style={{ maxHeight: 'calc(80vh - 200px)' }}>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
              {/* Left Column - Project Selection */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-bold text-white font-vazirmatn">
                    المشروع
                  </h3>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={handleNewProjectNavigation}
                    className="text-indigo-400 hover:text-indigo-300 hover:bg-indigo-500/10 font-vazirmatn"
                  >
                    <Plus className="w-4 h-4 ml-2" />
                    مشروع جديد
                  </Button>
                </div>

                <div className="space-y-4">
                  <div className="relative">
                    <select
                      value={selectedProject?.id || ''}
                      onChange={(e) => {
                        const project = projects.find(p => p.id === e.target.value)
                        if (project) handleProjectSelect(project)
                      }}
                      disabled={loadingProjects}
                      className="w-full bg-slate-700/50 border border-slate-600 rounded-xl px-4 py-4 text-white font-vazirmatn appearance-none text-lg disabled:opacity-50"
                    >
                      <option value="">
                        {loadingProjects ? 'جاري تحميل المشاريع...' : 'اختر مشروعاً موجوداً'}
                      </option>
                      {projects.map(project => (
                        <option key={project.id} value={project.id}>
                          {project.name}
                        </option>
                      ))}
                    </select>
                    <ChevronDown className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5 pointer-events-none" />
                  </div>

                  {selectedProject && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="p-6 bg-gradient-to-r from-slate-700/50 to-slate-800/50 rounded-2xl border border-slate-600/30"
                    >
                      <h4 className="text-xl font-bold text-white font-vazirmatn mb-3">
                        {selectedProject.name}
                      </h4>
                      <p className="text-slate-300 font-vazirmatn mb-4 leading-relaxed">
                        {selectedProject.description}
                      </p>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="flex items-center gap-3">
                          <Building className="w-5 h-5 text-indigo-400" />
                          <div>
                            <span className="text-slate-500 font-vazirmatn text-sm">القطاع</span>
                            <div className="text-slate-200 font-vazirmatn">{selectedProject.industry}</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <DollarSign className="w-5 h-5 text-green-400" />
                          <div>
                            <span className="text-slate-500 font-vazirmatn text-sm">الميزانية</span>
                            <div className="text-slate-200 font-vazirmatn">{selectedProject.budget}</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <Calendar className="w-5 h-5 text-purple-400" />
                          <div>
                            <span className="text-slate-500 font-vazirmatn text-sm">المدة</span>
                            <div className="text-slate-200 font-vazirmatn">{selectedProject.timeline}</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="w-5 h-5 bg-amber-400 rounded-full"></div>
                          <div>
                            <span className="text-slate-500 font-vazirmatn text-sm">الحالة</span>
                            <div className="text-slate-200 font-vazirmatn">{selectedProject.status}</div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}

                  {/* Empty state when no projects exist */}
                  {!loadingProjects && projects.length === 0 && (
                    <div className="p-6 bg-slate-700/20 rounded-xl border border-slate-600/30 text-center">
                      <p className="text-slate-400 font-vazirmatn mb-4">
                        لم تقم بإنشاء أي مشاريع بعد. قم بإنشاء مشروع جديد أولاً لتتمكن من إنشاء المستندات.
                      </p>
                      <Button
                        onClick={handleNewProjectNavigation}
                        className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 font-vazirmatn"
                      >
                        <Plus className="w-4 h-4 ml-2" />
                        إنشاء مشروع جديد
                      </Button>
                    </div>
                  )}

                  {/* Loading state */}
                  {loadingProjects && (
                    <div className="p-6 bg-slate-700/20 rounded-xl border border-slate-600/30 text-center">
                      <div className="w-8 h-8 animate-spin rounded-full border-2 border-indigo-500 border-t-transparent mx-auto mb-4" />
                      <p className="text-slate-400 font-vazirmatn">
                        جاري تحميل المشاريع...
                      </p>
                    </div>
                  )}

                  {/* Always visible Create New Project button when projects exist */}
                  {!loadingProjects && projects.length > 0 && (
                    <div className="mt-4 p-4 bg-slate-700/10 rounded-xl border border-slate-600/20">
                      <div className="text-center">
                        <p className="text-slate-400 font-vazirmatn text-sm mb-3">
                          أو قم بإنشاء مشروع جديد
                        </p>
                        <Button
                          onClick={handleNewProjectNavigation}
                          variant="ghost"
                          className="w-full bg-gradient-to-r from-indigo-500/10 to-purple-500/10 hover:from-indigo-500/20 hover:to-purple-500/20 text-indigo-300 border border-indigo-500/30 font-vazirmatn"
                        >
                          <Plus className="w-4 h-4 ml-2" />
                          إنشاء مشروع جديد
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Right Column - Document Info */}
              <div className="space-y-3">
                <h3 className="text-lg font-bold text-white font-vazirmatn">
                  عنوان المستند
                </h3>
                
                <div className="p-6 bg-gradient-to-r from-slate-700/50 to-slate-800/50 rounded-2xl border border-slate-600/30">
                  <Input
                    value={`${template.title} - ${selectedProject?.name || 'للمشروع الأول'}`}
                    readOnly
                    className="bg-slate-600/50 border-slate-500 text-slate-200 font-vazirmatn text-lg py-3"
                  />
                </div>

                <div className="space-y-2">
                  <h4 className="font-bold text-white font-vazirmatn">
                    ملاحظات عامة لهذا المستند
                  </h4>
                  <div className="p-6 bg-slate-700/30 rounded-2xl border border-slate-600/30">
                    <div className="text-slate-400 font-vazirmatn text-sm space-y-2 mb-4">
                      <p>يمكن إدخال أي ملاحظات أو تعليقات أو سياق إضافي لهذا المستند.</p>
                      <p>يمكن تعديل المحتوى بالكامل بعد إنشاء المستند بنجاح.</p>
                    </div>
                    <Textarea
                      value={documentNotes}
                      onChange={(e) => setDocumentNotes(e.target.value)}
                      placeholder="أدخل أي ملاحظات أو تعليقات أو سياق إضافي لهذا المستند..."
                      rows={4}
                      className="bg-slate-600/50 border-slate-500 text-white placeholder-slate-400 font-vazirmatn"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer - Fixed Position */}
          <div className="p-6 border-t border-slate-700/50 bg-slate-800/30">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  onClick={onBack}
                  className="font-vazirmatn text-slate-400 hover:text-white"
                >
                  <ArrowLeft className="w-4 h-4 ml-2" />
                  السابق
                </Button>
                <div className="text-slate-500 font-vazirmatn text-sm">
                  الخطوة 2 من 2
                </div>
              </div>

              <Button
                onClick={handleCreateDocument}
                disabled={!canCreateDocument() || isCreating}
                className={`font-vazirmatn px-8 py-3 shadow-lg transition-all duration-200 ${
                  selectedTier === 'professional'
                    ? 'bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600'
                    : 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600'
                } ${!canCreateDocument() || isCreating ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {isCreating ? (
                  <div className="flex items-center gap-2">
                    <div className="w-5 h-5 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    {selectedTier === 'professional' ? 'جاري إنشاء النسخة الاحترافية...' : 'جاري إنشاء النسخة السريعة...'}
                  </div>
                ) : (
                  selectedTier === 'professional' ? 'إنشاء المستند الاحترافي' : 'إنشاء المستند السريع'
                )}
              </Button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
