'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useRouter, usePathname } from 'next/navigation'
import { motion } from 'framer-motion'
import {
  BarChart3,
  Folder,
  FileText,
  Settings,
  LogOut,
  User,
  Crown,
  Menu,
  X,
  ScrollText,
  Search,
  Bot,
  Database,
  Compass
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/Button'
import { toast } from 'sonner'

interface SidebarProps {
  className?: string
}

export default function Sidebar({ className = '' }: SidebarProps) {
  const { user, signOut } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const [isMobileOpen, setIsMobileOpen] = useState(false)

  const handleSignOut = async () => {
    try {
      await signOut()
      toast.success('تم تسجيل الخروج بنجاح')
      router.push('/')
    } catch (error) {
      toast.error('حدث خطأ أثناء تسجيل الخروج')
    }
  }

  const navigationSections = [
    {
      title: 'الرئيسية',
      items: [
        {
          name: 'نظرة عامة',
          href: '/dashboard',
          icon: BarChart3,
          active: pathname === '/dashboard',
          disabled: false
        },
        {
          name: 'مشاريعي',
          href: '/dashboard/projects',
          icon: Folder,
          active: pathname === '/dashboard/projects',
          disabled: false
        }
      ]
    },
    {
      title: 'المستندات والعقود',
      items: [
        {
          name: 'مستنداتي',
          href: '/dashboard/documents',
          icon: FileText,
          active: pathname === '/dashboard/documents' || pathname === '/dashboard/discover-documents',
          disabled: false
        },
        {
          name: 'عقودي',
          href: '/dashboard/contracts',
          icon: ScrollText,
          active: pathname === '/dashboard/contracts',
          disabled: false
        }
      ]
    },
    {
      title: 'الاستكشاف والذكاء الاصطناعي',
      items: [
        {
          name: 'اكتشف المستندات',
          href: '/dashboard/discover-documents',
          icon: Search,
          active: pathname === '/dashboard/discover-documents',
          disabled: false
        },
        {
          name: 'اكتشف العقود',
          href: '/dashboard/discover-contracts',
          icon: Compass,
          active: pathname === '/dashboard/discover-contracts',
          disabled: false
        },
        {
          name: 'المساعد الذكي',
          href: '/dashboard/ai-assistant',
          icon: Bot,
          active: pathname === '/dashboard/ai-assistant',
          disabled: false
        },
        {
          name: 'مساحة البيانات',
          href: '/dashboard/Data-space',
          icon: Database,
          active: pathname === '/dashboard/Data-space',
          disabled: false
        }
      ]
    },
    {
      title: 'الإعدادات',
      items: [
        {
          name: 'الإعدادات',
          href: '/dashboard/settings',
          icon: Settings,
          active: pathname === '/dashboard/settings',
          disabled: false
        }
      ]
    }
  ]

  const SidebarContent = () => (
    <div className="flex flex-col h-full bg-gradient-to-b from-slate-800 via-slate-800 to-slate-900 border-r border-slate-700/50 shadow-2xl backdrop-blur-sm">
      {/* Header */}
      <div className="p-6 border-b border-slate-700/50 bg-gradient-to-l from-indigo-500/5 to-violet-500/5">
        <Link href="/" className="flex items-center space-x-2 rtl:space-x-reverse group">
          <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-violet-500 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-indigo-500/25 transition-all duration-300 group-hover:scale-105">
            <span className="text-white font-bold text-sm">BD</span>
          </div>
          <span className="text-xl font-bold text-white font-vazirmatn group-hover:text-indigo-300 transition-colors duration-300">BuildDocwithai</span>
        </Link>
      </div>

      {/* User Profile */}
      <div className="p-6 border-b border-slate-700/50">
        <div className="flex items-center space-x-3 rtl:space-x-reverse p-3 bg-slate-700/30 rounded-xl hover:bg-slate-700/50 transition-all duration-300 group">
          <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-violet-500 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-indigo-500/25 transition-all duration-300 group-hover:scale-105">
            <User className="w-6 h-6 text-white" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-semibold text-white font-vazirmatn truncate group-hover:text-indigo-300 transition-colors duration-300">
              {user?.user_metadata?.name || user?.email?.split('@')[0] || 'المستخدم'}
            </p>
            <p className="text-xs text-slate-400 font-vazirmatn truncate">
              {user?.email}
            </p>
            <div className="flex items-center mt-1">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-2 rtl:ml-2 rtl:mr-0"></div>
              <span className="text-xs text-green-400 font-vazirmatn">متصل</span>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-6 overflow-y-auto">
        {navigationSections.map((section, sectionIndex) => (
          <div key={section.title} className="space-y-2">
            {/* Section Title */}
            <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wider font-vazirmatn px-4 mb-3">
              {section.title}
            </h3>

            {/* Section Items */}
            <div className="space-y-1">
              {section.items.map((item) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.name}
                    href={item.disabled ? '#' : item.href}
                    className={`flex items-center space-x-3 rtl:space-x-reverse px-4 py-3 rounded-xl text-sm font-medium font-vazirmatn transition-all duration-300 group relative overflow-hidden ${
                      item.active
                        ? 'bg-gradient-to-l from-indigo-500 to-violet-500 text-white shadow-lg shadow-indigo-500/25'
                        : item.disabled
                        ? 'text-slate-500 cursor-not-allowed'
                        : 'text-slate-300 hover:text-white hover:bg-slate-700/50 hover:shadow-lg'
                    }`}
                    onClick={(e) => {
                      if (item.disabled) {
                        e.preventDefault()
                      }
                    }}
                  >
                    {item.active && (
                      <div className="absolute inset-0 bg-gradient-to-l from-indigo-400/20 to-violet-400/20 rounded-xl"></div>
                    )}
                    <Icon className={`w-5 h-5 relative z-10 transition-transform duration-300 ${
                      item.active ? 'text-white' : 'group-hover:scale-110'
                    }`} />
                    <span className="relative z-10 flex-1">{item.name}</span>
                    {item.disabled && (
                      <span className="text-xs bg-slate-600/50 text-slate-400 px-2 py-1 rounded-full backdrop-blur-sm relative z-10">
                        قريباً
                      </span>
                    )}
                  </Link>
                )
              })}
            </div>

            {/* Section Separator */}
            {sectionIndex < navigationSections.length - 1 && (
              <div className="pt-4">
                <div className="h-px bg-gradient-to-l from-slate-700 via-slate-600 to-transparent"></div>
              </div>
            )}
          </div>
        ))}
      </nav>

      {/* Subscription Section */}
      <div className="p-4 border-t border-slate-700/50">
        <div className="bg-gradient-to-br from-yellow-500/10 via-orange-500/10 to-red-500/10 border border-yellow-500/20 rounded-xl p-4 space-y-4 backdrop-blur-sm">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <div className="w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center shadow-lg">
              <Crown className="w-4 h-4 text-white" />
            </div>
            <span className="text-sm font-semibold text-white font-vazirmatn">الخطة المجانية</span>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between text-xs text-slate-300 font-vazirmatn">
              <span>3 من 10 مستندات مستخدمة</span>
              <span className="font-semibold text-yellow-400">30%</span>
            </div>
            <div className="w-full bg-slate-700/50 rounded-full h-2.5 overflow-hidden">
              <div className="bg-gradient-to-r from-yellow-500 to-orange-500 h-2.5 rounded-full shadow-sm transition-all duration-500" style={{ width: '30%' }}></div>
            </div>
          </div>

          <Button
            variant="gradient"
            size="sm"
            className="w-full font-vazirmatn text-sm bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 shadow-lg hover:shadow-yellow-500/25 transition-all duration-300"
          >
            ترقية الخطة
          </Button>
        </div>
      </div>

      {/* Sign Out */}
      <div className="p-4 border-t border-slate-700/50">
        <button
          onClick={handleSignOut}
          className="flex items-center space-x-3 rtl:space-x-reverse w-full px-4 py-3 text-sm font-medium text-slate-300 hover:text-red-300 hover:bg-red-500/10 rounded-xl transition-all duration-300 font-vazirmatn group border border-transparent hover:border-red-500/20"
        >
          <LogOut className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
          <span>تسجيل الخروج</span>
        </button>
      </div>
    </div>
  )

  return (
    <>
      {/* Mobile Menu Button */}
      <button
        onClick={() => setIsMobileOpen(true)}
        className="lg:hidden fixed top-4 left-4 z-50 p-2 bg-slate-800 text-white rounded-lg shadow-lg"
      >
        <Menu className="w-6 h-6" />
      </button>

      {/* Desktop Sidebar - Fixed to the right side for RTL */}
      <div className={`hidden lg:flex lg:flex-col lg:w-80 lg:fixed lg:inset-y-0 lg:right-0 lg:z-30 ${className}`}>
        <SidebarContent />
      </div>

      {/* Mobile Sidebar */}
      {isMobileOpen && (
        <div className="lg:hidden fixed inset-0 z-50 flex justify-end">
          <div className="fixed inset-0 bg-black bg-opacity-50" onClick={() => setIsMobileOpen(false)} />
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'tween', duration: 0.3 }}
            className="relative flex flex-col w-80 max-w-xs bg-slate-800 shadow-xl"
          >
            <button
              onClick={() => setIsMobileOpen(false)}
              className="absolute top-4 left-4 p-2 text-slate-400 hover:text-white"
            >
              <X className="w-6 h-6" />
            </button>
            <SidebarContent />
          </motion.div>
        </div>
      )}
    </>
  )
}
