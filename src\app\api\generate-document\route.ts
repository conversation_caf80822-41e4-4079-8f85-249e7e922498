import { NextRequest, NextResponse } from 'next/server'
import { GoogleGenerativeAI } from '@google/generative-ai'
import { DocumentCreationRequest, businessPlanConfig } from '@/types/document-generation'
import { saveGeneratedDocument } from '@/lib/supabase/documents'

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '')

export async function POST(request: NextRequest) {
  console.log('🚀 Document generation API called')

  try {
    const body: DocumentCreationRequest = await request.json()
    const { templateId, tier, projectData, title, description } = body

    console.log('📝 Document generation request:', {
      templateId,
      tier,
      title,
      projectId: projectData.id,
      projectName: projectData.name
    })

    // Get document configuration
    let config = businessPlanConfig
    if (templateId !== 'business-plan') {
      return NextResponse.json(
        { error: 'Template not supported yet' },
        { status: 400 }
      )
    }

    // Get sections based on tier
    const sections = config.sections[tier]
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' })

    // Generate content for each section
    const generatedSections = []

    // For testing purposes, use mock content instead of AI generation to avoid quota issues
    const useMockContent = true // Set to false when AI quota is available

    for (const section of sections) {
      try {
        let content = ''

        if (useMockContent) {
          // Generate mock content for testing
          console.log(`📝 Using mock content for section: ${section.title}`)
          content = `هذا محتوى تجريبي لقسم "${section.title}"

يهدف هذا القسم إلى تقديم معلومات شاملة حول ${section.title} في إطار مشروع ${projectData.name}.

## النقاط الرئيسية:

1. **التحليل الأساسي**: يتضمن هذا القسم تحليلاً مفصلاً للعوامل المؤثرة على ${section.title} في مجال ${projectData.industry}.

2. **البيانات والإحصائيات**:
   - نسبة النمو المتوقعة: 25% سنوياً
   - حجم السوق المستهدف: ${projectData.target_market}
   - الميزانية المخصصة: ${projectData.budget}

3. **التوصيات الاستراتيجية**: بناءً على التحليل المفصل، نوصي بتطبيق استراتيجيات محددة لتحقيق الأهداف المرجوة.

4. **الخطوات التنفيذية**:
   - المرحلة الأولى: التخطيط والإعداد
   - المرحلة الثانية: التنفيذ والمتابعة
   - المرحلة الثالثة: التقييم والتطوير

## الخلاصة:
يُعتبر ${section.title} عنصراً أساسياً في نجاح مشروع ${projectData.name}، ويتطلب تخطيطاً دقيقاً وتنفيذاً محكماً لضمان تحقيق الأهداف المرجوة.

---
*تم إنشاء هذا المحتوى باستخدام نظام BuildDocwithai المتطور*`
        } else {
          // Use AI generation (when quota is available)
          const tierPrompts = config.prompts[tier]
          const sectionPrompt = tierPrompts.sectionPrompts[section.id]

          const prompt = `
${tierPrompts.systemPrompt}

معلومات المشروع:
- اسم المشروع: ${projectData.name}
- وصف المشروع: ${projectData.description}
- الصناعة: ${projectData.industry}
- السوق المستهدف: ${projectData.target_market}
- الميزانية: ${projectData.budget}
- الجدول الزمني: ${projectData.timeline || 'غير محدد'}
- الموقع: ${projectData.location || 'غير محدد'}
- العملاء المستهدفون: ${projectData.target_customers || 'غير محدد'}
- المشكلة التي يحلها المشروع: ${projectData.customer_problem || 'غير محدد'}
- الحل المقترح: ${projectData.solution || 'غير محدد'}
- الموقع الإلكتروني: ${projectData.website || 'غير متوفر'}

المطلوب:
${sectionPrompt}

يجب أن يكون المحتوى:
1. مكتوباً باللغة العربية الفصحى
2. احترافياً ومفصلاً (على الأقل 500 كلمة)
3. يحتوي على تحليل عميق ومعلومات قيمة
4. منظماً بعناوين فرعية وفقرات واضحة
5. يتضمن أرقاماً وإحصائيات واقعية عند الإمكان
6. مناسباً للعرض على المستثمرين والجهات التمويلية

اكتب المحتوى مباشرة دون مقدمات أو خاتمات إضافية.
`

          const result = await model.generateContent(prompt)
          const response = await result.response
          content = response.text()
        }

        generatedSections.push({
          id: section.id,
          title: section.title,
          content: content,
          isLocked: section.isLocked,
          order: section.order,
          status: 'completed' as const,
          estimatedTime: section.estimatedTime,
          actualTime: section.estimatedTime // Mock actual time
        })

        // Add delay to simulate real generation time
        await new Promise(resolve => setTimeout(resolve, 500))

      } catch (error) {
        console.error(`Error generating section ${section.id}:`, error)
        generatedSections.push({
          id: section.id,
          title: section.title,
          content: `حدث خطأ في توليد محتوى هذا القسم. يرجى المحاولة مرة أخرى.

الخطأ: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`,
          isLocked: section.isLocked,
          order: section.order,
          status: 'error' as const,
          estimatedTime: section.estimatedTime,
          error: 'Generation failed'
        })
      }
    }

    // Create document object
    const document = {
      id: `doc_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      title,
      description,
      sections: generatedSections,
      tier,
      templateId,
      projectId: projectData.id,
      status: 'completed' as const,
      progress: 100,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      metadata: {
        totalSections: sections.length,
        completedSections: generatedSections.filter(s => s.status === 'completed').length,
        estimatedTotalTime: sections.reduce((acc, s) => acc + s.estimatedTime, 0),
        actualTotalTime: generatedSections.reduce((acc, s) => acc + (s.actualTime || 0), 0)
      }
    }

    console.log('💾 Attempting to save document to database:', {
      documentId: document.id,
      sectionsCount: document.sections.length,
      status: document.status
    })

    // Save the document to the database
    const { data: savedDocument, error: saveError } = await saveGeneratedDocument(document)

    if (saveError) {
      console.error('❌ Error saving document to database:', saveError)
      // Still return the document even if saving fails
      return NextResponse.json({
        document,
        warning: 'Document generated but not saved to database',
        saveError: saveError.message || 'Unknown save error'
      })
    }

    console.log('✅ Document successfully saved to database:', {
      documentId: document.id,
      savedDocumentId: savedDocument?.id
    })

    return NextResponse.json({ document })

  } catch (error) {
    console.error('Error generating document:', error)
    return NextResponse.json(
      { error: 'Failed to generate document' },
      { status: 500 }
    )
  }
}

// Stream endpoint for real-time generation
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const documentId = searchParams.get('documentId')
  
  if (!documentId) {
    return NextResponse.json({ error: 'Document ID required' }, { status: 400 })
  }

  // Create a readable stream for real-time updates
  const encoder = new TextEncoder()
  
  const stream = new ReadableStream({
    start(controller) {
      // Mock progress updates
      let progress = 0
      let currentSection = 0
      const totalSections = 6 // Mock for business plan quick
      
      const interval = setInterval(() => {
        progress += Math.random() * 15
        
        if (progress >= 100) {
          progress = 100
          controller.enqueue(encoder.encode(`data: ${JSON.stringify({
            documentId,
            progress: 100,
            status: 'completed',
            currentSection: totalSections,
            totalSections
          })}\n\n`))
          controller.close()
          clearInterval(interval)
          return
        }

        if (progress > (currentSection + 1) * (100 / totalSections)) {
          currentSection++
        }

        controller.enqueue(encoder.encode(`data: ${JSON.stringify({
          documentId,
          progress: Math.round(progress),
          status: 'generating',
          currentSection,
          totalSections,
          estimatedTimeRemaining: Math.round((100 - progress) * 2) // Mock time
        })}\n\n`))
      }, 2000)
    }
  })

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  })
}
