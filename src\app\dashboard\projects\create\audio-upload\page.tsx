'use client'

import { useState, useRef } from 'react'
import { motion } from 'framer-motion'
import { Upload, Music, X, Play, Pause, RotateCcw, CheckCircle } from 'lucide-react'
import { toast } from 'sonner'
import ProjectInfoChecklist from '@/components/dashboard/ProjectInfoChecklist'
import Breadcrumb from '@/components/dashboard/Breadcrumb'
import ProgressBar from '@/components/ui/ProgressBar'
import { Button } from '@/components/ui/Button'
import { PROJECT_CHECKLIST_ITEMS, UploadedFile } from '@/lib/project-creation-types'

export default function AudioUploadPage() {
  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [dragActive, setDragActive] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)
  const audioRef = useRef<HTMLAudioElement>(null)

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    const files = e.dataTransfer.files
    if (files && files[0]) {
      handleFileUpload(files[0])
    }
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files[0]) {
      handleFileUpload(files[0])
    }
  }

  const handleFileUpload = async (file: File) => {
    // Validate file type
    if (!file.type.startsWith('audio/')) {
      toast.error('يرجى رفع ملف صوتي صالح')
      return
    }

    // Validate file size (max 100MB)
    if (file.size > 100 * 1024 * 1024) {
      toast.error('حجم الملف كبير جداً. الحد الأقصى 100 ميجابايت')
      return
    }

    setIsUploading(true)
    setUploadProgress(0)

    try {
      // Simulate upload progress
      const uploadedFileData: UploadedFile = {
        name: file.name,
        size: file.size,
        type: file.type,
        url: URL.createObjectURL(file)
      }

      // Simulate progressive upload with progress updates
      for (let i = 0; i <= 100; i += 10) {
        setUploadProgress(i)
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      setUploadedFile(uploadedFileData)
      toast.success('تم رفع الملف الصوتي بنجاح')
    } catch (error) {
      toast.error('فشل في رفع الملف. يرجى المحاولة مرة أخرى')
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
    }
  }

  const removeFile = () => {
    if (uploadedFile?.url) {
      URL.revokeObjectURL(uploadedFile.url)
    }
    setUploadedFile(null)
    setIsPlaying(false)
    setCurrentTime(0)
    setDuration(0)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const togglePlayPause = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause()
      } else {
        audioRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  const resetAudio = () => {
    if (audioRef.current) {
      audioRef.current.currentTime = 0
      setCurrentTime(0)
      setIsPlaying(false)
    }
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 بايت'
    const k = 1024
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleCreateProject = async () => {
    if (!uploadedFile) {
      toast.error('يرجى رفع ملف صوتي أولاً')
      return
    }

    try {
      // Simulate project creation
      toast.success('تم إنشاء المشروع بنجاح!')
      // Redirect to projects list or project details
    } catch (error) {
      toast.error('فشل في إنشاء المشروع. يرجى المحاولة مرة أخرى')
    }
  }

  const breadcrumbItems = [
    { label: 'لوحة التحكم', href: '/dashboard' },
    { label: 'المشاريع', href: '/dashboard/projects' },
    { label: 'إنشاء مشروع', href: '/dashboard/projects/create' },
    { label: 'رفع ملف صوتي' }
  ]

  return (
    <div className="space-y-8">
      {/* Breadcrumb */}
      <Breadcrumb items={breadcrumbItems} />

      {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-3xl font-bold text-white font-vazirmatn mb-2">
            رفع ملف صوتي
          </h1>
          <p className="text-slate-400 font-vazirmatn">
            قم بتحميل مستند صوتي يحتوي على معلومات مشروعك وتأكد أنه يحتوي على النقاط التالية
          </p>
        </motion.div>

        {/* Upload Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8"
        >
          <h2 className="text-xl font-semibold text-white font-vazirmatn mb-6">
            رفع المحتوى
          </h2>

          {!uploadedFile ? (
            <div
              className={`border-2 border-dashed rounded-2xl p-12 text-center transition-all duration-300 ${
                dragActive
                  ? 'border-indigo-500 bg-indigo-500/10'
                  : 'border-slate-600 hover:border-slate-500'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <div className="flex flex-col items-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-2xl flex items-center justify-center">
                  <Music className="w-8 h-8 text-white" />
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white font-vazirmatn mb-2">
                    رفع مستند المشروع
                  </h3>
                  <p className="text-slate-400 font-vazirmatn mb-4">
                    ارفع ملف صوتي يحتوي على معلومات مشروعك
                  </p>
                </div>

                <Button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploading}
                  className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600"
                >
                  <Upload className="w-4 h-4 ml-2" />
                  {isUploading ? 'جاري الرفع...' : 'اختر ملف صوتي'}
                </Button>

                {isUploading && (
                  <div className="w-full max-w-md">
                    <ProgressBar progress={uploadProgress} />
                  </div>
                )}

                <p className="text-xs text-slate-500 font-vazirmatn">
                  نوع الملف المقبول: MP3 أو أي ملف صوتي (حتى 100 ميجابايت)
                </p>
              </div>

              <input
                ref={fileInputRef}
                type="file"
                accept="audio/*"
                onChange={handleFileSelect}
                className="hidden"
              />
            </div>
          ) : (
            <div className="space-y-6">
              {/* File Info */}
              <div className="bg-slate-700/30 rounded-xl p-6 border border-slate-600/30">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                      <CheckCircle className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-white font-vazirmatn">
                        {uploadedFile.name}
                      </h4>
                      <p className="text-sm text-slate-400 font-vazirmatn">
                        {formatFileSize(uploadedFile.size)}
                      </p>
                    </div>
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={removeFile}
                    className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>

                {/* Audio Player */}
                {uploadedFile.url && (
                  <div className="space-y-4">
                    <audio
                      ref={audioRef}
                      src={uploadedFile.url}
                      onLoadedMetadata={() => {
                        if (audioRef.current) {
                          setDuration(audioRef.current.duration)
                        }
                      }}
                      onTimeUpdate={() => {
                        if (audioRef.current) {
                          setCurrentTime(audioRef.current.currentTime)
                        }
                      }}
                      onEnded={() => setIsPlaying(false)}
                    />

                    <div className="flex items-center space-x-4 rtl:space-x-reverse">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={togglePlayPause}
                        className="text-indigo-400 hover:text-indigo-300"
                      >
                        {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                      </Button>

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={resetAudio}
                        className="text-slate-400 hover:text-slate-300"
                      >
                        <RotateCcw className="w-4 h-4" />
                      </Button>

                      <div className="flex-1">
                        <div className="flex items-center justify-between text-xs text-slate-400 font-vazirmatn mb-1">
                          <span>{formatTime(currentTime)}</span>
                          <span>{formatTime(duration)}</span>
                        </div>
                        <div className="w-full bg-slate-600 rounded-full h-2">
                          <div
                            className="bg-gradient-to-r from-indigo-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Create Project Button */}
              <div className="flex justify-end">
                <Button
                  onClick={handleCreateProject}
                  className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600"
                >
                  إنشاء المشروع
                </Button>
              </div>
            </div>
          )}
        </motion.div>

        {/* Project Information Checklist */}
        <ProjectInfoChecklist
          items={PROJECT_CHECKLIST_ITEMS}
          completedCount={0}
        />
      </div>
  )
}
