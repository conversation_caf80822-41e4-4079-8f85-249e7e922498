'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/Button'
import { User, Settings, LogOut, ChevronDown } from 'lucide-react'

export default function UserDropdown() {
  const [isOpen, setIsOpen] = useState(false)
  const { user, signOut } = useAuth()

  if (!user) return null

  const handleSignOut = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 rtl:space-x-reverse text-white hover:text-indigo-400 transition-colors duration-200"
      >
        <div className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center">
          <User className="w-4 h-4" />
        </div>
        <span className="hidden md:block text-sm font-medium">
          {user.user_metadata?.name || user.email?.split('@')[0] || 'المستخدم'}
        </span>
        <ChevronDown className="w-4 h-4" />
      </button>

      {isOpen && (
        <div className="absolute top-full mt-2 right-0 rtl:right-auto rtl:left-0 bg-slate-800 border border-slate-700 rounded-lg shadow-xl py-2 min-w-[200px] z-50">
          <div className="px-4 py-2 border-b border-slate-700">
            <p className="text-sm font-medium text-white">
              {user.user_metadata?.name || 'المستخدم'}
            </p>
            <p className="text-xs text-slate-400">{user.email}</p>
          </div>
          
          <Link
            href="/profile"
            className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 text-sm text-slate-300 hover:text-white hover:bg-slate-700 transition-colors duration-200"
            onClick={() => setIsOpen(false)}
          >
            <Settings className="w-4 h-4" />
            <span>الإعدادات</span>
          </Link>
          
          <button
            onClick={handleSignOut}
            className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 text-sm text-slate-300 hover:text-white hover:bg-slate-700 transition-colors duration-200 w-full text-right rtl:text-right"
          >
            <LogOut className="w-4 h-4" />
            <span>تسجيل الخروج</span>
          </button>
        </div>
      )}
    </div>
  )
}
