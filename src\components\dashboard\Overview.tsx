'use client'

import { motion } from 'framer-motion'
import { Plus, Crown, TrendingUp, BarChart3 } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/Button'
import ActivityCard from './ActivityCard'
import RecentActivity from './RecentActivity'
import DocumentsTable from './DocumentsTable'
import RecommendationCard from './RecommendationCard'
import {
  getActivitySummary,
  getRecentActivity,
  getLatestDocuments,
  getRecommendations,
  getUserStats
} from '@/lib/dashboard-data'

export default function Overview() {
  const { user } = useAuth()
  const activitySummary = getActivitySummary()
  const recentActivity = getRecentActivity()
  const latestDocuments = getLatestDocuments()
  const recommendations = getRecommendations()
  const userStats = getUserStats()

  const userName = user?.user_metadata?.name || user?.email?.split('@')[0] || 'المستخدم'

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="relative bg-gradient-to-br from-indigo-500/10 via-purple-500/10 to-pink-500/10 border border-indigo-500/20 rounded-2xl p-8 overflow-hidden backdrop-blur-sm"
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5 opacity-50"></div>
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-2xl"></div>

        <div className="relative z-10 flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold text-white font-vazirmatn mb-3 bg-gradient-to-l from-white to-indigo-200 bg-clip-text text-transparent">
              مرحباً، {userName}
            </h1>
            <p className="text-slate-300 font-vazirmatn text-lg leading-relaxed">
              لقد أكملت <span className="text-indigo-400 font-semibold">{userStats.documentsThisWeek}</span> مستندات هذا الأسبوع. استمر في التقدم!
            </p>
          </div>
          <div className="hidden md:flex items-center space-x-6 rtl:space-x-reverse">
            <div className="text-center p-4 bg-white/5 rounded-xl backdrop-blur-sm border border-white/10">
              <div className="text-3xl font-bold text-white mb-1">{userStats.totalDocuments}</div>
              <div className="text-sm text-slate-400 font-vazirmatn">إجمالي المستندات</div>
            </div>
            <div className="text-center p-4 bg-white/5 rounded-xl backdrop-blur-sm border border-white/10">
              <div className="text-3xl font-bold text-white mb-1">{userStats.activeProjects}</div>
              <div className="text-sm text-slate-400 font-vazirmatn">مشاريع نشطة</div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Primary CTA */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="flex justify-center"
      >
        <Button
          variant="gradient"
          size="xl"
          className="font-vazirmatn text-lg px-10 py-5 shadow-2xl shadow-indigo-500/30 hover:shadow-indigo-500/50 transition-all duration-300 hover:scale-105 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 hover:from-indigo-600 hover:via-purple-600 hover:to-pink-600 border border-indigo-400/20 hover:border-indigo-400/40"
        >
          <Plus className="w-6 h-6 ml-2 rtl:mr-2 rtl:ml-0" />
          إنشاء مستند جديد
        </Button>
      </motion.div>

      {/* Activity Summary Cards */}
      <div>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="flex items-center space-x-3 rtl:space-x-reverse mb-8"
        >
          <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
            <BarChart3 className="w-4 h-4 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-white font-vazirmatn">
            ملخص النشاط
          </h2>
          <div className="flex-1 h-px bg-gradient-to-l from-slate-700 to-transparent"></div>
        </motion.div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {activitySummary.map((activity, index) => (
            <ActivityCard
              key={activity.id}
              activity={activity}
              index={index}
            />
          ))}
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Recent Activity */}
        <div className="lg:col-span-1">
          <RecentActivity activities={recentActivity} />
        </div>

        {/* Right Column - Documents and Recommendations */}
        <div className="lg:col-span-2 space-y-8">
          {/* Latest Documents */}
          <DocumentsTable documents={latestDocuments} />

          {/* AI Recommendations */}
          <RecommendationCard recommendations={recommendations} />
        </div>
      </div>

      {/* Subscription Status Widget */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="relative bg-gradient-to-br from-yellow-500/10 via-orange-500/10 to-red-500/10 border border-yellow-500/20 rounded-2xl p-8 overflow-hidden backdrop-blur-sm"
      >
        {/* Background Pattern */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-yellow-500/20 to-orange-500/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-br from-orange-500/20 to-red-500/20 rounded-full blur-2xl"></div>

        <div className="relative z-10 flex items-center justify-between">
          <div className="flex items-center space-x-6 rtl:space-x-reverse">
            <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-2xl flex items-center justify-center shadow-2xl shadow-yellow-500/25">
              <Crown className="w-8 h-8 text-white" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-white font-vazirmatn mb-2">
                الخطة المجانية
              </h3>
              <p className="text-slate-300 font-vazirmatn">
                <span className="text-yellow-400 font-semibold">{userStats.planUsage.used}</span> من {userStats.planUsage.total} مستندات مستخدمة
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-6 rtl:space-x-reverse">
            <div className="text-center hidden sm:block">
              <div className="w-20 h-20 relative">
                <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                  <path
                    className="text-slate-700"
                    stroke="currentColor"
                    strokeWidth="2"
                    fill="none"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                  <path
                    className="text-yellow-500"
                    stroke="currentColor"
                    strokeWidth="3"
                    strokeDasharray={`${userStats.planUsage.percentage}, 100`}
                    strokeLinecap="round"
                    fill="none"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-lg font-bold text-white">{userStats.planUsage.percentage}%</span>
                </div>
              </div>
            </div>

            <Button
              variant="gradient"
              size="lg"
              className="font-vazirmatn bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 shadow-xl shadow-yellow-500/25 hover:shadow-yellow-500/40 transition-all duration-300 hover:scale-105 px-8 py-4"
            >
              <TrendingUp className="w-5 h-5 ml-2 rtl:mr-2 rtl:ml-0" />
              ترقية الخطة
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
