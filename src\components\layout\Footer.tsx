'use client'

import Link from 'next/link'
import { Facebook, Twitter, Linkedin, Instagram } from 'lucide-react'

export default function Footer() {
  const productLinks = [
    { name: 'المميزات', href: '#features' },
    { name: 'القوالب', href: '#templates' },
    { name: 'الأسعار', href: '#pricing' },
    { name: 'التكاملات', href: '#integrations' },
  ]

  const companyLinks = [
    { name: 'من نحن', href: '/about' },
    { name: 'الوظائف', href: '/careers' },
    { name: 'تواصل معنا', href: '/contact' },
    { name: 'المدونة', href: '/blog' },
  ]

  const supportLinks = [
    { name: 'مركز المساعدة', href: '/help' },
    { name: 'التوثيق', href: '/docs' },
    { name: 'المجتمع', href: '/community' },
    { name: 'حالة الخدمة', href: '/status' },
  ]

  const legalLinks = [
    { name: 'سياسة الخصوصية', href: '/privacy' },
    { name: 'شروط الاستخدام', href: '/terms' },
    { name: 'سياسة ملفات تعريف الارتباط', href: '/cookies' },
  ]

  const socialLinks = [
    { name: 'تويتر', href: '#', icon: Twitter },
    { name: 'لينكد إن', href: '#', icon: Linkedin },
    { name: 'فيسبوك', href: '#', icon: Facebook },
    { name: 'إنستغرام', href: '#', icon: Instagram },
  ]

  return (
    <footer className="bg-slate-900 border-t border-slate-800">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand */}
          <div className="lg:col-span-2">
            <Link href="/" className="flex items-center space-x-2 rtl:space-x-reverse mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-violet-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">BD</span>
              </div>
              <span className="text-xl font-bold text-white">BuildDocwithai</span>
            </Link>

            <p className="text-slate-400 mb-6 max-w-md leading-relaxed">
              منصة BuildDocwithai تساعدك في إنشاء مستندات عملك الاحترافية بالذكاء الاصطناعي
            </p>
            
            {/* Social Links */}
            <div className="flex space-x-4 rtl:space-x-reverse">
              {socialLinks.map((social) => {
                const Icon = social.icon
                return (
                  <a
                    key={social.name}
                    href={social.href}
                    className="text-slate-400 hover:text-white transition-colors duration-200"
                    aria-label={social.name}
                  >
                    <Icon className="w-5 h-5" />
                  </a>
                )
              })}
            </div>
          </div>

          {/* Product */}
          <div>
            <h3 className="text-white font-semibold mb-4">المنتج</h3>
            <ul className="space-y-3">
              {productLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-slate-400 hover:text-white transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="text-white font-semibold mb-4">الشركة</h3>
            <ul className="space-y-3">
              {companyLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-slate-400 hover:text-white transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-white font-semibold mb-4">الدعم</h3>
            <ul className="space-y-3">
              {supportLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-slate-400 hover:text-white transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom */}
        <div className="border-t border-slate-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-slate-400 text-sm">
              © 2024 BuildDocwithai. جميع الحقوق محفوظة.
            </p>
            
            <div className="flex space-x-6 rtl:space-x-reverse">
              {legalLinks.map((link) => (
                <Link
                  key={link.name}
                  href={link.href}
                  className="text-slate-400 hover:text-white transition-colors duration-200 text-sm"
                >
                  {link.name}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
