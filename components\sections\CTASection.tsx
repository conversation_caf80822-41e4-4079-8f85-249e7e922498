'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { ArrowLeft, Check, Phone } from 'lucide-react'
import { motion } from 'framer-motion'

export default function CTASection() {
  const features = [
    'تجربة مجانية لمدة 14 يوم',
    'لا حاجة لبطاقة ائتمانية',
    'دعم فني على مدار الساعة',
  ]

  return (
    <section className="section-padding bg-gradient-to-br from-indigo-900 via-violet-900 to-slate-900 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-indigo-500/10 to-violet-500/10"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-indigo-500/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-violet-500/20 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center max-w-4xl mx-auto"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight">
            ابدأ رحلتك نحو النجاح اليوم
          </h2>

          <p className="text-xl text-slate-300 mb-8 leading-relaxed">
            انضم إلى آلاف رواد الأعمال الذين يثقون في BuildDocwithai لإنشاء مستنداتهم الاحترافية
          </p>

          {/* Features list */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-8 rtl:sm:space-x-reverse mb-10"
          >
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-center space-x-2 rtl:space-x-reverse text-slate-300"
              >
                <div className="w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <Check className="w-3 h-3 text-white" />
                </div>
                <span className="text-sm font-medium">{feature}</span>
              </motion.div>
            ))}
          </motion.div>

          {/* CTA buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12"
          >
            <Link href="/signup">
              <Button variant="gradient" size="xl" className="group shadow-2xl shadow-indigo-500/25">
                ابدأ مجاناً الآن
                <ArrowLeft className="w-5 h-5 mr-2 group-hover:translate-x-1 transition-transform" />
              </Button>
            </Link>

            <Button variant="secondary" size="xl" className="group">
              <Phone className="w-5 h-5 mr-2 rtl:ml-2 rtl:mr-0 group-hover:scale-110 transition-transform" />
              تحدث مع فريق المبيعات
            </Button>
          </motion.div>

          {/* Trust indicators */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 pt-8 border-t border-white/10"
          >
            <div className="text-center">
              <div className="text-3xl mb-2">🚀</div>
              <div className="text-white font-semibold mb-1">إطلاق سريع</div>
              <div className="text-slate-400 text-sm">ابدأ في أقل من دقيقتين</div>
            </div>
            <div className="text-center">
              <div className="text-3xl mb-2">🔒</div>
              <div className="text-white font-semibold mb-1">أمان مضمون</div>
              <div className="text-slate-400 text-sm">حماية بيانات متقدمة</div>
            </div>
            <div className="text-center">
              <div className="text-3xl mb-2">💎</div>
              <div className="text-white font-semibold mb-1">جودة عالية</div>
              <div className="text-slate-400 text-sm">مستندات احترافية دائماً</div>
            </div>
          </motion.div>
        </motion.div>

        {/* Bottom decoration */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1, delay: 0.8 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 border border-white/20">
            <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
            <span className="text-slate-300 text-sm font-medium">
              أكثر من 1000 شخص انضم هذا الشهر
            </span>
          </div>
        </motion.div>
      </div>

      {/* Floating elements */}
      <motion.div
        animate={{ 
          y: [-20, 20, -20],
          rotate: [0, 5, 0, -5, 0]
        }}
        transition={{ 
          duration: 6, 
          repeat: Infinity, 
          ease: "easeInOut" 
        }}
        className="absolute top-20 left-10 w-16 h-16 bg-gradient-to-r from-indigo-500/20 to-violet-500/20 rounded-full blur-xl"
      ></motion.div>

      <motion.div
        animate={{ 
          y: [20, -20, 20],
          rotate: [0, -5, 0, 5, 0]
        }}
        transition={{ 
          duration: 8, 
          repeat: Infinity, 
          ease: "easeInOut" 
        }}
        className="absolute bottom-20 right-10 w-20 h-20 bg-gradient-to-r from-violet-500/20 to-indigo-500/20 rounded-full blur-xl"
      ></motion.div>
    </section>
  )
}
