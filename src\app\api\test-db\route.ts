import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET() {
  try {
    // Test database connection
    const { data, error } = await supabase
      .from('generated_documents')
      .select('id')
      .limit(1)

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json({
        success: false,
        error: error.message,
        message: 'Database table might not exist. Please run the migration.'
      })
    }

    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      tableExists: true
    })

  } catch (error) {
    console.error('Connection error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to connect to database'
    })
  }
}
