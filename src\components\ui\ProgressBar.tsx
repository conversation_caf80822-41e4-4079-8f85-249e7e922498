'use client'

import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface ProgressBarProps {
  progress: number
  className?: string
  showPercentage?: boolean
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'success' | 'warning' | 'error'
}

export default function ProgressBar({
  progress,
  className,
  showPercentage = true,
  size = 'md',
  variant = 'default'
}: ProgressBarProps) {
  const sizeClasses = {
    sm: 'h-2',
    md: 'h-3',
    lg: 'h-4'
  }

  const variantClasses = {
    default: 'from-indigo-500 to-purple-500',
    success: 'from-green-500 to-emerald-500',
    warning: 'from-amber-500 to-orange-500',
    error: 'from-red-500 to-pink-500'
  }

  const clampedProgress = Math.min(Math.max(progress, 0), 100)

  return (
    <div className={cn('space-y-2', className)}>
      {showPercentage && (
        <div className="flex items-center justify-between">
          <span className="text-sm text-slate-300 font-vazirmatn">
            جاري الرفع...
          </span>
          <span className="text-sm font-medium text-white font-vazirmatn">
            {Math.round(clampedProgress)}%
          </span>
        </div>
      )}
      
      <div className={cn(
        'w-full bg-slate-700 rounded-full overflow-hidden',
        sizeClasses[size]
      )}>
        <motion.div
          className={cn(
            'h-full bg-gradient-to-r rounded-full transition-all duration-300',
            variantClasses[variant]
          )}
          initial={{ width: 0 }}
          animate={{ width: `${clampedProgress}%` }}
          transition={{ duration: 0.5, ease: 'easeInOut' }}
        />
      </div>
    </div>
  )
}
