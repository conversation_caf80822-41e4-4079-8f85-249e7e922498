import { NextRequest, NextResponse } from 'next/server'
import { getUserDocuments } from '@/lib/supabase/documents'

// GET /api/documents - Get all documents for the current user
export async function GET(request: NextRequest) {
  try {
    const { data: documents, error } = await getUserDocuments()

    if (error) {
      if (error.message === 'User not authenticated') {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        )
      }

      console.error('Error fetching user documents:', error)
      return NextResponse.json(
        { error: 'Failed to fetch documents' },
        { status: 500 }
      )
    }

    return NextResponse.json({ documents: documents || [] })

  } catch (error) {
    console.error('Error in GET /api/documents:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
