import { supabase } from '@/lib/supabase'
import { Project, ProjectFounder, CreateProjectData, UpdateProjectData } from '@/types/database'

// Get all projects for the authenticated user
export async function getProjects(): Promise<{ data: Project[] | null; error: any }> {
  try {
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*')
      .order('created_at', { ascending: false })

    if (projectsError) {
      console.error('Error fetching projects:', projectsError)
      return { data: null, error: projectsError }
    }

    // Fetch founders for each project
    const projectsWithFounders = await Promise.all(
      (projects || []).map(async (project) => {
        const { data: founders } = await supabase
          .from('project_founders')
          .select('*')
          .eq('project_id', project.id)
          .order('created_at', { ascending: true })

        return {
          ...project,
          founders: founders || []
        }
      })
    )

    return { data: projectsWithFounders, error: null }
  } catch (error) {
    console.error('Error in getProjects:', error)
    return { data: null, error }
  }
}

// Get a single project by ID
export async function getProject(id: string): Promise<{ data: Project | null; error: any }> {
  try {
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('*')
      .eq('id', id)
      .single()

    if (projectError) {
      console.error('Error fetching project:', projectError)
      return { data: null, error: projectError }
    }

    // Fetch founders for the project
    const { data: founders } = await supabase
      .from('project_founders')
      .select('*')
      .eq('project_id', id)
      .order('created_at', { ascending: true })

    const projectWithFounders = {
      ...project,
      founders: founders || []
    }

    return { data: projectWithFounders, error: null }
  } catch (error) {
    console.error('Error in getProject:', error)
    return { data: null, error }
  }
}

// Create a new project
export async function createProject(projectData: CreateProjectData): Promise<{ data: Project | null; error: any }> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return { data: null, error: { message: 'User not authenticated' } }
    }

    // Create the project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .insert({
        user_id: user.id,
        name: projectData.name,
        description: projectData.description,
        profile: projectData.profile,
        website: projectData.website,
        location: projectData.location,
        target_customers: projectData.target_customers,
        customer_problem: projectData.customer_problem,
        solution: projectData.solution,
        industry: projectData.industry,
        target_market: projectData.target_market,
        budget: projectData.budget,
        timeline: projectData.timeline,
        status: projectData.status || 'draft'
      })
      .select()
      .single()

    if (projectError) {
      console.error('Error creating project:', projectError)
      return { data: null, error: projectError }
    }

    // Create founders if provided
    let founders: ProjectFounder[] = []
    if (projectData.founders && projectData.founders.length > 0) {
      const foundersData = projectData.founders.map(founder => ({
        project_id: project.id,
        name: founder.name,
        ownership_percentage: founder.ownership_percentage
      }))

      const { data: createdFounders, error: foundersError } = await supabase
        .from('project_founders')
        .insert(foundersData)
        .select()

      if (foundersError) {
        console.error('Error creating founders:', foundersError)
        // Don't fail the entire operation, just log the error
      } else {
        founders = createdFounders || []
      }
    }

    return { 
      data: { ...project, founders }, 
      error: null 
    }
  } catch (error) {
    console.error('Error in createProject:', error)
    return { data: null, error }
  }
}

// Update an existing project
export async function updateProject(projectData: UpdateProjectData): Promise<{ data: Project | null; error: any }> {
  try {
    const { id, founders, ...updateData } = projectData

    // Update the project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (projectError) {
      console.error('Error updating project:', projectError)
      return { data: null, error: projectError }
    }

    // Update founders if provided
    let updatedFounders: ProjectFounder[] = []
    if (founders) {
      // Delete existing founders
      await supabase
        .from('project_founders')
        .delete()
        .eq('project_id', id)

      // Insert new founders
      if (founders.length > 0) {
        const foundersData = founders.map(founder => ({
          project_id: id,
          name: founder.name,
          ownership_percentage: founder.ownership_percentage
        }))

        const { data: createdFounders, error: foundersError } = await supabase
          .from('project_founders')
          .insert(foundersData)
          .select()

        if (foundersError) {
          console.error('Error updating founders:', foundersError)
        } else {
          updatedFounders = createdFounders || []
        }
      }
    } else {
      // Fetch existing founders
      const { data: existingFounders } = await supabase
        .from('project_founders')
        .select('*')
        .eq('project_id', id)
        .order('created_at', { ascending: true })

      updatedFounders = existingFounders || []
    }

    return { 
      data: { ...project, founders: updatedFounders }, 
      error: null 
    }
  } catch (error) {
    console.error('Error in updateProject:', error)
    return { data: null, error }
  }
}

// Delete a project
export async function deleteProject(id: string): Promise<{ error: any }> {
  try {
    // Delete founders first (cascade should handle this, but being explicit)
    await supabase
      .from('project_founders')
      .delete()
      .eq('project_id', id)

    // Delete the project
    const { error: projectError } = await supabase
      .from('projects')
      .delete()
      .eq('id', id)

    if (projectError) {
      console.error('Error deleting project:', projectError)
      return { error: projectError }
    }

    return { error: null }
  } catch (error) {
    console.error('Error in deleteProject:', error)
    return { error }
  }
}
