
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

// Environment variables validation
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env.local file.');
}

// Supabase client for client-side operations
export const supabase = createClientComponentClient({
  supabaseUrl,
  supabaseKey: supabaseAnonKey
});

// Auth helper functions
export const authHelpers = {
  signUp: async (email: string, password: string) => {
    const redirectUrl = `${window.location.origin}/`;

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectUrl
      }
    });

    return { data, error };
  },

  signIn: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    return { data, error };
  },

  signInWithGoogle: async () => {
    const redirectUrl = `${window.location.origin}/`;

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: redirectUrl,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
        skipBrowserRedirect: false // We'll handle popup manually
      }
    });

    return { data, error };
  },

  signInWithGooglePopup: async () => {
    return new Promise((resolve, reject) => {
      const redirectUrl = `${window.location.origin}/auth/callback`;

      // Create popup window
      const popup = window.open(
        '',
        'google-auth',
        'width=500,height=600,scrollbars=yes,resizable=yes'
      );

      if (!popup) {
        reject(new Error('Popup blocked'));
        return;
      }

      // Start OAuth flow
      supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
          skipBrowserRedirect: true
        }
      }).then(({ data, error }) => {
        if (error) {
          popup.close();
          reject(error);
          return;
        }

        if (data.url) {
          popup.location.href = data.url;
        }

        // Listen for popup messages
        const messageListener = (event: MessageEvent) => {
          if (event.origin !== window.location.origin) return;

          if (event.data.type === 'SUPABASE_AUTH_SUCCESS') {
            window.removeEventListener('message', messageListener);
            popup.close();
            resolve({ data: event.data.session, error: null });
          } else if (event.data.type === 'SUPABASE_AUTH_ERROR') {
            window.removeEventListener('message', messageListener);
            popup.close();
            reject(new Error(event.data.error));
          }
        };

        window.addEventListener('message', messageListener);

        // Check if popup is closed manually
        const checkClosed = setInterval(() => {
          if (popup.closed) {
            clearInterval(checkClosed);
            window.removeEventListener('message', messageListener);
            reject(new Error('Authentication cancelled'));
          }
        }, 1000);
      });
    });
  },

  signOut: async () => {
    const { error } = await supabase.auth.signOut();
    return { error };
  },

  getCurrentUser: async () => {
    const { data: { user }, error } = await supabase.auth.getUser();
    return { user, error };
  },

  onAuthStateChange: (callback: (event: string, session: any) => void) => {
    return supabase.auth.onAuthStateChange(callback);
  }
};
