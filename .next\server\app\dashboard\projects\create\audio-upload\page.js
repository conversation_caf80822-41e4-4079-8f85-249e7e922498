/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/projects/create/audio-upload/page";
exports.ids = ["app/dashboard/projects/create/audio-upload/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fprojects%2Fcreate%2Faudio-upload%2Fpage&page=%2Fdashboard%2Fprojects%2Fcreate%2Faudio-upload%2Fpage&appPaths=%2Fdashboard%2Fprojects%2Fcreate%2Faudio-upload%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fprojects%2Fcreate%2Faudio-upload%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmoham%5COneDrive%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5Cbuilddocwithai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmoham%5COneDrive%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5Cbuilddocwithai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fprojects%2Fcreate%2Faudio-upload%2Fpage&page=%2Fdashboard%2Fprojects%2Fcreate%2Faudio-upload%2Fpage&appPaths=%2Fdashboard%2Fprojects%2Fcreate%2Faudio-upload%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fprojects%2Fcreate%2Faudio-upload%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmoham%5COneDrive%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5Cbuilddocwithai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmoham%5COneDrive%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5Cbuilddocwithai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'projects',\n        {\n        children: [\n        'create',\n        {\n        children: [\n        'audio-upload',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/projects/create/audio-upload/page.tsx */ \"(rsc)/./src/app/dashboard/projects/create/audio-upload/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/projects/create/audio-upload/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/projects/create/audio-upload/page\",\n        pathname: \"/dashboard/projects/create/audio-upload\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fprojects%2Fcreate%2Faudio-upload%2Fpage&page=%2Fdashboard%2Fprojects%2Fcreate%2Faudio-upload%2Fpage&appPaths=%2Fdashboard%2Fprojects%2Fcreate%2Faudio-upload%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fprojects%2Fcreate%2Faudio-upload%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmoham%5COneDrive%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5Cbuilddocwithai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmoham%5COneDrive%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5Cbuilddocwithai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vaGFtJTVDJTVDT25lRHJpdmUlNUMlNUMlRDglQjMlRDglQjclRDglQUQlMjAlRDglQTclRDklODQlRDklODUlRDklODMlRDglQUElRDglQTglNUMlNUNidWlsZGRvY3dpdGhhaSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdNQUFzSSIsInNvdXJjZXMiOlsid2VicGFjazovL2J1aWxkZG9jd2l0aGFpLz9jZmJlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbW9oYW1cXFxcT25lRHJpdmVcXFxc2LPYt9itINin2YTZhdmD2KrYqFxcXFxidWlsZGRvY3dpdGhhaVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vaGFtJTVDJTVDT25lRHJpdmUlNUMlNUMlRDglQjMlRDglQjclRDglQUQlMjAlRDglQTclRDklODQlRDklODUlRDklODMlRDglQUElRDglQTglNUMlNUNidWlsZGRvY3dpdGhhaSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q3Nvbm5lciU1QyU1Q2Rpc3QlNUMlNUNpbmRleC5tanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUb2FzdGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vaGFtJTVDJTVDT25lRHJpdmUlNUMlNUMlRDglQjMlRDglQjclRDglQUQlMjAlRDglQTclRDklODQlRDklODUlRDklODMlRDglQUElRDglQTglNUMlNUNidWlsZGRvY3dpdGhhaSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vaGFtJTVDJTVDT25lRHJpdmUlNUMlNUMlRDglQjMlRDglQjclRDglQUQlMjAlRDglQTclRDklODQlRDklODUlRDklODMlRDglQUElRDglQTglNUMlNUNidWlsZGRvY3dpdGhhaSU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvTEFBK0o7QUFDL0o7QUFDQSx3S0FBNkoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idWlsZGRvY3dpdGhhaS8/MjMyNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRvYXN0ZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxtb2hhbVxcXFxPbmVEcml2ZVxcXFzYs9i32K0g2KfZhNmF2YPYqtioXFxcXGJ1aWxkZG9jd2l0aGFpXFxcXG5vZGVfbW9kdWxlc1xcXFxzb25uZXJcXFxcZGlzdFxcXFxpbmRleC5tanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXG1vaGFtXFxcXE9uZURyaXZlXFxcXNiz2LfYrSDYp9mE2YXZg9iq2KhcXFxcYnVpbGRkb2N3aXRoYWlcXFxcc3JjXFxcXGNvbnRleHRzXFxcXEF1dGhDb250ZXh0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cprojects%5C%5Ccreate%5C%5Caudio-upload%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cprojects%5C%5Ccreate%5C%5Caudio-upload%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/projects/create/audio-upload/page.tsx */ \"(ssr)/./src/app/dashboard/projects/create/audio-upload/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vaGFtJTVDJTVDT25lRHJpdmUlNUMlNUMlRDglQjMlRDglQjclRDglQUQlMjAlRDglQTclRDklODQlRDklODUlRDklODMlRDglQUElRDglQTglNUMlNUNidWlsZGRvY3dpdGhhaSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q3Byb2plY3RzJTVDJTVDY3JlYXRlJTVDJTVDYXVkaW8tdXBsb2FkJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDhOQUEwSiIsInNvdXJjZXMiOlsid2VicGFjazovL2J1aWxkZG9jd2l0aGFpLz83ZmJiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbW9oYW1cXFxcT25lRHJpdmVcXFxc2LPYt9itINin2YTZhdmD2KrYqFxcXFxidWlsZGRvY3dpdGhhaVxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwcm9qZWN0c1xcXFxjcmVhdGVcXFxcYXVkaW8tdXBsb2FkXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cprojects%5C%5Ccreate%5C%5Caudio-upload%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/dashboard/Sidebar.tsx */ \"(ssr)/./src/components/dashboard/Sidebar.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vaGFtJTVDJTVDT25lRHJpdmUlNUMlNUMlRDglQjMlRDglQjclRDglQUQlMjAlRDglQTclRDklODQlRDklODUlRDklODMlRDglQUElRDglQTglNUMlNUNidWlsZGRvY3dpdGhhaSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNkYXNoYm9hcmQlNUMlNUNTaWRlYmFyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdMQUFpSyIsInNvdXJjZXMiOlsid2VicGFjazovL2J1aWxkZG9jd2l0aGFpLz8zOTNkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXG1vaGFtXFxcXE9uZURyaXZlXFxcXNiz2LfYrSDYp9mE2YXZg9iq2KhcXFxcYnVpbGRkb2N3aXRoYWlcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcZGFzaGJvYXJkXFxcXFNpZGViYXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/projects/create/audio-upload/page.tsx":
/*!*****************************************************************!*\
  !*** ./src/app/dashboard/projects/create/audio-upload/page.tsx ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AudioUploadPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Music_Pause_Play_RotateCcw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Music,Pause,Play,RotateCcw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Music_Pause_Play_RotateCcw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Music,Pause,Play,RotateCcw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Music_Pause_Play_RotateCcw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Music,Pause,Play,RotateCcw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Music_Pause_Play_RotateCcw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Music,Pause,Play,RotateCcw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Music_Pause_Play_RotateCcw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Music,Pause,Play,RotateCcw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Music_Pause_Play_RotateCcw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Music,Pause,Play,RotateCcw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Music_Pause_Play_RotateCcw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Music,Pause,Play,RotateCcw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_dashboard_ProjectInfoChecklist__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/ProjectInfoChecklist */ \"(ssr)/./src/components/dashboard/ProjectInfoChecklist.tsx\");\n/* harmony import */ var _components_dashboard_Breadcrumb__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/Breadcrumb */ \"(ssr)/./src/components/dashboard/Breadcrumb.tsx\");\n/* harmony import */ var _components_ui_ProgressBar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/ProgressBar */ \"(ssr)/./src/components/ui/ProgressBar.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _lib_project_creation_types__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/project-creation-types */ \"(ssr)/./src/lib/project-creation-types.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction AudioUploadPage() {\n    const [uploadedFile, setUploadedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleDrag = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (e.type === \"dragenter\" || e.type === \"dragover\") {\n            setDragActive(true);\n        } else if (e.type === \"dragleave\") {\n            setDragActive(false);\n        }\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragActive(false);\n        const files = e.dataTransfer.files;\n        if (files && files[0]) {\n            handleFileUpload(files[0]);\n        }\n    };\n    const handleFileSelect = (e)=>{\n        const files = e.target.files;\n        if (files && files[0]) {\n            handleFileUpload(files[0]);\n        }\n    };\n    const handleFileUpload = async (file)=>{\n        // Validate file type\n        if (!file.type.startsWith(\"audio/\")) {\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"يرجى رفع ملف صوتي صالح\");\n            return;\n        }\n        // Validate file size (max 100MB)\n        if (file.size > 100 * 1024 * 1024) {\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"حجم الملف كبير جداً. الحد الأقصى 100 ميجابايت\");\n            return;\n        }\n        setIsUploading(true);\n        setUploadProgress(0);\n        try {\n            // Simulate upload progress\n            const uploadedFileData = {\n                name: file.name,\n                size: file.size,\n                type: file.type,\n                url: URL.createObjectURL(file)\n            };\n            // Simulate progressive upload with progress updates\n            for(let i = 0; i <= 100; i += 10){\n                setUploadProgress(i);\n                await new Promise((resolve)=>setTimeout(resolve, 100));\n            }\n            setUploadedFile(uploadedFileData);\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"تم رفع الملف الصوتي بنجاح\");\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"فشل في رفع الملف. يرجى المحاولة مرة أخرى\");\n        } finally{\n            setIsUploading(false);\n            setUploadProgress(0);\n        }\n    };\n    const removeFile = ()=>{\n        if (uploadedFile?.url) {\n            URL.revokeObjectURL(uploadedFile.url);\n        }\n        setUploadedFile(null);\n        setIsPlaying(false);\n        setCurrentTime(0);\n        setDuration(0);\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const togglePlayPause = ()=>{\n        if (audioRef.current) {\n            if (isPlaying) {\n                audioRef.current.pause();\n            } else {\n                audioRef.current.play();\n            }\n            setIsPlaying(!isPlaying);\n        }\n    };\n    const resetAudio = ()=>{\n        if (audioRef.current) {\n            audioRef.current.currentTime = 0;\n            setCurrentTime(0);\n            setIsPlaying(false);\n        }\n    };\n    const formatTime = (time)=>{\n        const minutes = Math.floor(time / 60);\n        const seconds = Math.floor(time % 60);\n        return `${minutes}:${seconds.toString().padStart(2, \"0\")}`;\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 بايت\";\n        const k = 1024;\n        const sizes = [\n            \"بايت\",\n            \"كيلوبايت\",\n            \"ميجابايت\",\n            \"جيجابايت\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    const handleCreateProject = async ()=>{\n        if (!uploadedFile) {\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"يرجى رفع ملف صوتي أولاً\");\n            return;\n        }\n        try {\n            // Simulate project creation\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"تم إنشاء المشروع بنجاح!\");\n        // Redirect to projects list or project details\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"فشل في إنشاء المشروع. يرجى المحاولة مرة أخرى\");\n        }\n    };\n    const breadcrumbItems = [\n        {\n            label: \"لوحة التحكم\",\n            href: \"/dashboard\"\n        },\n        {\n            label: \"المشاريع\",\n            href: \"/dashboard/projects\"\n        },\n        {\n            label: \"إنشاء مشروع\",\n            href: \"/dashboard/projects/create\"\n        },\n        {\n            label: \"رفع ملف صوتي\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_Breadcrumb__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                items: breadcrumbItems\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-white font-vazirmatn mb-2\",\n                        children: \"رفع ملف صوتي\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-slate-400 font-vazirmatn\",\n                        children: \"قم بتحميل مستند صوتي يحتوي على معلومات مشروعك وتأكد أنه يحتوي على النقاط التالية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5,\n                    delay: 0.2\n                },\n                className: \"bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-white font-vazirmatn mb-6\",\n                        children: \"رفع المحتوى\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this),\n                    !uploadedFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `border-2 border-dashed rounded-2xl p-12 text-center transition-all duration-300 ${dragActive ? \"border-indigo-500 bg-indigo-500/10\" : \"border-slate-600 hover:border-slate-500\"}`,\n                        onDragEnter: handleDrag,\n                        onDragLeave: handleDrag,\n                        onDragOver: handleDrag,\n                        onDrop: handleDrop,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-2xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Music_Pause_Play_RotateCcw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-8 h-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white font-vazirmatn mb-2\",\n                                                children: \"رفع مستند المشروع\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-400 font-vazirmatn mb-4\",\n                                                children: \"ارفع ملف صوتي يحتوي على معلومات مشروعك\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: ()=>fileInputRef.current?.click(),\n                                        disabled: isUploading,\n                                        className: \"bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Music_Pause_Play_RotateCcw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 19\n                                            }, this),\n                                            isUploading ? \"جاري الرفع...\" : \"اختر ملف صوتي\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, this),\n                                    isUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full max-w-md\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ProgressBar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            progress: uploadProgress\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-slate-500 font-vazirmatn\",\n                                        children: \"نوع الملف المقبول: MP3 أو أي ملف صوتي (حتى 100 ميجابايت)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: fileInputRef,\n                                type: \"file\",\n                                accept: \"audio/*\",\n                                onChange: handleFileSelect,\n                                className: \"hidden\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-slate-700/30 rounded-xl p-6 border border-slate-600/30\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Music_Pause_Play_RotateCcw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 h-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-white font-vazirmatn\",\n                                                                children: uploadedFile.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-slate-400 font-vazirmatn\",\n                                                                children: formatFileSize(uploadedFile.size)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: removeFile,\n                                                className: \"text-red-400 hover:text-red-300 hover:bg-red-500/10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Music_Pause_Play_RotateCcw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, this),\n                                    uploadedFile.url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                                                ref: audioRef,\n                                                src: uploadedFile.url,\n                                                onLoadedMetadata: ()=>{\n                                                    if (audioRef.current) {\n                                                        setDuration(audioRef.current.duration);\n                                                    }\n                                                },\n                                                onTimeUpdate: ()=>{\n                                                    if (audioRef.current) {\n                                                        setCurrentTime(audioRef.current.currentTime);\n                                                    }\n                                                },\n                                                onEnded: ()=>setIsPlaying(false)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 rtl:space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: togglePlayPause,\n                                                        className: \"text-indigo-400 hover:text-indigo-300\",\n                                                        children: isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Music_Pause_Play_RotateCcw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 38\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Music_Pause_Play_RotateCcw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 70\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: resetAudio,\n                                                        className: \"text-slate-400 hover:text-slate-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Music_Pause_Play_RotateCcw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xs text-slate-400 font-vazirmatn mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatTime(currentTime)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                                        lineNumber: 315,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatTime(duration)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                                        lineNumber: 316,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full bg-slate-600 rounded-full h-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gradient-to-r from-indigo-500 to-purple-500 h-2 rounded-full transition-all duration-300\",\n                                                                    style: {\n                                                                        width: `${duration > 0 ? currentTime / duration * 100 : 0}%`\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: handleCreateProject,\n                                    className: \"bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600\",\n                                    children: \"إنشاء المشروع\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_ProjectInfoChecklist__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                items: _lib_project_creation_types__WEBPACK_IMPORTED_MODULE_7__.PROJECT_CHECKLIST_ITEMS,\n                completedCount: 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n                lineNumber: 344,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\create\\\\audio-upload\\\\page.tsx\",\n        lineNumber: 163,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/projects/create/audio-upload/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/Breadcrumb.tsx":
/*!*************************************************!*\
  !*** ./src/components/dashboard/Breadcrumb.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Breadcrumb)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Breadcrumb({ items, showBackButton = true }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleBack = ()=>{\n        router.back();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: -10\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.3\n        },\n        className: \"flex items-center justify-between mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                    className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                    children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: \"flex items-center\",\n                            children: [\n                                index > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4 text-slate-500 mx-2 rtl:rotate-180\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Breadcrumb.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 17\n                                }, this),\n                                item.href && index < items.length - 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: item.href,\n                                    className: \"text-slate-400 hover:text-indigo-400 transition-colors duration-200 font-vazirmatn text-sm hover:underline\",\n                                    children: item.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Breadcrumb.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: `font-vazirmatn text-sm ${index === items.length - 1 ? \"text-white font-medium\" : \"text-slate-400\"}`,\n                                    children: item.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Breadcrumb.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Breadcrumb.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Breadcrumb.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Breadcrumb.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            showBackButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: \"outline\",\n                size: \"sm\",\n                onClick: handleBack,\n                className: \"text-slate-400 border-slate-600 hover:text-white hover:border-indigo-500 hover:bg-indigo-500/10 transition-all duration-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"w-4 h-4 ml-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Breadcrumb.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this),\n                    \"رجوع\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Breadcrumb.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Breadcrumb.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/Breadcrumb.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/ProjectInfoChecklist.tsx":
/*!***********************************************************!*\
  !*** ./src/components/dashboard/ProjectInfoChecklist.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectInfoChecklist)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle2_Circle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle2,Circle,Info!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle2_Circle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle2,Circle,Info!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle2_Circle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle2,Circle,Info!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ProjectInfoChecklist({ items, completedCount, analysisResult }) {\n    // Map analysis result to checklist items\n    const updatedItems = items.map((item)=>{\n        if (analysisResult) {\n            const isCompleted = analysisResult.completedItems.includes(item.id);\n            return {\n                ...item,\n                completed: isCompleted\n            };\n        }\n        return item;\n    });\n    const actualCompletedCount = analysisResult ? analysisResult.completedItems.length : completedCount;\n    const totalItems = items.length;\n    const progressPercentage = actualCompletedCount / totalItems * 100;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.5\n        },\n        className: \"bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle2_Circle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-5 h-5 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white font-vazirmatn\",\n                                        children: \"قائمة مراجعة معلومات المشروع\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-slate-400 font-vazirmatn\",\n                                        children: [\n                                            completedCount,\n                                            \"/\",\n                                            totalItems,\n                                            \" مكتمل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-16 h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-16 h-16 transform -rotate-90\",\n                                viewBox: \"0 0 64 64\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                        cx: \"32\",\n                                        cy: \"32\",\n                                        r: \"28\",\n                                        stroke: \"currentColor\",\n                                        strokeWidth: \"4\",\n                                        fill: \"none\",\n                                        className: \"text-slate-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.circle, {\n                                        cx: \"32\",\n                                        cy: \"32\",\n                                        r: \"28\",\n                                        stroke: \"currentColor\",\n                                        strokeWidth: \"4\",\n                                        fill: \"none\",\n                                        strokeLinecap: \"round\",\n                                        className: \"text-indigo-500\",\n                                        initial: {\n                                            strokeDasharray: \"0 175.93\"\n                                        },\n                                        animate: {\n                                            strokeDasharray: `${progressPercentage / 100 * 175.93} 175.93`\n                                        },\n                                        transition: {\n                                            duration: 1,\n                                            ease: \"easeInOut\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-bold text-white font-vazirmatn\",\n                                    children: [\n                                        Math.round(progressPercentage),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-slate-300 text-sm font-vazirmatn mb-6 leading-relaxed\",\n                children: analysisResult ? \"نتائج تحليل الملف بالذكاء الاصطناعي:\" : \"تأكد من أن ملفك المرفوع يحتوي على جميع المعلومات المطلوبة أدناه\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            analysisResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `mb-6 p-4 rounded-xl border ${analysisResult.success ? \"bg-green-500/10 border-green-500/20 text-green-300\" : \"bg-amber-500/10 border-amber-500/20 text-amber-300\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm font-vazirmatn\",\n                    children: analysisResult.message\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: updatedItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.3,\n                            delay: index * 0.1\n                        },\n                        className: `flex items-center space-x-3 rtl:space-x-reverse p-3 rounded-xl transition-all duration-200 ${item.completed ? \"bg-green-500/10 border border-green-500/20\" : \"bg-slate-700/30 border border-slate-600/30 hover:bg-slate-700/50\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: item.completed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle2_Circle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-5 h-5 text-green-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle2_Circle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5 text-slate-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `text-sm font-vazirmatn ${item.completed ? \"text-green-300\" : \"text-slate-300\"}`,\n                                        children: item.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this),\n                                    !item.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-slate-500 font-vazirmatn mr-2 rtl:ml-2 rtl:mr-0\",\n                                        children: \"(اختياري)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this),\n                            item.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs bg-red-500/20 text-red-400 px-2 py-1 rounded-full font-vazirmatn\",\n                                    children: \"مطلوب\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, item.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 bg-indigo-500/10 border border-indigo-500/20 rounded-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-indigo-300 font-vazirmatn text-center\",\n                    children: \"\\uD83D\\uDCA1 ستُستخدم هذه البيانات لإنشاء المستندات المستقبلية. سيتم تنفيذ عملية التحقق لاحقاً بواسطة الذكاء الاصطناعي\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectInfoChecklist.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvUHJvamVjdEluZm9DaGVja2xpc3QudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRXNDO0FBQ21CO0FBYzFDLFNBQVNJLHFCQUFxQixFQUFFQyxLQUFLLEVBQUVDLGNBQWMsRUFBRUMsY0FBYyxFQUE2QjtJQUMvRyx5Q0FBeUM7SUFDekMsTUFBTUMsZUFBZUgsTUFBTUksR0FBRyxDQUFDQyxDQUFBQTtRQUM3QixJQUFJSCxnQkFBZ0I7WUFDbEIsTUFBTUksY0FBY0osZUFBZUssY0FBYyxDQUFDQyxRQUFRLENBQUNILEtBQUtJLEVBQUU7WUFDbEUsT0FBTztnQkFBRSxHQUFHSixJQUFJO2dCQUFFSyxXQUFXSjtZQUFZO1FBQzNDO1FBQ0EsT0FBT0Q7SUFDVDtJQUVBLE1BQU1NLHVCQUF1QlQsaUJBQ3pCQSxlQUFlSyxjQUFjLENBQUNLLE1BQU0sR0FDcENYO0lBRUosTUFBTVksYUFBYWIsTUFBTVksTUFBTTtJQUMvQixNQUFNRSxxQkFBcUIsdUJBQXdCRCxhQUFjO0lBRWpFLHFCQUNFLDhEQUFDbEIsaURBQU1BLENBQUNvQixHQUFHO1FBQ1RDLFNBQVM7WUFBRUMsU0FBUztZQUFHQyxHQUFHO1FBQUc7UUFDN0JDLFNBQVM7WUFBRUYsU0FBUztZQUFHQyxHQUFHO1FBQUU7UUFDNUJFLFlBQVk7WUFBRUMsVUFBVTtRQUFJO1FBQzVCQyxXQUFVOzswQkFHViw4REFBQ1A7Z0JBQUlPLFdBQVU7O2tDQUNiLDhEQUFDUDt3QkFBSU8sV0FBVTs7MENBQ2IsOERBQUNQO2dDQUFJTyxXQUFVOzBDQUNiLDRFQUFDeEIsb0dBQUlBO29DQUFDd0IsV0FBVTs7Ozs7Ozs7Ozs7MENBRWxCLDhEQUFDUDs7a0RBQ0MsOERBQUNRO3dDQUFHRCxXQUFVO2tEQUFrRDs7Ozs7O2tEQUdoRSw4REFBQ0U7d0NBQUVGLFdBQVU7OzRDQUNWckI7NENBQWU7NENBQUVZOzRDQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU1uQyw4REFBQ0U7d0JBQUlPLFdBQVU7OzBDQUNiLDhEQUFDRztnQ0FBSUgsV0FBVTtnQ0FBaUNJLFNBQVE7O2tEQUN0RCw4REFBQ0M7d0NBQ0NDLElBQUc7d0NBQ0hDLElBQUc7d0NBQ0hDLEdBQUU7d0NBQ0ZDLFFBQU87d0NBQ1BDLGFBQVk7d0NBQ1pDLE1BQUs7d0NBQ0xYLFdBQVU7Ozs7OztrREFFWiw4REFBQzNCLGlEQUFNQSxDQUFDZ0MsTUFBTTt3Q0FDWkMsSUFBRzt3Q0FDSEMsSUFBRzt3Q0FDSEMsR0FBRTt3Q0FDRkMsUUFBTzt3Q0FDUEMsYUFBWTt3Q0FDWkMsTUFBSzt3Q0FDTEMsZUFBYzt3Q0FDZFosV0FBVTt3Q0FDVk4sU0FBUzs0Q0FBRW1CLGlCQUFpQjt3Q0FBVzt3Q0FDdkNoQixTQUFTOzRDQUFFZ0IsaUJBQWlCLENBQUMsRUFBRSxxQkFBc0IsTUFBTyxPQUFPLE9BQU8sQ0FBQzt3Q0FBQzt3Q0FDNUVmLFlBQVk7NENBQUVDLFVBQVU7NENBQUdlLE1BQU07d0NBQVk7Ozs7Ozs7Ozs7OzswQ0FHakQsOERBQUNyQjtnQ0FBSU8sV0FBVTswQ0FDYiw0RUFBQ2U7b0NBQUtmLFdBQVU7O3dDQUNiZ0IsS0FBS0MsS0FBSyxDQUFDekI7d0NBQW9COzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT3hDLDhEQUFDVTtnQkFBRUYsV0FBVTswQkFDVnBCLGlCQUNHLHlDQUNBOzs7Ozs7WUFLTEEsZ0NBQ0MsOERBQUNhO2dCQUFJTyxXQUFXLENBQUMsMkJBQTJCLEVBQzFDcEIsZUFBZXNDLE9BQU8sR0FDbEIsdURBQ0EscURBQ0wsQ0FBQzswQkFDQSw0RUFBQ2hCO29CQUFFRixXQUFVOzhCQUNWcEIsZUFBZXVDLE9BQU87Ozs7Ozs7Ozs7OzBCQU03Qiw4REFBQzFCO2dCQUFJTyxXQUFVOzBCQUNabkIsYUFBYUMsR0FBRyxDQUFDLENBQUNDLE1BQU1xQyxzQkFDdkIsOERBQUMvQyxpREFBTUEsQ0FBQ29CLEdBQUc7d0JBRVRDLFNBQVM7NEJBQUVDLFNBQVM7NEJBQUcwQixHQUFHO3dCQUFHO3dCQUM3QnhCLFNBQVM7NEJBQUVGLFNBQVM7NEJBQUcwQixHQUFHO3dCQUFFO3dCQUM1QnZCLFlBQVk7NEJBQUVDLFVBQVU7NEJBQUt1QixPQUFPRixRQUFRO3dCQUFJO3dCQUNoRHBCLFdBQVcsQ0FBQywyRkFBMkYsRUFDckdqQixLQUFLSyxTQUFTLEdBQ1YsK0NBQ0EsbUVBQ0wsQ0FBQzs7MENBRUYsOERBQUNLO2dDQUFJTyxXQUFVOzBDQUNaakIsS0FBS0ssU0FBUyxpQkFDYiw4REFBQ2Qsb0dBQVlBO29DQUFDMEIsV0FBVTs7Ozs7eURBRXhCLDhEQUFDekIsb0dBQU1BO29DQUFDeUIsV0FBVTs7Ozs7Ozs7Ozs7MENBSXRCLDhEQUFDUDtnQ0FBSU8sV0FBVTs7a0RBQ2IsOERBQUNlO3dDQUFLZixXQUFXLENBQUMsdUJBQXVCLEVBQ3ZDakIsS0FBS0ssU0FBUyxHQUFHLG1CQUFtQixpQkFDckMsQ0FBQztrREFDQ0wsS0FBS3dDLEtBQUs7Ozs7OztvQ0FFWixDQUFDeEMsS0FBS3lDLFFBQVEsa0JBQ2IsOERBQUNUO3dDQUFLZixXQUFVO2tEQUErRDs7Ozs7Ozs7Ozs7OzRCQU1sRmpCLEtBQUt5QyxRQUFRLGtCQUNaLDhEQUFDL0I7Z0NBQUlPLFdBQVU7MENBQ2IsNEVBQUNlO29DQUFLZixXQUFVOzhDQUEyRTs7Ozs7Ozs7Ozs7O3VCQWpDMUZqQixLQUFLSSxFQUFFOzs7Ozs7Ozs7OzBCQTJDbEIsOERBQUNNO2dCQUFJTyxXQUFVOzBCQUNiLDRFQUFDRTtvQkFBRUYsV0FBVTs4QkFBcUQ7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTTFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVpbGRkb2N3aXRoYWkvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvUHJvamVjdEluZm9DaGVja2xpc3QudHN4PzJjZDUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IG1vdGlvbiB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nXG5pbXBvcnQgeyBDaGVja0NpcmNsZTIsIENpcmNsZSwgSW5mbyB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcbmltcG9ydCB7IENoZWNrbGlzdEl0ZW0gfSBmcm9tICdAL2xpYi9wcm9qZWN0LWNyZWF0aW9uLXR5cGVzJ1xuXG5pbnRlcmZhY2UgUHJvamVjdEluZm9DaGVja2xpc3RQcm9wcyB7XG4gIGl0ZW1zOiBDaGVja2xpc3RJdGVtW11cbiAgY29tcGxldGVkQ291bnQ6IG51bWJlclxuICBhbmFseXNpc1Jlc3VsdD86IHtcbiAgICBzdWNjZXNzOiBib29sZWFuXG4gICAgY29tcGxldGVkSXRlbXM6IHN0cmluZ1tdXG4gICAgbWlzc2luZ0l0ZW1zOiBzdHJpbmdbXVxuICAgIG1lc3NhZ2U6IHN0cmluZ1xuICB9XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFByb2plY3RJbmZvQ2hlY2tsaXN0KHsgaXRlbXMsIGNvbXBsZXRlZENvdW50LCBhbmFseXNpc1Jlc3VsdCB9OiBQcm9qZWN0SW5mb0NoZWNrbGlzdFByb3BzKSB7XG4gIC8vIE1hcCBhbmFseXNpcyByZXN1bHQgdG8gY2hlY2tsaXN0IGl0ZW1zXG4gIGNvbnN0IHVwZGF0ZWRJdGVtcyA9IGl0ZW1zLm1hcChpdGVtID0+IHtcbiAgICBpZiAoYW5hbHlzaXNSZXN1bHQpIHtcbiAgICAgIGNvbnN0IGlzQ29tcGxldGVkID0gYW5hbHlzaXNSZXN1bHQuY29tcGxldGVkSXRlbXMuaW5jbHVkZXMoaXRlbS5pZClcbiAgICAgIHJldHVybiB7IC4uLml0ZW0sIGNvbXBsZXRlZDogaXNDb21wbGV0ZWQgfVxuICAgIH1cbiAgICByZXR1cm4gaXRlbVxuICB9KVxuXG4gIGNvbnN0IGFjdHVhbENvbXBsZXRlZENvdW50ID0gYW5hbHlzaXNSZXN1bHRcbiAgICA/IGFuYWx5c2lzUmVzdWx0LmNvbXBsZXRlZEl0ZW1zLmxlbmd0aFxuICAgIDogY29tcGxldGVkQ291bnRcblxuICBjb25zdCB0b3RhbEl0ZW1zID0gaXRlbXMubGVuZ3RoXG4gIGNvbnN0IHByb2dyZXNzUGVyY2VudGFnZSA9IChhY3R1YWxDb21wbGV0ZWRDb3VudCAvIHRvdGFsSXRlbXMpICogMTAwXG5cbiAgcmV0dXJuIChcbiAgICA8bW90aW9uLmRpdlxuICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjUgfX1cbiAgICAgIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tc2xhdGUtODAwLzYwIHZpYS1zbGF0ZS04MDAvNDAgdG8tc2xhdGUtOTAwLzYwIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci1zbGF0ZS03MDAvNTAgcm91bmRlZC0yeGwgcC02IG1iLThcIlxuICAgID5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHJ0bDpzcGFjZS14LXJldmVyc2VcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1ncmFkaWVudC10by1yIGZyb20taW5kaWdvLTUwMCB0by1wdXJwbGUtNTAwIHJvdW5kZWQteGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxJbmZvIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBmb250LXZhemlybWF0blwiPlxuICAgICAgICAgICAgICDZgtin2KbZhdipINmF2LHYp9is2LnYqSDZhdi52YTZiNmF2KfYqiDYp9mE2YXYtNix2YjYuVxuICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1zbGF0ZS00MDAgZm9udC12YXppcm1hdG5cIj5cbiAgICAgICAgICAgICAge2NvbXBsZXRlZENvdW50fS97dG90YWxJdGVtc30g2YXZg9iq2YXZhFxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgXG4gICAgICAgIHsvKiBQcm9ncmVzcyBDaXJjbGUgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgdy0xNiBoLTE2XCI+XG4gICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgdHJhbnNmb3JtIC1yb3RhdGUtOTBcIiB2aWV3Qm94PVwiMCAwIDY0IDY0XCI+XG4gICAgICAgICAgICA8Y2lyY2xlXG4gICAgICAgICAgICAgIGN4PVwiMzJcIlxuICAgICAgICAgICAgICBjeT1cIjMyXCJcbiAgICAgICAgICAgICAgcj1cIjI4XCJcbiAgICAgICAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCI0XCJcbiAgICAgICAgICAgICAgZmlsbD1cIm5vbmVcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTcwMFwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgPG1vdGlvbi5jaXJjbGVcbiAgICAgICAgICAgICAgY3g9XCIzMlwiXG4gICAgICAgICAgICAgIGN5PVwiMzJcIlxuICAgICAgICAgICAgICByPVwiMjhcIlxuICAgICAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjRcIlxuICAgICAgICAgICAgICBmaWxsPVwibm9uZVwiXG4gICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtaW5kaWdvLTUwMFwiXG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgc3Ryb2tlRGFzaGFycmF5OiBcIjAgMTc1LjkzXCIgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBzdHJva2VEYXNoYXJyYXk6IGAkeyhwcm9ncmVzc1BlcmNlbnRhZ2UgLyAxMDApICogMTc1LjkzfSAxNzUuOTNgIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDEsIGVhc2U6IFwiZWFzZUluT3V0XCIgfX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIGZvbnQtdmF6aXJtYXRuXCI+XG4gICAgICAgICAgICAgIHtNYXRoLnJvdW5kKHByb2dyZXNzUGVyY2VudGFnZSl9JVxuICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRGVzY3JpcHRpb24gKi99XG4gICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTMwMCB0ZXh0LXNtIGZvbnQtdmF6aXJtYXRuIG1iLTYgbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgIHthbmFseXNpc1Jlc3VsdFxuICAgICAgICAgID8gJ9mG2KrYp9im2Kwg2KrYrdmE2YrZhCDYp9mE2YXZhNmBINio2KfZhNiw2YPYp9ihINin2YTYp9i12LfZhtin2LnZijonXG4gICAgICAgICAgOiAn2KrYo9mD2K8g2YXZhiDYo9mGINmF2YTZgdmDINin2YTZhdix2YHZiNi5INmK2K3YqtmI2Yog2LnZhNmJINis2YXZiti5INin2YTZhdi52YTZiNmF2KfYqiDYp9mE2YXYt9mE2YjYqNipINij2K/Zhtin2YcnXG4gICAgICAgIH1cbiAgICAgIDwvcD5cblxuICAgICAgey8qIEFuYWx5c2lzIFJlc3VsdCBNZXNzYWdlICovfVxuICAgICAge2FuYWx5c2lzUmVzdWx0ICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BtYi02IHAtNCByb3VuZGVkLXhsIGJvcmRlciAke1xuICAgICAgICAgIGFuYWx5c2lzUmVzdWx0LnN1Y2Nlc3NcbiAgICAgICAgICAgID8gJ2JnLWdyZWVuLTUwMC8xMCBib3JkZXItZ3JlZW4tNTAwLzIwIHRleHQtZ3JlZW4tMzAwJ1xuICAgICAgICAgICAgOiAnYmctYW1iZXItNTAwLzEwIGJvcmRlci1hbWJlci01MDAvMjAgdGV4dC1hbWJlci0zMDAnXG4gICAgICAgIH1gfT5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtdmF6aXJtYXRuXCI+XG4gICAgICAgICAgICB7YW5hbHlzaXNSZXN1bHQubWVzc2FnZX1cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIENoZWNrbGlzdCBJdGVtcyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgIHt1cGRhdGVkSXRlbXMubWFwKChpdGVtLCBpbmRleCkgPT4gKFxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBrZXk9e2l0ZW0uaWR9XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IDIwIH19XG4gICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMywgZGVsYXk6IGluZGV4ICogMC4xIH19XG4gICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgcnRsOnNwYWNlLXgtcmV2ZXJzZSBwLTMgcm91bmRlZC14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcbiAgICAgICAgICAgICAgaXRlbS5jb21wbGV0ZWQgXG4gICAgICAgICAgICAgICAgPyAnYmctZ3JlZW4tNTAwLzEwIGJvcmRlciBib3JkZXItZ3JlZW4tNTAwLzIwJyBcbiAgICAgICAgICAgICAgICA6ICdiZy1zbGF0ZS03MDAvMzAgYm9yZGVyIGJvcmRlci1zbGF0ZS02MDAvMzAgaG92ZXI6Ymctc2xhdGUtNzAwLzUwJ1xuICAgICAgICAgICAgfWB9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgIHtpdGVtLmNvbXBsZXRlZCA/IChcbiAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUyIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ncmVlbi00MDBcIiAvPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxDaXJjbGUgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXNsYXRlLTQwMFwiIC8+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LXZhemlybWF0biAke1xuICAgICAgICAgICAgICAgIGl0ZW0uY29tcGxldGVkID8gJ3RleHQtZ3JlZW4tMzAwJyA6ICd0ZXh0LXNsYXRlLTMwMCdcbiAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgIHtpdGVtLmxhYmVsfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIHshaXRlbS5yZXF1aXJlZCAmJiAoXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTUwMCBmb250LXZhemlybWF0biBtci0yIHJ0bDptbC0yIHJ0bDptci0wXCI+XG4gICAgICAgICAgICAgICAgICAo2KfYrtiq2YrYp9ix2YopXG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHtpdGVtLnJlcXVpcmVkICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyBiZy1yZWQtNTAwLzIwIHRleHQtcmVkLTQwMCBweC0yIHB5LTEgcm91bmRlZC1mdWxsIGZvbnQtdmF6aXJtYXRuXCI+XG4gICAgICAgICAgICAgICAgICDZhdi32YTZiNioXG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICApKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRm9vdGVyIE5vdGUgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTYgcC00IGJnLWluZGlnby01MDAvMTAgYm9yZGVyIGJvcmRlci1pbmRpZ28tNTAwLzIwIHJvdW5kZWQteGxcIj5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWluZGlnby0zMDAgZm9udC12YXppcm1hdG4gdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICDwn5KhINiz2KrZj9iz2KrYrtiv2YUg2YfYsNmHINin2YTYqNmK2KfZhtin2Kog2YTYpdmG2LTYp9ihINin2YTZhdiz2KrZhtiv2KfYqiDYp9mE2YXYs9iq2YLYqNmE2YrYqS4g2LPZitiq2YUg2KrZhtmB2YrYsCDYudmF2YTZitipINin2YTYqtit2YLZgiDZhNin2K3Zgtin2Ysg2KjZiNin2LPYt9ipINin2YTYsNmD2KfYoSDYp9mE2KfYtdi32YbYp9i52YpcbiAgICAgICAgPC9wPlxuICAgICAgPC9kaXY+XG4gICAgPC9tb3Rpb24uZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsibW90aW9uIiwiQ2hlY2tDaXJjbGUyIiwiQ2lyY2xlIiwiSW5mbyIsIlByb2plY3RJbmZvQ2hlY2tsaXN0IiwiaXRlbXMiLCJjb21wbGV0ZWRDb3VudCIsImFuYWx5c2lzUmVzdWx0IiwidXBkYXRlZEl0ZW1zIiwibWFwIiwiaXRlbSIsImlzQ29tcGxldGVkIiwiY29tcGxldGVkSXRlbXMiLCJpbmNsdWRlcyIsImlkIiwiY29tcGxldGVkIiwiYWN0dWFsQ29tcGxldGVkQ291bnQiLCJsZW5ndGgiLCJ0b3RhbEl0ZW1zIiwicHJvZ3Jlc3NQZXJjZW50YWdlIiwiZGl2IiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJ5IiwiYW5pbWF0ZSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsImNsYXNzTmFtZSIsImgzIiwicCIsInN2ZyIsInZpZXdCb3giLCJjaXJjbGUiLCJjeCIsImN5IiwiciIsInN0cm9rZSIsInN0cm9rZVdpZHRoIiwiZmlsbCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VEYXNoYXJyYXkiLCJlYXNlIiwic3BhbiIsIk1hdGgiLCJyb3VuZCIsInN1Y2Nlc3MiLCJtZXNzYWdlIiwiaW5kZXgiLCJ4IiwiZGVsYXkiLCJsYWJlbCIsInJlcXVpcmVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/ProjectInfoChecklist.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/Sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/Sidebar.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/scroll-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/compass.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction Sidebar({ className = \"\" }) {\n    const { user, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [isMobileOpen, setIsMobileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"تم تسجيل الخروج بنجاح\");\n            router.push(\"/\");\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"حدث خطأ أثناء تسجيل الخروج\");\n        }\n    };\n    const navigationSections = [\n        {\n            title: \"الرئيسية\",\n            items: [\n                {\n                    name: \"نظرة عامة\",\n                    href: \"/dashboard\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    active: pathname === \"/dashboard\",\n                    disabled: false\n                },\n                {\n                    name: \"مشاريعي\",\n                    href: \"/dashboard/projects\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    active: pathname === \"/dashboard/projects\",\n                    disabled: false\n                }\n            ]\n        },\n        {\n            title: \"المستندات والعقود\",\n            items: [\n                {\n                    name: \"مستنداتي\",\n                    href: \"/dashboard/documents\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    active: pathname === \"/dashboard/documents\" || pathname === \"/dashboard/discover-documents\",\n                    disabled: false\n                },\n                {\n                    name: \"عقودي\",\n                    href: \"/dashboard/contracts\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    active: pathname === \"/dashboard/contracts\",\n                    disabled: false\n                }\n            ]\n        },\n        {\n            title: \"الاستكشاف والذكاء الاصطناعي\",\n            items: [\n                {\n                    name: \"اكتشف المستندات\",\n                    href: \"/dashboard/discover-documents\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                    active: pathname === \"/dashboard/discover-documents\",\n                    disabled: false\n                },\n                {\n                    name: \"اكتشف العقود\",\n                    href: \"/dashboard/discover-contracts\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                    active: pathname === \"/dashboard/discover-contracts\",\n                    disabled: false\n                },\n                {\n                    name: \"المساعد الذكي\",\n                    href: \"/dashboard/ai-assistant\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                    active: pathname === \"/dashboard/ai-assistant\",\n                    disabled: false\n                },\n                {\n                    name: \"مساحة البيانات\",\n                    href: \"/dashboard/Data-space\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                    active: pathname === \"/dashboard/Data-space\",\n                    disabled: false\n                }\n            ]\n        },\n        {\n            title: \"الإعدادات\",\n            items: [\n                {\n                    name: \"الإعدادات\",\n                    href: \"/dashboard/settings\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    active: pathname === \"/dashboard/settings\",\n                    disabled: false\n                }\n            ]\n        }\n    ];\n    const SidebarContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-gradient-to-b from-slate-800 via-slate-800 to-slate-900 border-r border-slate-700/50 shadow-2xl backdrop-blur-sm\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-slate-700/50 bg-gradient-to-l from-indigo-500/5 to-violet-500/5\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: \"/\",\n                        className: \"flex items-center space-x-2 rtl:space-x-reverse group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-gradient-to-r from-indigo-500 to-violet-500 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-indigo-500/25 transition-all duration-300 group-hover:scale-105\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white font-bold text-sm\",\n                                    children: \"BD\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl font-bold text-white font-vazirmatn group-hover:text-indigo-300 transition-colors duration-300\",\n                                children: \"BuildDocwithai\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-slate-700/50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3 rtl:space-x-reverse p-3 bg-slate-700/30 rounded-xl hover:bg-slate-700/50 transition-all duration-300 group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-gradient-to-r from-indigo-500 to-violet-500 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-indigo-500/25 transition-all duration-300 group-hover:scale-105\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"w-6 h-6 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-semibold text-white font-vazirmatn truncate group-hover:text-indigo-300 transition-colors duration-300\",\n                                        children: user?.user_metadata?.name || user?.email?.split(\"@\")[0] || \"المستخدم\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-slate-400 font-vazirmatn truncate\",\n                                        children: user?.email\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-green-400 rounded-full mr-2 rtl:ml-2 rtl:mr-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-400 font-vazirmatn\",\n                                                children: \"متصل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex-1 p-4 space-y-6 overflow-y-auto\",\n                    children: navigationSections.map((section, sectionIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xs font-semibold text-slate-400 uppercase tracking-wider font-vazirmatn px-4 mb-3\",\n                                    children: section.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: section.items.map((item)=>{\n                                        const Icon = item.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: item.disabled ? \"#\" : item.href,\n                                            className: `flex items-center space-x-3 rtl:space-x-reverse px-4 py-3 rounded-xl text-sm font-medium font-vazirmatn transition-all duration-300 group relative overflow-hidden ${item.active ? \"bg-gradient-to-l from-indigo-500 to-violet-500 text-white shadow-lg shadow-indigo-500/25\" : item.disabled ? \"text-slate-500 cursor-not-allowed\" : \"text-slate-300 hover:text-white hover:bg-slate-700/50 hover:shadow-lg\"}`,\n                                            onClick: (e)=>{\n                                                if (item.disabled) {\n                                                    e.preventDefault();\n                                                }\n                                            },\n                                            children: [\n                                                item.active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-l from-indigo-400/20 to-violet-400/20 rounded-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: `w-5 h-5 relative z-10 transition-transform duration-300 ${item.active ? \"text-white\" : \"group-hover:scale-110\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"relative z-10 flex-1\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 21\n                                                }, this),\n                                                item.disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs bg-slate-600/50 text-slate-400 px-2 py-1 rounded-full backdrop-blur-sm relative z-10\",\n                                                    children: \"قريباً\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                sectionIndex < navigationSections.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-px bg-gradient-to-l from-slate-700 via-slate-600 to-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, section.title, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-t border-slate-700/50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-yellow-500/10 via-orange-500/10 to-red-500/10 border border-yellow-500/20 rounded-xl p-4 space-y-4 backdrop-blur-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-semibold text-white font-vazirmatn\",\n                                        children: \"الخطة المجانية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-xs text-slate-300 font-vazirmatn\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"3 من 10 مستندات مستخدمة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-yellow-400\",\n                                                children: \"30%\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-slate-700/50 rounded-full h-2.5 overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-yellow-500 to-orange-500 h-2.5 rounded-full shadow-sm transition-all duration-500\",\n                                            style: {\n                                                width: \"30%\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"gradient\",\n                                size: \"sm\",\n                                className: \"w-full font-vazirmatn text-sm bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 shadow-lg hover:shadow-yellow-500/25 transition-all duration-300\",\n                                children: \"ترقية الخطة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-t border-slate-700/50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleSignOut,\n                        className: \"flex items-center space-x-3 rtl:space-x-reverse w-full px-4 py-3 text-sm font-medium text-slate-300 hover:text-red-300 hover:bg-red-500/10 rounded-xl transition-all duration-300 font-vazirmatn group border border-transparent hover:border-red-500/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"w-5 h-5 group-hover:scale-110 transition-transform duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"تسجيل الخروج\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n            lineNumber: 134,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsMobileOpen(true),\n                className: \"lg:hidden fixed top-4 left-4 z-50 p-2 bg-slate-800 text-white rounded-lg shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `hidden lg:flex lg:flex-col lg:w-80 lg:fixed lg:inset-y-0 lg:right-0 lg:z-30 ${className}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this),\n            isMobileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed inset-0 z-50 flex justify-end\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50\",\n                        onClick: ()=>setIsMobileOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.div, {\n                        initial: {\n                            x: \"100%\"\n                        },\n                        animate: {\n                            x: 0\n                        },\n                        exit: {\n                            x: \"100%\"\n                        },\n                        transition: {\n                            type: \"tween\",\n                            duration: 0.3\n                        },\n                        className: \"relative flex flex-col w-80 max-w-xs bg-slate-800 shadow-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMobileOpen(false),\n                                className: \"absolute top-4 left-4 p-2 text-slate-400 hover:text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 283,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            primary: \"bg-indigo-500 text-white hover:bg-indigo-600 shadow-lg hover:shadow-xl\",\n            secondary: \"bg-transparent border-2 border-white/20 text-white hover:border-white/40 hover:bg-white/5\",\n            outline: \"border border-slate-600 bg-transparent hover:bg-slate-800 hover:text-white\",\n            ghost: \"hover:bg-slate-800 hover:text-white\",\n            link: \"text-indigo-400 underline-offset-4 hover:underline\",\n            gradient: \"bg-gradient-to-r from-indigo-500 to-violet-500 text-white hover:from-indigo-600 hover:to-violet-600 shadow-lg hover:shadow-xl\"\n        },\n        size: {\n            default: \"h-12 px-6 py-3\",\n            sm: \"h-9 px-4 py-2 text-xs\",\n            lg: \"h-14 px-8 py-4 text-base\",\n            xl: \"h-16 px-10 py-5 text-lg\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"primary\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 41,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ProgressBar.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/ProgressBar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProgressBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ProgressBar({ progress, className, showPercentage = true, size = \"md\", variant = \"default\" }) {\n    const sizeClasses = {\n        sm: \"h-2\",\n        md: \"h-3\",\n        lg: \"h-4\"\n    };\n    const variantClasses = {\n        default: \"from-indigo-500 to-purple-500\",\n        success: \"from-green-500 to-emerald-500\",\n        warning: \"from-amber-500 to-orange-500\",\n        error: \"from-red-500 to-pink-500\"\n    };\n    const clampedProgress = Math.min(Math.max(progress, 0), 100);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"space-y-2\", className),\n        children: [\n            showPercentage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-slate-300 font-vazirmatn\",\n                        children: \"جاري الرفع...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\ui\\\\ProgressBar.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium text-white font-vazirmatn\",\n                        children: [\n                            Math.round(clampedProgress),\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\ui\\\\ProgressBar.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\ui\\\\ProgressBar.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"w-full bg-slate-700 rounded-full overflow-hidden\", sizeClasses[size]),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"h-full bg-gradient-to-r rounded-full transition-all duration-300\", variantClasses[variant]),\n                    initial: {\n                        width: 0\n                    },\n                    animate: {\n                        width: `${clampedProgress}%`\n                    },\n                    transition: {\n                        duration: 0.5,\n                        ease: \"easeInOut\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\ui\\\\ProgressBar.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\ui\\\\ProgressBar.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\ui\\\\ProgressBar.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9Qcm9ncmVzc0Jhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRXNDO0FBQ047QUFVakIsU0FBU0UsWUFBWSxFQUNsQ0MsUUFBUSxFQUNSQyxTQUFTLEVBQ1RDLGlCQUFpQixJQUFJLEVBQ3JCQyxPQUFPLElBQUksRUFDWEMsVUFBVSxTQUFTLEVBQ0Y7SUFDakIsTUFBTUMsY0FBYztRQUNsQkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7SUFDTjtJQUVBLE1BQU1DLGlCQUFpQjtRQUNyQkMsU0FBUztRQUNUQyxTQUFTO1FBQ1RDLFNBQVM7UUFDVEMsT0FBTztJQUNUO0lBRUEsTUFBTUMsa0JBQWtCQyxLQUFLQyxHQUFHLENBQUNELEtBQUtFLEdBQUcsQ0FBQ2pCLFVBQVUsSUFBSTtJQUV4RCxxQkFDRSw4REFBQ2tCO1FBQUlqQixXQUFXSCw4Q0FBRUEsQ0FBQyxhQUFhRzs7WUFDN0JDLGdDQUNDLDhEQUFDZ0I7Z0JBQUlqQixXQUFVOztrQ0FDYiw4REFBQ2tCO3dCQUFLbEIsV0FBVTtrQ0FBd0M7Ozs7OztrQ0FHeEQsOERBQUNrQjt3QkFBS2xCLFdBQVU7OzRCQUNiYyxLQUFLSyxLQUFLLENBQUNOOzRCQUFpQjs7Ozs7Ozs7Ozs7OzswQkFLbkMsOERBQUNJO2dCQUFJakIsV0FBV0gsOENBQUVBLENBQ2hCLG9EQUNBTyxXQUFXLENBQUNGLEtBQUs7MEJBRWpCLDRFQUFDTixpREFBTUEsQ0FBQ3FCLEdBQUc7b0JBQ1RqQixXQUFXSCw4Q0FBRUEsQ0FDWCxvRUFDQVcsY0FBYyxDQUFDTCxRQUFRO29CQUV6QmlCLFNBQVM7d0JBQUVDLE9BQU87b0JBQUU7b0JBQ3BCQyxTQUFTO3dCQUFFRCxPQUFPLENBQUMsRUFBRVIsZ0JBQWdCLENBQUMsQ0FBQztvQkFBQztvQkFDeENVLFlBQVk7d0JBQUVDLFVBQVU7d0JBQUtDLE1BQU07b0JBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS3pEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVpbGRkb2N3aXRoYWkvLi9zcmMvY29tcG9uZW50cy91aS9Qcm9ncmVzc0Jhci50c3g/MDJkZSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSAnZnJhbWVyLW1vdGlvbidcbmltcG9ydCB7IGNuIH0gZnJvbSAnQC9saWIvdXRpbHMnXG5cbmludGVyZmFjZSBQcm9ncmVzc0JhclByb3BzIHtcbiAgcHJvZ3Jlc3M6IG51bWJlclxuICBjbGFzc05hbWU/OiBzdHJpbmdcbiAgc2hvd1BlcmNlbnRhZ2U/OiBib29sZWFuXG4gIHNpemU/OiAnc20nIHwgJ21kJyB8ICdsZydcbiAgdmFyaWFudD86ICdkZWZhdWx0JyB8ICdzdWNjZXNzJyB8ICd3YXJuaW5nJyB8ICdlcnJvcidcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUHJvZ3Jlc3NCYXIoe1xuICBwcm9ncmVzcyxcbiAgY2xhc3NOYW1lLFxuICBzaG93UGVyY2VudGFnZSA9IHRydWUsXG4gIHNpemUgPSAnbWQnLFxuICB2YXJpYW50ID0gJ2RlZmF1bHQnXG59OiBQcm9ncmVzc0JhclByb3BzKSB7XG4gIGNvbnN0IHNpemVDbGFzc2VzID0ge1xuICAgIHNtOiAnaC0yJyxcbiAgICBtZDogJ2gtMycsXG4gICAgbGc6ICdoLTQnXG4gIH1cblxuICBjb25zdCB2YXJpYW50Q2xhc3NlcyA9IHtcbiAgICBkZWZhdWx0OiAnZnJvbS1pbmRpZ28tNTAwIHRvLXB1cnBsZS01MDAnLFxuICAgIHN1Y2Nlc3M6ICdmcm9tLWdyZWVuLTUwMCB0by1lbWVyYWxkLTUwMCcsXG4gICAgd2FybmluZzogJ2Zyb20tYW1iZXItNTAwIHRvLW9yYW5nZS01MDAnLFxuICAgIGVycm9yOiAnZnJvbS1yZWQtNTAwIHRvLXBpbmstNTAwJ1xuICB9XG5cbiAgY29uc3QgY2xhbXBlZFByb2dyZXNzID0gTWF0aC5taW4oTWF0aC5tYXgocHJvZ3Jlc3MsIDApLCAxMDApXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17Y24oJ3NwYWNlLXktMicsIGNsYXNzTmFtZSl9PlxuICAgICAge3Nob3dQZXJjZW50YWdlICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtc2xhdGUtMzAwIGZvbnQtdmF6aXJtYXRuXCI+XG4gICAgICAgICAgICDYrNin2LHZiiDYp9mE2LHZgdi5Li4uXG4gICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZSBmb250LXZhemlybWF0blwiPlxuICAgICAgICAgICAge01hdGgucm91bmQoY2xhbXBlZFByb2dyZXNzKX0lXG4gICAgICAgICAgPC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgICBcbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgJ3ctZnVsbCBiZy1zbGF0ZS03MDAgcm91bmRlZC1mdWxsIG92ZXJmbG93LWhpZGRlbicsXG4gICAgICAgIHNpemVDbGFzc2VzW3NpemVdXG4gICAgICApfT5cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgJ2gtZnVsbCBiZy1ncmFkaWVudC10by1yIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAnLFxuICAgICAgICAgICAgdmFyaWFudENsYXNzZXNbdmFyaWFudF1cbiAgICAgICAgICApfVxuICAgICAgICAgIGluaXRpYWw9e3sgd2lkdGg6IDAgfX1cbiAgICAgICAgICBhbmltYXRlPXt7IHdpZHRoOiBgJHtjbGFtcGVkUHJvZ3Jlc3N9JWAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjUsIGVhc2U6ICdlYXNlSW5PdXQnIH19XG4gICAgICAgIC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIm1vdGlvbiIsImNuIiwiUHJvZ3Jlc3NCYXIiLCJwcm9ncmVzcyIsImNsYXNzTmFtZSIsInNob3dQZXJjZW50YWdlIiwic2l6ZSIsInZhcmlhbnQiLCJzaXplQ2xhc3NlcyIsInNtIiwibWQiLCJsZyIsInZhcmlhbnRDbGFzc2VzIiwiZGVmYXVsdCIsInN1Y2Nlc3MiLCJ3YXJuaW5nIiwiZXJyb3IiLCJjbGFtcGVkUHJvZ3Jlc3MiLCJNYXRoIiwibWluIiwibWF4IiwiZGl2Iiwic3BhbiIsInJvdW5kIiwiaW5pdGlhbCIsIndpZHRoIiwiYW5pbWF0ZSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsImVhc2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ProgressBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    loading: true,\n    signOut: async ()=>{}\n});\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get initial user\n        const getUser = async ()=>{\n            try {\n                const { user } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.authHelpers.getCurrentUser();\n                setUser(user);\n            } catch (error) {\n                console.error(\"Error getting user:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        getUser();\n        // Listen for auth changes\n        const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.authHelpers.onAuthStateChange(async (event, session)=>{\n            setUser(session?.user ?? null);\n            setLoading(false);\n        });\n        return ()=>subscription.unsubscribe();\n    }, []);\n    const signOut = async ()=>{\n        try {\n            await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.authHelpers.signOut();\n            setUser(null);\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n        }\n    };\n    const value = {\n        user,\n        loading,\n        signOut\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/project-creation-types.ts":
/*!*******************************************!*\
  !*** ./src/lib/project-creation-types.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROJECT_CHECKLIST_ITEMS: () => (/* binding */ PROJECT_CHECKLIST_ITEMS)\n/* harmony export */ });\nconst PROJECT_CHECKLIST_ITEMS = [\n    {\n        id: \"project-name\",\n        label: \"إسم المشروع\",\n        required: true,\n        completed: false\n    },\n    {\n        id: \"project-description\",\n        label: \"وصف المشروع\",\n        required: true,\n        completed: false\n    },\n    {\n        id: \"target-customers\",\n        label: \"شريحة العملاء المستهدفة (اختياري)\",\n        required: false,\n        completed: false\n    },\n    {\n        id: \"customer-problem\",\n        label: \"مشكلة العملاء (اختياري)\",\n        required: false,\n        completed: false\n    },\n    {\n        id: \"solution\",\n        label: \"حل مشروعك (اختياري)\",\n        required: false,\n        completed: false\n    },\n    {\n        id: \"project-location\",\n        label: \"موقع مشروعك\",\n        required: true,\n        completed: false\n    },\n    {\n        id: \"founders\",\n        label: \"مؤسسو المشروع وحصصهم\",\n        required: true,\n        completed: false\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/project-creation-types.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authHelpers: () => (/* binding */ authHelpers),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n\n// Environment variables validation\nconst supabaseUrl = \"https://zkjasrxgkkhfcafwilsb.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpramFzcnhna2toZmNhZndpbHNiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjE0MjIsImV4cCI6MjA2Nzc5NzQyMn0.XljfQR5z8rOqeTplTQ78llsYYeind8jMw3Nl1HqnrvQ\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables. Please check your .env.local file.\");\n}\n// Supabase client for client-side operations\nconst supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)({\n    supabaseUrl,\n    supabaseKey: supabaseAnonKey\n});\n// Auth helper functions\nconst authHelpers = {\n    signUp: async (email, password)=>{\n        const redirectUrl = `${window.location.origin}/`;\n        const { data, error } = await supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                emailRedirectTo: redirectUrl\n            }\n        });\n        return {\n            data,\n            error\n        };\n    },\n    signIn: async (email, password)=>{\n        const { data, error } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        return {\n            data,\n            error\n        };\n    },\n    signInWithGoogle: async ()=>{\n        const redirectUrl = `${window.location.origin}/`;\n        const { data, error } = await supabase.auth.signInWithOAuth({\n            provider: \"google\",\n            options: {\n                redirectTo: redirectUrl,\n                queryParams: {\n                    access_type: \"offline\",\n                    prompt: \"consent\"\n                },\n                skipBrowserRedirect: false // We'll handle popup manually\n            }\n        });\n        return {\n            data,\n            error\n        };\n    },\n    signInWithGooglePopup: async ()=>{\n        return new Promise((resolve, reject)=>{\n            const redirectUrl = `${window.location.origin}/auth/callback`;\n            // Create popup window\n            const popup = window.open(\"\", \"google-auth\", \"width=500,height=600,scrollbars=yes,resizable=yes\");\n            if (!popup) {\n                reject(new Error(\"Popup blocked\"));\n                return;\n            }\n            // Start OAuth flow\n            supabase.auth.signInWithOAuth({\n                provider: \"google\",\n                options: {\n                    redirectTo: redirectUrl,\n                    queryParams: {\n                        access_type: \"offline\",\n                        prompt: \"consent\"\n                    },\n                    skipBrowserRedirect: true\n                }\n            }).then(({ data, error })=>{\n                if (error) {\n                    popup.close();\n                    reject(error);\n                    return;\n                }\n                if (data.url) {\n                    popup.location.href = data.url;\n                }\n                // Listen for popup messages\n                const messageListener = (event)=>{\n                    if (event.origin !== window.location.origin) return;\n                    if (event.data.type === \"SUPABASE_AUTH_SUCCESS\") {\n                        window.removeEventListener(\"message\", messageListener);\n                        popup.close();\n                        resolve({\n                            data: event.data.session,\n                            error: null\n                        });\n                    } else if (event.data.type === \"SUPABASE_AUTH_ERROR\") {\n                        window.removeEventListener(\"message\", messageListener);\n                        popup.close();\n                        reject(new Error(event.data.error));\n                    }\n                };\n                window.addEventListener(\"message\", messageListener);\n                // Check if popup is closed manually\n                const checkClosed = setInterval(()=>{\n                    if (popup.closed) {\n                        clearInterval(checkClosed);\n                        window.removeEventListener(\"message\", messageListener);\n                        reject(new Error(\"Authentication cancelled\"));\n                    }\n                }, 1000);\n            });\n        });\n    },\n    signOut: async ()=>{\n        const { error } = await supabase.auth.signOut();\n        return {\n            error\n        };\n    },\n    getCurrentUser: async ()=>{\n        const { data: { user }, error } = await supabase.auth.getUser();\n        return {\n            user,\n            error\n        };\n    },\n    onAuthStateChange: (callback)=>{\n        return supabase.auth.onAuthStateChange(callback);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFDNEU7QUFFNUUsbUNBQW1DO0FBQ25DLE1BQU1DLGNBQWNDLDBDQUFvQztBQUN4RCxNQUFNRyxrQkFBa0JILGtOQUF5QztBQUVqRSxJQUFJLENBQUNELGVBQWUsQ0FBQ0ksaUJBQWlCO0lBQ3BDLE1BQU0sSUFBSUUsTUFBTTtBQUNsQjtBQUVBLDZDQUE2QztBQUN0QyxNQUFNQyxXQUFXUiwwRkFBMkJBLENBQUM7SUFDbERDO0lBQ0FRLGFBQWFKO0FBQ2YsR0FBRztBQUVILHdCQUF3QjtBQUNqQixNQUFNSyxjQUFjO0lBQ3pCQyxRQUFRLE9BQU9DLE9BQWVDO1FBQzVCLE1BQU1DLGNBQWMsQ0FBQyxFQUFFQyxPQUFPQyxRQUFRLENBQUNDLE1BQU0sQ0FBQyxDQUFDLENBQUM7UUFFaEQsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1YLFNBQVNZLElBQUksQ0FBQ1QsTUFBTSxDQUFDO1lBQ2pEQztZQUNBQztZQUNBUSxTQUFTO2dCQUNQQyxpQkFBaUJSO1lBQ25CO1FBQ0Y7UUFFQSxPQUFPO1lBQUVJO1lBQU1DO1FBQU07SUFDdkI7SUFFQUksUUFBUSxPQUFPWCxPQUFlQztRQUM1QixNQUFNLEVBQUVLLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTVgsU0FBU1ksSUFBSSxDQUFDSSxrQkFBa0IsQ0FBQztZQUM3RFo7WUFDQUM7UUFDRjtRQUVBLE9BQU87WUFBRUs7WUFBTUM7UUFBTTtJQUN2QjtJQUVBTSxrQkFBa0I7UUFDaEIsTUFBTVgsY0FBYyxDQUFDLEVBQUVDLE9BQU9DLFFBQVEsQ0FBQ0MsTUFBTSxDQUFDLENBQUMsQ0FBQztRQUVoRCxNQUFNLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTVgsU0FBU1ksSUFBSSxDQUFDTSxlQUFlLENBQUM7WUFDMURDLFVBQVU7WUFDVk4sU0FBUztnQkFDUE8sWUFBWWQ7Z0JBQ1plLGFBQWE7b0JBQ1hDLGFBQWE7b0JBQ2JDLFFBQVE7Z0JBQ1Y7Z0JBQ0FDLHFCQUFxQixNQUFNLDhCQUE4QjtZQUMzRDtRQUNGO1FBRUEsT0FBTztZQUFFZDtZQUFNQztRQUFNO0lBQ3ZCO0lBRUFjLHVCQUF1QjtRQUNyQixPQUFPLElBQUlDLFFBQVEsQ0FBQ0MsU0FBU0M7WUFDM0IsTUFBTXRCLGNBQWMsQ0FBQyxFQUFFQyxPQUFPQyxRQUFRLENBQUNDLE1BQU0sQ0FBQyxjQUFjLENBQUM7WUFFN0Qsc0JBQXNCO1lBQ3RCLE1BQU1vQixRQUFRdEIsT0FBT3VCLElBQUksQ0FDdkIsSUFDQSxlQUNBO1lBR0YsSUFBSSxDQUFDRCxPQUFPO2dCQUNWRCxPQUFPLElBQUk3QixNQUFNO2dCQUNqQjtZQUNGO1lBRUEsbUJBQW1CO1lBQ25CQyxTQUFTWSxJQUFJLENBQUNNLGVBQWUsQ0FBQztnQkFDNUJDLFVBQVU7Z0JBQ1ZOLFNBQVM7b0JBQ1BPLFlBQVlkO29CQUNaZSxhQUFhO3dCQUNYQyxhQUFhO3dCQUNiQyxRQUFRO29CQUNWO29CQUNBQyxxQkFBcUI7Z0JBQ3ZCO1lBQ0YsR0FBR08sSUFBSSxDQUFDLENBQUMsRUFBRXJCLElBQUksRUFBRUMsS0FBSyxFQUFFO2dCQUN0QixJQUFJQSxPQUFPO29CQUNUa0IsTUFBTUcsS0FBSztvQkFDWEosT0FBT2pCO29CQUNQO2dCQUNGO2dCQUVBLElBQUlELEtBQUt1QixHQUFHLEVBQUU7b0JBQ1pKLE1BQU1yQixRQUFRLENBQUMwQixJQUFJLEdBQUd4QixLQUFLdUIsR0FBRztnQkFDaEM7Z0JBRUEsNEJBQTRCO2dCQUM1QixNQUFNRSxrQkFBa0IsQ0FBQ0M7b0JBQ3ZCLElBQUlBLE1BQU0zQixNQUFNLEtBQUtGLE9BQU9DLFFBQVEsQ0FBQ0MsTUFBTSxFQUFFO29CQUU3QyxJQUFJMkIsTUFBTTFCLElBQUksQ0FBQzJCLElBQUksS0FBSyx5QkFBeUI7d0JBQy9DOUIsT0FBTytCLG1CQUFtQixDQUFDLFdBQVdIO3dCQUN0Q04sTUFBTUcsS0FBSzt3QkFDWEwsUUFBUTs0QkFBRWpCLE1BQU0wQixNQUFNMUIsSUFBSSxDQUFDNkIsT0FBTzs0QkFBRTVCLE9BQU87d0JBQUs7b0JBQ2xELE9BQU8sSUFBSXlCLE1BQU0xQixJQUFJLENBQUMyQixJQUFJLEtBQUssdUJBQXVCO3dCQUNwRDlCLE9BQU8rQixtQkFBbUIsQ0FBQyxXQUFXSDt3QkFDdENOLE1BQU1HLEtBQUs7d0JBQ1hKLE9BQU8sSUFBSTdCLE1BQU1xQyxNQUFNMUIsSUFBSSxDQUFDQyxLQUFLO29CQUNuQztnQkFDRjtnQkFFQUosT0FBT2lDLGdCQUFnQixDQUFDLFdBQVdMO2dCQUVuQyxvQ0FBb0M7Z0JBQ3BDLE1BQU1NLGNBQWNDLFlBQVk7b0JBQzlCLElBQUliLE1BQU1jLE1BQU0sRUFBRTt3QkFDaEJDLGNBQWNIO3dCQUNkbEMsT0FBTytCLG1CQUFtQixDQUFDLFdBQVdIO3dCQUN0Q1AsT0FBTyxJQUFJN0IsTUFBTTtvQkFDbkI7Z0JBQ0YsR0FBRztZQUNMO1FBQ0Y7SUFDRjtJQUVBOEMsU0FBUztRQUNQLE1BQU0sRUFBRWxDLEtBQUssRUFBRSxHQUFHLE1BQU1YLFNBQVNZLElBQUksQ0FBQ2lDLE9BQU87UUFDN0MsT0FBTztZQUFFbEM7UUFBTTtJQUNqQjtJQUVBbUMsZ0JBQWdCO1FBQ2QsTUFBTSxFQUFFcEMsTUFBTSxFQUFFcUMsSUFBSSxFQUFFLEVBQUVwQyxLQUFLLEVBQUUsR0FBRyxNQUFNWCxTQUFTWSxJQUFJLENBQUNvQyxPQUFPO1FBQzdELE9BQU87WUFBRUQ7WUFBTXBDO1FBQU07SUFDdkI7SUFFQXNDLG1CQUFtQixDQUFDQztRQUNsQixPQUFPbEQsU0FBU1ksSUFBSSxDQUFDcUMsaUJBQWlCLENBQUNDO0lBQ3pDO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL2J1aWxkZG9jd2l0aGFpLy4vc3JjL2xpYi9zdXBhYmFzZS50cz8wNmUxIl0sInNvdXJjZXNDb250ZW50IjpbIlxyXG5pbXBvcnQgeyBjcmVhdGVDbGllbnRDb21wb25lbnRDbGllbnQgfSBmcm9tICdAc3VwYWJhc2UvYXV0aC1oZWxwZXJzLW5leHRqcyc7XHJcblxyXG4vLyBFbnZpcm9ubWVudCB2YXJpYWJsZXMgdmFsaWRhdGlvblxyXG5jb25zdCBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTDtcclxuY29uc3Qgc3VwYWJhc2VBbm9uS2V5ID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVk7XHJcblxyXG5pZiAoIXN1cGFiYXNlVXJsIHx8ICFzdXBhYmFzZUFub25LZXkpIHtcclxuICB0aHJvdyBuZXcgRXJyb3IoJ01pc3NpbmcgU3VwYWJhc2UgZW52aXJvbm1lbnQgdmFyaWFibGVzLiBQbGVhc2UgY2hlY2sgeW91ciAuZW52LmxvY2FsIGZpbGUuJyk7XHJcbn1cclxuXHJcbi8vIFN1cGFiYXNlIGNsaWVudCBmb3IgY2xpZW50LXNpZGUgb3BlcmF0aW9uc1xyXG5leHBvcnQgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVDbGllbnRDb21wb25lbnRDbGllbnQoe1xyXG4gIHN1cGFiYXNlVXJsLFxyXG4gIHN1cGFiYXNlS2V5OiBzdXBhYmFzZUFub25LZXlcclxufSk7XHJcblxyXG4vLyBBdXRoIGhlbHBlciBmdW5jdGlvbnNcclxuZXhwb3J0IGNvbnN0IGF1dGhIZWxwZXJzID0ge1xyXG4gIHNpZ25VcDogYXN5bmMgKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcpID0+IHtcclxuICAgIGNvbnN0IHJlZGlyZWN0VXJsID0gYCR7d2luZG93LmxvY2F0aW9uLm9yaWdpbn0vYDtcclxuXHJcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25VcCh7XHJcbiAgICAgIGVtYWlsLFxyXG4gICAgICBwYXNzd29yZCxcclxuICAgICAgb3B0aW9uczoge1xyXG4gICAgICAgIGVtYWlsUmVkaXJlY3RUbzogcmVkaXJlY3RVcmxcclxuICAgICAgfVxyXG4gICAgfSk7XHJcblxyXG4gICAgcmV0dXJuIHsgZGF0YSwgZXJyb3IgfTtcclxuICB9LFxyXG5cclxuICBzaWduSW46IGFzeW5jIChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKSA9PiB7XHJcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25JbldpdGhQYXNzd29yZCh7XHJcbiAgICAgIGVtYWlsLFxyXG4gICAgICBwYXNzd29yZCxcclxuICAgIH0pO1xyXG5cclxuICAgIHJldHVybiB7IGRhdGEsIGVycm9yIH07XHJcbiAgfSxcclxuXHJcbiAgc2lnbkluV2l0aEdvb2dsZTogYXN5bmMgKCkgPT4ge1xyXG4gICAgY29uc3QgcmVkaXJlY3RVcmwgPSBgJHt3aW5kb3cubG9jYXRpb24ub3JpZ2lufS9gO1xyXG5cclxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguc2lnbkluV2l0aE9BdXRoKHtcclxuICAgICAgcHJvdmlkZXI6ICdnb29nbGUnLFxyXG4gICAgICBvcHRpb25zOiB7XHJcbiAgICAgICAgcmVkaXJlY3RUbzogcmVkaXJlY3RVcmwsXHJcbiAgICAgICAgcXVlcnlQYXJhbXM6IHtcclxuICAgICAgICAgIGFjY2Vzc190eXBlOiAnb2ZmbGluZScsXHJcbiAgICAgICAgICBwcm9tcHQ6ICdjb25zZW50JyxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHNraXBCcm93c2VyUmVkaXJlY3Q6IGZhbHNlIC8vIFdlJ2xsIGhhbmRsZSBwb3B1cCBtYW51YWxseVxyXG4gICAgICB9XHJcbiAgICB9KTtcclxuXHJcbiAgICByZXR1cm4geyBkYXRhLCBlcnJvciB9O1xyXG4gIH0sXHJcblxyXG4gIHNpZ25JbldpdGhHb29nbGVQb3B1cDogYXN5bmMgKCkgPT4ge1xyXG4gICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcclxuICAgICAgY29uc3QgcmVkaXJlY3RVcmwgPSBgJHt3aW5kb3cubG9jYXRpb24ub3JpZ2lufS9hdXRoL2NhbGxiYWNrYDtcclxuXHJcbiAgICAgIC8vIENyZWF0ZSBwb3B1cCB3aW5kb3dcclxuICAgICAgY29uc3QgcG9wdXAgPSB3aW5kb3cub3BlbihcclxuICAgICAgICAnJyxcclxuICAgICAgICAnZ29vZ2xlLWF1dGgnLFxyXG4gICAgICAgICd3aWR0aD01MDAsaGVpZ2h0PTYwMCxzY3JvbGxiYXJzPXllcyxyZXNpemFibGU9eWVzJ1xyXG4gICAgICApO1xyXG5cclxuICAgICAgaWYgKCFwb3B1cCkge1xyXG4gICAgICAgIHJlamVjdChuZXcgRXJyb3IoJ1BvcHVwIGJsb2NrZWQnKSk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBTdGFydCBPQXV0aCBmbG93XHJcbiAgICAgIHN1cGFiYXNlLmF1dGguc2lnbkluV2l0aE9BdXRoKHtcclxuICAgICAgICBwcm92aWRlcjogJ2dvb2dsZScsXHJcbiAgICAgICAgb3B0aW9uczoge1xyXG4gICAgICAgICAgcmVkaXJlY3RUbzogcmVkaXJlY3RVcmwsXHJcbiAgICAgICAgICBxdWVyeVBhcmFtczoge1xyXG4gICAgICAgICAgICBhY2Nlc3NfdHlwZTogJ29mZmxpbmUnLFxyXG4gICAgICAgICAgICBwcm9tcHQ6ICdjb25zZW50JyxcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgICBza2lwQnJvd3NlclJlZGlyZWN0OiB0cnVlXHJcbiAgICAgICAgfVxyXG4gICAgICB9KS50aGVuKCh7IGRhdGEsIGVycm9yIH0pID0+IHtcclxuICAgICAgICBpZiAoZXJyb3IpIHtcclxuICAgICAgICAgIHBvcHVwLmNsb3NlKCk7XHJcbiAgICAgICAgICByZWplY3QoZXJyb3IpO1xyXG4gICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKGRhdGEudXJsKSB7XHJcbiAgICAgICAgICBwb3B1cC5sb2NhdGlvbi5ocmVmID0gZGF0YS51cmw7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBMaXN0ZW4gZm9yIHBvcHVwIG1lc3NhZ2VzXHJcbiAgICAgICAgY29uc3QgbWVzc2FnZUxpc3RlbmVyID0gKGV2ZW50OiBNZXNzYWdlRXZlbnQpID0+IHtcclxuICAgICAgICAgIGlmIChldmVudC5vcmlnaW4gIT09IHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4pIHJldHVybjtcclxuXHJcbiAgICAgICAgICBpZiAoZXZlbnQuZGF0YS50eXBlID09PSAnU1VQQUJBU0VfQVVUSF9TVUNDRVNTJykge1xyXG4gICAgICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignbWVzc2FnZScsIG1lc3NhZ2VMaXN0ZW5lcik7XHJcbiAgICAgICAgICAgIHBvcHVwLmNsb3NlKCk7XHJcbiAgICAgICAgICAgIHJlc29sdmUoeyBkYXRhOiBldmVudC5kYXRhLnNlc3Npb24sIGVycm9yOiBudWxsIH0pO1xyXG4gICAgICAgICAgfSBlbHNlIGlmIChldmVudC5kYXRhLnR5cGUgPT09ICdTVVBBQkFTRV9BVVRIX0VSUk9SJykge1xyXG4gICAgICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignbWVzc2FnZScsIG1lc3NhZ2VMaXN0ZW5lcik7XHJcbiAgICAgICAgICAgIHBvcHVwLmNsb3NlKCk7XHJcbiAgICAgICAgICAgIHJlamVjdChuZXcgRXJyb3IoZXZlbnQuZGF0YS5lcnJvcikpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH07XHJcblxyXG4gICAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdtZXNzYWdlJywgbWVzc2FnZUxpc3RlbmVyKTtcclxuXHJcbiAgICAgICAgLy8gQ2hlY2sgaWYgcG9wdXAgaXMgY2xvc2VkIG1hbnVhbGx5XHJcbiAgICAgICAgY29uc3QgY2hlY2tDbG9zZWQgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XHJcbiAgICAgICAgICBpZiAocG9wdXAuY2xvc2VkKSB7XHJcbiAgICAgICAgICAgIGNsZWFySW50ZXJ2YWwoY2hlY2tDbG9zZWQpO1xyXG4gICAgICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignbWVzc2FnZScsIG1lc3NhZ2VMaXN0ZW5lcik7XHJcbiAgICAgICAgICAgIHJlamVjdChuZXcgRXJyb3IoJ0F1dGhlbnRpY2F0aW9uIGNhbmNlbGxlZCcpKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9LCAxMDAwKTtcclxuICAgICAgfSk7XHJcbiAgICB9KTtcclxuICB9LFxyXG5cclxuICBzaWduT3V0OiBhc3luYyAoKSA9PiB7XHJcbiAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25PdXQoKTtcclxuICAgIHJldHVybiB7IGVycm9yIH07XHJcbiAgfSxcclxuXHJcbiAgZ2V0Q3VycmVudFVzZXI6IGFzeW5jICgpID0+IHtcclxuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0sIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcclxuICAgIHJldHVybiB7IHVzZXIsIGVycm9yIH07XHJcbiAgfSxcclxuXHJcbiAgb25BdXRoU3RhdGVDaGFuZ2U6IChjYWxsYmFjazogKGV2ZW50OiBzdHJpbmcsIHNlc3Npb246IGFueSkgPT4gdm9pZCkgPT4ge1xyXG4gICAgcmV0dXJuIHN1cGFiYXNlLmF1dGgub25BdXRoU3RhdGVDaGFuZ2UoY2FsbGJhY2spO1xyXG4gIH1cclxufTtcclxuIl0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudENvbXBvbmVudENsaWVudCIsInN1cGFiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsInN1cGFiYXNlQW5vbktleSIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwiRXJyb3IiLCJzdXBhYmFzZSIsInN1cGFiYXNlS2V5IiwiYXV0aEhlbHBlcnMiLCJzaWduVXAiLCJlbWFpbCIsInBhc3N3b3JkIiwicmVkaXJlY3RVcmwiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsIm9yaWdpbiIsImRhdGEiLCJlcnJvciIsImF1dGgiLCJvcHRpb25zIiwiZW1haWxSZWRpcmVjdFRvIiwic2lnbkluIiwic2lnbkluV2l0aFBhc3N3b3JkIiwic2lnbkluV2l0aEdvb2dsZSIsInNpZ25JbldpdGhPQXV0aCIsInByb3ZpZGVyIiwicmVkaXJlY3RUbyIsInF1ZXJ5UGFyYW1zIiwiYWNjZXNzX3R5cGUiLCJwcm9tcHQiLCJza2lwQnJvd3NlclJlZGlyZWN0Iiwic2lnbkluV2l0aEdvb2dsZVBvcHVwIiwiUHJvbWlzZSIsInJlc29sdmUiLCJyZWplY3QiLCJwb3B1cCIsIm9wZW4iLCJ0aGVuIiwiY2xvc2UiLCJ1cmwiLCJocmVmIiwibWVzc2FnZUxpc3RlbmVyIiwiZXZlbnQiLCJ0eXBlIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsInNlc3Npb24iLCJhZGRFdmVudExpc3RlbmVyIiwiY2hlY2tDbG9zZWQiLCJzZXRJbnRlcnZhbCIsImNsb3NlZCIsImNsZWFySW50ZXJ2YWwiLCJzaWduT3V0IiwiZ2V0Q3VycmVudFVzZXIiLCJ1c2VyIiwiZ2V0VXNlciIsIm9uQXV0aFN0YXRlQ2hhbmdlIiwiY2FsbGJhY2siXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL2J1aWxkZG9jd2l0aGFpLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"916782a38227\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVpbGRkb2N3aXRoYWkvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzQ3MmUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5MTY3ODJhMzgyMjdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_dashboard_Sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/dashboard/Sidebar */ \"(rsc)/./src/components/dashboard/Sidebar.tsx\");\n\n\nconst metadata = {\n    title: \"لوحة التحكم - BuildDocwithai\",\n    description: \"لوحة تحكم BuildDocwithai لإدارة مستنداتك ومشاريعك\",\n    robots: {\n        index: false,\n        follow: false\n    }\n};\nfunction DashboardLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-slate-900 to-slate-800\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_Sidebar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:pr-80 min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"p-4 sm:p-6 lg:p-8 max-w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/projects/create/audio-upload/page.tsx":
/*!*****************************************************************!*\
  !*** ./src/app/dashboard/projects/create/audio-upload/page.tsx ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\سطح المكتب\builddocwithai\src\app\dashboard\projects\create\audio-upload\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_fonts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/fonts */ \"(rsc)/./src/lib/fonts.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: \"BuildDocwithai - أنشئ مستندات عملك بالذكاء الاصطناعي\",\n    description: \"منصة متطورة تساعدك في إنشاء خطط العمل ودراسات الجدوى والعروض التقديمية في دقائق معدودة\",\n    keywords: [\n        \"ذكاء اصطناعي\",\n        \"مستندات\",\n        \"خطط عمل\",\n        \"دراسات جدوى\",\n        \"عروض تقديمية\"\n    ],\n    authors: [\n        {\n            name: \"BuildDocwithai Team\"\n        }\n    ],\n    creator: \"BuildDocwithai\",\n    publisher: \"BuildDocwithai\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"https://builddocwithai.com\"),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        title: \"BuildDocwithai - أنشئ مستندات عملك بالذكاء الاصطناعي\",\n        description: \"منصة متطورة تساعدك في إنشاء خطط العمل ودراسات الجدوى والعروض التقديمية في دقائق معدودة\",\n        url: \"https://builddocwithai.com\",\n        siteName: \"BuildDocwithai\",\n        locale: \"ar_SA\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"BuildDocwithai - أنشئ مستندات عملك بالذكاء الاصطناعي\",\n        description: \"منصة متطورة تساعدك في إنشاء خطط العمل ودراسات الجدوى والعروض التقديمية في دقائق معدودة\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        className: _lib_fonts__WEBPACK_IMPORTED_MODULE_1__.vazirmatnFont.variable,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Vazirmatn:wght@100..900&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${_lib_fonts__WEBPACK_IMPORTED_MODULE_1__.vazirmatnFont.className} antialiased bg-slate-900 text-white`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: [\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                            position: \"bottom-right\",\n                            richColors: true,\n                            theme: \"dark\",\n                            toastOptions: {\n                                style: {\n                                    fontFamily: \"var(--font-vazirmatn)\",\n                                    direction: \"rtl\",\n                                    textAlign: \"right\"\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(rsc)/./src/components/ui/Button.tsx\");\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-slate-900 flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-6xl font-bold text-indigo-500 mb-4\",\n                    children: \"404\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold text-white mb-4\",\n                    children: \"الصفحة غير موجودة\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-slate-400 mb-8 max-w-md mx-auto\",\n                    children: \"عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    href: \"/\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"gradient\",\n                        size: \"lg\",\n                        children: \"العودة للرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\not-found.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL25vdC1mb3VuZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTRCO0FBQ21CO0FBRWhDLFNBQVNFO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQTBDOzs7Ozs7OEJBQ3pELDhEQUFDQztvQkFBR0QsV0FBVTs4QkFBcUM7Ozs7Ozs4QkFDbkQsOERBQUNFO29CQUFFRixXQUFVOzhCQUF1Qzs7Ozs7OzhCQUdwRCw4REFBQ0osaURBQUlBO29CQUFDTyxNQUFLOzhCQUNULDRFQUFDTix5REFBTUE7d0JBQUNPLFNBQVE7d0JBQVdDLE1BQUs7a0NBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPL0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idWlsZGRvY3dpdGhhaS8uL3NyYy9hcHAvbm90LWZvdW5kLnRzeD9jYWUyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9CdXR0b24nXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE5vdEZvdW5kKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLXNsYXRlLTkwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBweC00XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC02eGwgZm9udC1ib2xkIHRleHQtaW5kaWdvLTUwMCBtYi00XCI+NDA0PC9kaXY+XG4gICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+2KfZhNi12YHYrdipINi62YrYsSDZhdmI2KzZiNiv2Kk8L2gxPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTQwMCBtYi04IG1heC13LW1kIG14LWF1dG9cIj5cbiAgICAgICAgICDYudiw2LHYp9mL2Iwg2KfZhNi12YHYrdipINin2YTYqtmKINiq2KjYrdirINi52YbZh9inINi62YrYsSDZhdmI2KzZiNiv2Kkg2KPZiCDYqtmFINmG2YLZhNmH2Kcg2KXZhNmJINmF2YjZgti5INii2K7YsS5cbiAgICAgICAgPC9wPlxuICAgICAgICA8TGluayBocmVmPVwiL1wiPlxuICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImdyYWRpZW50XCIgc2l6ZT1cImxnXCI+XG4gICAgICAgICAgICDYp9mE2LnZiNiv2Kkg2YTZhNix2KbZitiz2YrYqVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L0xpbms+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkxpbmsiLCJCdXR0b24iLCJOb3RGb3VuZCIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwicCIsImhyZWYiLCJ2YXJpYW50Iiwic2l6ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/dashboard/Sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/Sidebar.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\سطح المكتب\builddocwithai\src\components\dashboard\Sidebar.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            primary: \"bg-indigo-500 text-white hover:bg-indigo-600 shadow-lg hover:shadow-xl\",\n            secondary: \"bg-transparent border-2 border-white/20 text-white hover:border-white/40 hover:bg-white/5\",\n            outline: \"border border-slate-600 bg-transparent hover:bg-slate-800 hover:text-white\",\n            ghost: \"hover:bg-slate-800 hover:text-white\",\n            link: \"text-indigo-400 underline-offset-4 hover:underline\",\n            gradient: \"bg-gradient-to-r from-indigo-500 to-violet-500 text-white hover:from-indigo-600 hover:to-violet-600 shadow-lg hover:shadow-xl\"\n        },\n        size: {\n            default: \"h-12 px-6 py-3\",\n            sm: \"h-9 px-4 py-2 text-xs\",\n            lg: \"h-14 px-8 py-4 text-base\",\n            xl: \"h-16 px-10 py-5 text-lg\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"primary\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 41,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e1),
/* harmony export */   useAuth: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\سطح المكتب\builddocwithai\src\contexts\AuthContext.tsx#useAuth`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\سطح المكتب\builddocwithai\src\contexts\AuthContext.tsx#AuthProvider`);


/***/ }),

/***/ "(rsc)/./src/lib/fonts.ts":
/*!**************************!*\
  !*** ./src/lib/fonts.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   vazirmatnFont: () => (/* binding */ vazirmatnFont)\n/* harmony export */ });\n// Vazirmatn font for Arabic text (loaded via CSS)\nconst vazirmatnFont = {\n    className: \"font-vazirmatn\",\n    variable: \"--font-vazirmatn\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2ZvbnRzLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFDQSxrREFBa0Q7QUFDM0MsTUFBTUEsZ0JBQWdCO0lBQzNCQyxXQUFXO0lBQ1hDLFVBQVU7QUFDWixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVpbGRkb2N3aXRoYWkvLi9zcmMvbGliL2ZvbnRzLnRzPzY2ZGQiXSwic291cmNlc0NvbnRlbnQiOlsiXHJcbi8vIFZhemlybWF0biBmb250IGZvciBBcmFiaWMgdGV4dCAobG9hZGVkIHZpYSBDU1MpXHJcbmV4cG9ydCBjb25zdCB2YXppcm1hdG5Gb250ID0ge1xyXG4gIGNsYXNzTmFtZTogJ2ZvbnQtdmF6aXJtYXRuJyxcclxuICB2YXJpYWJsZTogJy0tZm9udC12YXppcm1hdG4nLFxyXG59XHJcbiJdLCJuYW1lcyI6WyJ2YXppcm1hdG5Gb250IiwiY2xhc3NOYW1lIiwidmFyaWFibGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/fonts.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL2J1aWxkZG9jd2l0aGFpLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/framer-motion","vendor-chunks/tr46","vendor-chunks/tailwind-merge","vendor-chunks/motion-dom","vendor-chunks/ws","vendor-chunks/sonner","vendor-chunks/lucide-react","vendor-chunks/whatwg-url","vendor-chunks/motion-utils","vendor-chunks/set-cookie-parser","vendor-chunks/class-variance-authority","vendor-chunks/webidl-conversions","vendor-chunks/jose","vendor-chunks/isows","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fprojects%2Fcreate%2Faudio-upload%2Fpage&page=%2Fdashboard%2Fprojects%2Fcreate%2Faudio-upload%2Fpage&appPaths=%2Fdashboard%2Fprojects%2Fcreate%2Faudio-upload%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fprojects%2Fcreate%2Faudio-upload%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmoham%5COneDrive%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5Cbuilddocwithai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmoham%5COneDrive%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5Cbuilddocwithai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();