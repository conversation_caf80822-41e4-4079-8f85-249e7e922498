'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { Menu, X } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import UserDropdown from './UserDropdown'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const { user, loading } = useAuth()

  // Dynamic navigation based on authentication status
  const navigation = user ? [
    { name: 'لوحة التحكم', href: '/dashboard' },
    { name: 'مشاريعي', href: '/dashboard/projects' },
    { name: 'الرئيسية', href: '/' },
    { name: 'المميزات', href: '#features' },
  ] : [
    { name: 'الرئيسية', href: '/' },
    { name: 'المميزات', href: '#features' },
    { name: 'القوالب', href: '#templates' },
    { name: 'الأسعار', href: '#pricing' },
    { name: 'تواصل معنا', href: '#contact' },
  ]

  return (
    <header className="fixed top-0 w-full z-50 bg-slate-900/80 backdrop-blur-md border-b border-slate-800">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="flex items-center space-x-2 rtl:space-x-reverse">
              <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-violet-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">BD</span>
              </div>
              <span className="text-xl font-bold text-white">BuildDocwithai</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8 rtl:space-x-reverse">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-slate-300 hover:text-white transition-colors duration-200 font-medium"
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center">
            {loading ? (
              <div className="w-8 h-8 bg-slate-700 rounded-full animate-pulse"></div>
            ) : user ? (
              <UserDropdown />
            ) : (
              <Link href="/auth/login">
                <Button variant="primary" size="sm" className="font-vazirmatn">
                  تسجيل الدخول
                </Button>
              </Link>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-slate-300 hover:text-white transition-colors duration-200"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-slate-800">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="block px-3 py-2 text-slate-300 hover:text-white hover:bg-slate-800 rounded-md transition-colors duration-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
              
              <div className="border-t border-slate-800 pt-4 mt-4">
                <div className="px-3 py-2">
                  {loading ? (
                    <div className="w-full h-10 bg-slate-700 rounded-lg animate-pulse"></div>
                  ) : user ? (
                    <div className="w-full">
                      <UserDropdown />
                    </div>
                  ) : (
                    <Link href="/auth/login">
                      <Button variant="primary" size="sm" className="w-full font-vazirmatn">
                        تسجيل الدخول
                      </Button>
                    </Link>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
