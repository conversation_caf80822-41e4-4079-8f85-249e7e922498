'use client'

import { motion } from 'framer-motion'
import { Plus, Edit, Save, Download, Clock } from 'lucide-react'
import { RecentActivity as RecentActivityType } from '@/lib/dashboard-data'

interface RecentActivityProps {
  activities: RecentActivityType[]
}

const iconMap = {
  Plus,
  Edit,
  Save,
  Download
}

const getActivityColor = (type: string) => {
  switch (type) {
    case 'created':
      return 'bg-green-500'
    case 'edited':
      return 'bg-blue-500'
    case 'saved':
      return 'bg-yellow-500'
    case 'downloaded':
      return 'bg-purple-500'
    default:
      return 'bg-slate-500'
  }
}

export default function RecentActivity({ activities }: RecentActivityProps) {
  return (
    <div className="bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-xl">
      {/* Header */}
      <div className="flex items-center space-x-3 rtl:space-x-reverse mb-6">
        <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
          <Clock className="w-4 h-4 text-white" />
        </div>
        <h2 className="text-xl font-semibold text-white font-vazirmatn">
          النشاط الأخير
        </h2>
        <div className="flex-1 h-px bg-gradient-to-l from-slate-700 to-transparent"></div>
      </div>

      {/* Activity Timeline */}
      <div className="space-y-4">
        {activities.map((activity, index) => {
          const Icon = iconMap[activity.icon as keyof typeof iconMap] || Plus
          
          return (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              className="flex items-start space-x-4 rtl:space-x-reverse group"
            >
              {/* Timeline Icon */}
              <div className="flex-shrink-0 relative">
                <div className={`w-8 h-8 ${getActivityColor(activity.type)} rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-200`}>
                  <Icon className="w-4 h-4 text-white" />
                </div>
                {index < activities.length - 1 && (
                  <div className="absolute top-8 right-3.5 rtl:left-3.5 rtl:right-auto w-0.5 h-6 bg-slate-600"></div>
                )}
              </div>

              {/* Activity Content */}
              <div className="flex-1 min-w-0 pb-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-slate-300 font-vazirmatn">
                    <span className="font-medium">{activity.description}</span>
                    {' '}
                    <button className="text-indigo-400 hover:text-indigo-300 transition-colors duration-200 hover:underline">
                      {activity.documentName}
                    </button>
                  </p>
                  <span className="text-xs text-slate-500 font-vazirmatn whitespace-nowrap mr-4 rtl:ml-4 rtl:mr-0">
                    {activity.timestamp}
                  </span>
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* View All Button */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="mt-6 pt-4 border-t border-slate-700"
      >
        <button className="w-full text-center text-sm text-indigo-400 hover:text-indigo-300 font-vazirmatn transition-colors duration-200">
          عرض جميع الأنشطة
        </button>
      </motion.div>
    </div>
  )
}
