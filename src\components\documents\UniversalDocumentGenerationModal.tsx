'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Play, Pause, RotateCcw, CheckCircle, Clock, AlertCircle, FileText, ArrowRight } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { DocumentGenerationProgress, DocumentSection } from '@/types/document-generation'
import { useRouter } from 'next/navigation'

interface UniversalDocumentGenerationModalProps {
  isOpen: boolean
  onClose: () => void
  onComplete: (documentId: string) => void
  documentId: string
  documentTitle: string
  templateTitle: string
  projectName: string
  tier: 'quick' | 'professional'
}

export default function UniversalDocumentGenerationModal({
  isOpen,
  onClose,
  onComplete,
  documentId,
  documentTitle,
  templateTitle,
  projectName,
  tier
}: UniversalDocumentGenerationModalProps) {
  const router = useRouter()
  const [progress, setProgress] = useState<DocumentGenerationProgress>({
    documentId,
    currentSection: 0,
    totalSections: 0,
    progress: 0,
    status: 'generating',
    sections: []
  })
  const [isPaused, setIsPaused] = useState(false)

  // Simulate document generation progress
  useEffect(() => {
    if (!isOpen || isPaused) return

    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev.status === 'completed' || prev.status === 'error') {
          return prev
        }

        const newSections = prev.sections.map((section, index) => {
          if (index < prev.currentSection) {
            return { ...section, status: 'completed' as const }
          } else if (index === prev.currentSection) {
            return { ...section, status: 'generating' as const }
          } else {
            return { ...section, status: 'queued' as const }
          }
        })

        const newProgress = Math.min(((prev.currentSection + 0.5) / prev.totalSections) * 100, 100)
        const isCompleted = prev.currentSection >= prev.totalSections - 1 && newProgress >= 95

        if (isCompleted) {
          return {
            ...prev,
            progress: 100,
            status: 'completed',
            sections: newSections.map(s => ({ ...s, status: 'completed' as const }))
          }
        }

        return {
          ...prev,
          currentSection: Math.min(prev.currentSection + (Math.random() > 0.7 ? 1 : 0), prev.totalSections - 1),
          progress: newProgress,
          sections: newSections
        }
      })
    }, 2000 + Math.random() * 3000) // Random interval between 2-5 seconds

    return () => clearInterval(interval)
  }, [isOpen, isPaused])

  // Initialize sections based on template and tier
  useEffect(() => {
    if (isOpen && progress.sections.length === 0) {
      // Get sections based on tier
      const mockSections: DocumentSection[] = tier === 'professional' ? [
        { id: 'executive-summary', title: 'الملخص التنفيذي', content: '', isLocked: false, order: 1, status: 'queued', estimatedTime: 180 },
        { id: 'company-overview', title: 'نظرة عامة على الشركة ونموذج العمل', content: '', isLocked: false, order: 2, status: 'queued', estimatedTime: 240 },
        { id: 'market-overview', title: 'نظرة عامة على السوق', content: '', isLocked: false, order: 3, status: 'queued', estimatedTime: 300 },
        { id: 'market-demand', title: 'الطلب في السوق', content: '', isLocked: false, order: 4, status: 'queued', estimatedTime: 240 },
        { id: 'market-supply', title: 'العرض في السوق', content: '', isLocked: false, order: 5, status: 'queued', estimatedTime: 240 },
        { id: 'supply-demand-gap', title: 'فجوة العرض والطلب', content: '', isLocked: false, order: 6, status: 'queued', estimatedTime: 180 },
        { id: 'swot-analysis', title: 'تحليل SWOT', content: '', isLocked: false, order: 7, status: 'queued', estimatedTime: 300 },
        { id: 'market-penetration', title: 'خطة اختراق السوق', content: '', isLocked: false, order: 8, status: 'queued', estimatedTime: 360 },
        { id: 'technical-analysis', title: 'التحليل الفني وتحليل الموارد', content: '', isLocked: false, order: 9, status: 'queued', estimatedTime: 300 },
        { id: 'workforce-assessment', title: 'تقييم القوى العاملة', content: '', isLocked: false, order: 10, status: 'queued', estimatedTime: 240 },
        { id: 'investment-requirements', title: 'متطلبات الاستثمار', content: '', isLocked: false, order: 11, status: 'queued', estimatedTime: 180 },
        { id: 'investment-costs', title: 'تكلفة الاستثمار', content: '', isLocked: false, order: 12, status: 'queued', estimatedTime: 240 },
        { id: 'financing-plan', title: 'خطة التمويل', content: '', isLocked: false, order: 13, status: 'queued', estimatedTime: 300 },
        { id: 'financial-assumptions', title: 'الافتراضات والمبررات المالية', content: '', isLocked: false, order: 14, status: 'queued', estimatedTime: 240 },
        { id: 'financial-statements', title: 'البيانات المالية', content: '', isLocked: false, order: 15, status: 'queued', estimatedTime: 360 },
        { id: 'break-even-analysis', title: 'تحليل نقطة التعادل', content: '', isLocked: false, order: 16, status: 'queued', estimatedTime: 180 },
        { id: 'multiple-analysis', title: 'تحليل متعدد', content: '', isLocked: false, order: 17, status: 'queued', estimatedTime: 240 },
        { id: 'active-business-plan', title: 'خطة العمل النشطة', content: '', isLocked: false, order: 18, status: 'queued', estimatedTime: 300 },
        { id: 'appendix', title: 'الملحق', content: '', isLocked: false, order: 19, status: 'queued', estimatedTime: 120 }
      ] : [
        { id: 'executive-summary', title: 'الملخص التنفيذي', content: '', isLocked: false, order: 1, status: 'queued', estimatedTime: 120 },
        { id: 'company-overview', title: 'نظرة عامة على الشركة ونموذج العمل', content: '', isLocked: false, order: 2, status: 'queued', estimatedTime: 180 },
        { id: 'market-overview', title: 'نظرة عامة على السوق', content: '', isLocked: false, order: 3, status: 'queued', estimatedTime: 180 },
        { id: 'swot-analysis', title: 'تحليل SWOT', content: '', isLocked: false, order: 4, status: 'queued', estimatedTime: 180 },
        { id: 'financial-statements', title: 'البيانات المالية', content: '', isLocked: false, order: 5, status: 'queued', estimatedTime: 240 },
        { id: 'multiple-analysis', title: 'تحليل متعدد', content: '', isLocked: false, order: 6, status: 'queued', estimatedTime: 120 }
      ]

      setProgress(prev => ({
        ...prev,
        sections: mockSections,
        totalSections: mockSections.length
      }))
    }
  }, [isOpen, tier])

  const handlePauseResume = () => {
    setIsPaused(!isPaused)
  }

  const handleRetry = () => {
    setProgress(prev => ({
      ...prev,
      currentSection: 0,
      progress: 0,
      status: 'generating',
      sections: prev.sections.map(s => ({ ...s, status: 'queued' as const }))
    }))
    setIsPaused(false)
  }

  const handleComplete = () => {
    // Navigate directly to the document view page
    router.push(`/dashboard/documents/${documentId}`)
    onClose()
  }

  const getStatusIcon = (status: DocumentSection['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-400" />
      case 'generating':
        return <div className="w-5 h-5 animate-spin rounded-full border-2 border-indigo-500 border-t-transparent" />
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-400" />
      default:
        return <Clock className="w-5 h-5 text-slate-500" />
    }
  }

  const getStatusColor = (status: DocumentSection['status']) => {
    switch (status) {
      case 'completed':
        return 'border-green-500/30 bg-green-500/10'
      case 'generating':
        return 'border-indigo-500/50 bg-indigo-500/20'
      case 'error':
        return 'border-red-500/30 bg-red-500/10'
      default:
        return 'border-slate-600/30 bg-slate-700/20'
    }
  }

  const completedSections = progress.sections.filter(s => s.status === 'completed').length
  const remainingSections = progress.totalSections - completedSections

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.3 }}
          className="bg-gradient-to-br from-slate-800 via-slate-800 to-slate-900 border border-slate-700 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="relative p-6 border-b border-slate-700/50 bg-gradient-to-r from-slate-800/50 to-slate-900/50">
            {/* Progress Bar */}
            <div className="absolute top-0 left-0 right-0 h-1 bg-slate-700">
              <motion.div
                className="h-full bg-gradient-to-r from-indigo-500 to-purple-500"
                initial={{ width: 0 }}
                animate={{ width: `${progress.progress}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center shadow-lg">
                  <FileText className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white font-vazirmatn">
                    إنشاء المستند
                  </h2>
                  <p className="text-slate-400 font-vazirmatn">
                    {documentTitle}
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-slate-400 hover:text-white transition-colors duration-200 p-2 hover:bg-slate-700/50 rounded-lg"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            {/* Progress Stats */}
            <div className="mt-6 grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="bg-slate-700/30 rounded-xl p-4">
                <div className="text-slate-400 font-vazirmatn text-sm">التقدم</div>
                <div className="text-2xl font-bold text-white font-vazirmatn">
                  {Math.round(progress.progress)}%
                </div>
              </div>
              <div className="bg-slate-700/30 rounded-xl p-4">
                <div className="text-slate-400 font-vazirmatn text-sm">الأقسام المكتملة</div>
                <div className="text-2xl font-bold text-white font-vazirmatn">
                  {progress.sections.filter(s => s.status === 'completed').length} من {progress.totalSections}
                </div>
              </div>
              <div className="bg-slate-700/30 rounded-xl p-4">
                <div className="text-slate-400 font-vazirmatn text-sm">القسم الحالي</div>
                <div className="text-lg font-bold text-white font-vazirmatn">
                  {progress.currentSection < progress.totalSections
                    ? progress.sections[progress.currentSection]?.title || 'جاري التحضير...'
                    : 'اكتمل الإنشاء'
                  }
                </div>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto" style={{ maxHeight: 'calc(90vh - 300px)' }}>
            <div className="space-y-4">
              {progress.sections.map((section, index) => (
                <motion.div
                  key={section.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`p-4 rounded-xl border transition-all duration-300 ${getStatusColor(section.status)}`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(section.status)}
                      <div>
                        <h3 className="font-bold text-white font-vazirmatn">
                          {section.title}
                        </h3>
                        <p className="text-slate-400 font-vazirmatn text-sm">
                          القسم {index + 1} من {progress.totalSections}
                        </p>
                      </div>
                    </div>
                    <div className="text-slate-400 font-vazirmatn text-sm">
                      {section.estimatedTime && `${Math.floor(section.estimatedTime / 60)} دقيقة`}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Footer */}
          <div className="p-6 border-t border-slate-700/50 bg-slate-800/30">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {progress.status === 'generating' && !isPaused && (
                  <Button
                    onClick={handlePauseResume}
                    variant="ghost"
                    className="text-amber-400 hover:text-amber-300 hover:bg-amber-500/10 font-vazirmatn"
                  >
                    <Pause className="w-4 h-4 ml-2" />
                    إيقاف مؤقت
                  </Button>
                )}
                {isPaused && (
                  <Button
                    onClick={handlePauseResume}
                    variant="ghost"
                    className="text-green-400 hover:text-green-300 hover:bg-green-500/10 font-vazirmatn"
                  >
                    <Play className="w-4 h-4 ml-2" />
                    متابعة
                  </Button>
                )}
                {progress.status === 'error' && (
                  <Button
                    onClick={handleRetry}
                    variant="ghost"
                    className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10 font-vazirmatn"
                  >
                    <RotateCcw className="w-4 h-4 ml-2" />
                    إعادة المحاولة
                  </Button>
                )}
              </div>

              {progress.status === 'completed' && (
                <Button
                  onClick={handleComplete}
                  className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 font-vazirmatn px-8"
                >
                  اكتمل إنشاء المستند
                  <ArrowRight className="w-4 h-4 mr-2" />
                </Button>
              )}
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
