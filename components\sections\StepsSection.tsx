'use client'

import { useTranslations } from 'next-intl'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { MousePointer, MessageSquare, Download } from 'lucide-react'
import { motion } from 'framer-motion'

export default function StepsSection() {
  const t = useTranslations()

  const steps = [
    {
      number: '01',
      icon: MousePointer,
      title: t('steps.step1.title'),
      description: t('steps.step1.description'),
      color: 'from-indigo-500 to-blue-500',
    },
    {
      number: '02',
      icon: MessageSquare,
      title: t('steps.step2.title'),
      description: t('steps.step2.description'),
      color: 'from-violet-500 to-purple-500',
    },
    {
      number: '03',
      icon: Download,
      title: t('steps.step3.title'),
      description: t('steps.step3.description'),
      color: 'from-emerald-500 to-teal-500',
    },
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.8,
      },
    },
  }

  return (
    <section className="section-padding bg-slate-900 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-indigo-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-violet-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6">
            {t('steps.title')}
          </h2>
          <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
            {t('steps.subtitle')}
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="relative"
        >
          {/* Connection line */}
          <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-indigo-500/20 via-violet-500/20 to-emerald-500/20 transform -translate-y-1/2 z-0"></div>

          <div className="grid lg:grid-cols-3 gap-8 relative z-10">
            {steps.map((step, index) => {
              const Icon = step.icon
              return (
                <motion.div key={index} variants={itemVariants}>
                  <Card className="text-center group hover:scale-105 transition-all duration-300 hover:shadow-2xl hover:shadow-indigo-500/10 relative">
                    {/* Step number */}
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div className={`w-12 h-12 bg-gradient-to-r ${step.color} rounded-full flex items-center justify-center shadow-lg text-white font-bold text-lg`}>
                        {step.number}
                      </div>
                    </div>

                    <CardHeader className="pt-12">
                      <div className="mx-auto mb-4 relative">
                        <div className={`w-16 h-16 bg-gradient-to-r ${step.color} rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300`}>
                          <Icon className="w-8 h-8 text-white" />
                        </div>
                        {/* Glow effect */}
                        <div className={`absolute inset-0 w-16 h-16 bg-gradient-to-r ${step.color} rounded-2xl blur-xl opacity-20 group-hover:opacity-40 transition-opacity duration-300`}></div>
                      </div>
                      <CardTitle className="text-xl mb-2 group-hover:text-indigo-400 transition-colors duration-300">
                        {step.title}
                      </CardTitle>
                    </CardHeader>
                    
                    <CardContent>
                      <CardDescription className="leading-relaxed">
                        {step.description}
                      </CardDescription>
                    </CardContent>

                    {/* Background decoration */}
                    <div className={`absolute -bottom-2 -right-2 rtl:-left-2 rtl:right-auto w-20 h-20 bg-gradient-to-r ${step.color} rounded-full blur-2xl opacity-10 group-hover:opacity-20 transition-opacity duration-300`}></div>
                  </Card>
                </motion.div>
              )
            })}
          </div>
        </motion.div>

        {/* Additional info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-2xl p-8 border border-slate-700/50 backdrop-blur-sm">
            <h3 className="text-2xl font-bold text-white mb-4">
              عملية سريعة وبسيطة
            </h3>
            <p className="text-slate-400 mb-6 max-w-2xl mx-auto leading-relaxed">
              في أقل من 10 دقائق، ستحصل على مستند احترافي جاهز للاستخدام. لا حاجة لخبرة تقنية أو معرفة متخصصة.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-indigo-400 mb-2">⚡</div>
                <div className="text-sm text-slate-400">سرعة فائقة</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-violet-400 mb-2">🎯</div>
                <div className="text-sm text-slate-400">دقة عالية</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-emerald-400 mb-2">✨</div>
                <div className="text-sm text-slate-400">جودة احترافية</div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
