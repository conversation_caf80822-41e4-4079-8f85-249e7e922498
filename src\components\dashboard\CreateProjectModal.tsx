'use client'

import { useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import {
  X,
  Plus,
  Upload,
  FileText,
  Mic,
  Edit3,
  ArrowLeft
} from 'lucide-react'

interface ProjectTypeSelectionModalProps {
  isOpen: boolean
  onClose: () => void
  onProjectCreated?: () => void
}

const PROJECT_CREATION_OPTIONS = [
  {
    id: 'audio-upload',
    title: 'رفع ملف صوتي',
    description: 'قم برفع ملف صوتي لتحويله إلى مشروع تلقائياً',
    icon: Upload,
    route: '/dashboard/projects/create/audio-upload',
    color: 'from-blue-500 to-indigo-500',
    bgColor: 'from-blue-500/10 to-indigo-500/10',
    borderColor: 'border-blue-500/20'
  },
  {
    id: 'document-upload',
    title: 'رفع مستند',
    description: 'ارفع مستند موجود لتحويله إلى مشروع منظم',
    icon: FileText,
    route: '/dashboard/projects/create/document-upload',
    color: 'from-green-500 to-emerald-500',
    bgColor: 'from-green-500/10 to-emerald-500/10',
    borderColor: 'border-green-500/20'
  },
  {
    id: 'voice-recording',
    title: 'تسجيل صوتي للبيانات',
    description: 'سجل بياناتك صوتياً وسيتم تحويلها إلى مشروع',
    icon: Mic,
    route: '/dashboard/projects/create/voice-recording',
    color: 'from-purple-500 to-pink-500',
    bgColor: 'from-purple-500/10 to-pink-500/10',
    borderColor: 'border-purple-500/20'
  },
  {
    id: 'manual-entry',
    title: 'تعبئة يدوية',
    description: 'أدخل بيانات المشروع يدوياً خطوة بخطوة',
    icon: Edit3,
    route: '/dashboard/projects/create/manual-entry',
    color: 'from-orange-500 to-red-500',
    bgColor: 'from-orange-500/10 to-red-500/10',
    borderColor: 'border-orange-500/20'
  }
]

export default function ProjectTypeSelectionModal({ isOpen, onClose }: ProjectTypeSelectionModalProps) {
  const router = useRouter()

  const handleOptionSelect = (route: string) => {
    onClose()
    router.push(route)
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/60 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ type: 'spring', duration: 0.5 }}
            className="relative w-full max-w-2xl bg-gradient-to-br from-slate-800 via-slate-800 to-slate-900 border border-slate-700/50 rounded-2xl shadow-2xl overflow-hidden"
            dir="rtl"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-slate-700/50 bg-gradient-to-l from-indigo-500/5 to-purple-500/5">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center">
                  <Plus className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-white font-vazirmatn">
                    إنشاء مشروع
                  </h2>
                  <p className="text-sm text-slate-400 font-vazirmatn">
                    اختر طريقة إنشاء مشروعك
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="p-2 text-slate-400 hover:text-white hover:bg-slate-700/50 rounded-lg transition-colors duration-200"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Project Creation Options */}
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {PROJECT_CREATION_OPTIONS.map((option, index) => {
                  const Icon = option.icon
                  return (
                    <motion.button
                      key={option.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      onClick={() => handleOptionSelect(option.route)}
                      className={`relative p-6 bg-gradient-to-br ${option.bgColor} border ${option.borderColor} rounded-xl hover:border-opacity-50 transition-all duration-300 group text-right overflow-hidden`}
                    >
                      {/* Background Pattern */}
                      <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-white/5 to-transparent rounded-full blur-xl"></div>

                      {/* Content */}
                      <div className="relative z-10">
                        <div className="flex items-center justify-between mb-4">
                          <div className={`w-12 h-12 bg-gradient-to-r ${option.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                            <Icon className="w-6 h-6 text-white" />
                          </div>
                          <ArrowLeft className="w-5 h-5 text-slate-400 group-hover:text-white group-hover:translate-x-1 transition-all duration-300" />
                        </div>

                        <h3 className="text-lg font-semibold text-white font-vazirmatn mb-2">
                          {option.title}
                        </h3>

                        <p className="text-sm text-slate-400 font-vazirmatn leading-relaxed">
                          {option.description}
                        </p>
                      </div>
                    </motion.button>
                  )
                })}
              </div>

              {/* Divider */}
              <div className="flex items-center my-6">
                <div className="flex-1 h-px bg-slate-700"></div>
                <span className="px-4 text-sm text-slate-500 font-vazirmatn">أو</span>
                <div className="flex-1 h-px bg-slate-700"></div>
              </div>

              {/* Manual Entry Option */}
              <div className="text-center">
                <p className="text-sm text-slate-400 font-vazirmatn mb-4">
                  تفضل الطريقة التقليدية؟
                </p>
                <button
                  onClick={() => handleOptionSelect('/dashboard/projects/create/manual-entry')}
                  className="text-indigo-400 hover:text-indigo-300 font-vazirmatn text-sm transition-colors duration-200 hover:underline"
                >
                  تعبئة يدوية
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  )
}
