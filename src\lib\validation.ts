// Advanced validation utilities for authentication forms

export interface ValidationResult {
  isValid: boolean
  message: string
}

export interface PasswordStrength {
  score: number // 0-4 (0: very weak, 4: very strong)
  label: string
  color: string
  percentage: number
}

// Enhanced email validation with comprehensive regex
export const validateEmail = (email: string): ValidationResult => {
  if (!email) {
    return { isValid: false, message: 'البريد الإلكتروني مطلوب' }
  }

  // Comprehensive email regex pattern
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/

  if (!emailRegex.test(email)) {
    return { isValid: false, message: 'البريد الإلكتروني غير صحيح' }
  }

  return { isValid: true, message: '' }
}

// Advanced password validation with strength calculation
export const validatePassword = (password: string): ValidationResult & { strength: PasswordStrength } => {
  if (!password) {
    return {
      isValid: false,
      message: 'كلمة المرور مطلوبة',
      strength: { score: 0, label: 'ضعيف جداً', color: 'bg-red-500', percentage: 0 }
    }
  }

  const errors: string[] = []
  let score = 0

  // Length check (minimum 8 characters)
  if (password.length < 8) {
    errors.push('8 أحرف على الأقل')
  } else {
    score += 1
  }

  // Uppercase letter check
  if (!/[A-Z]/.test(password)) {
    errors.push('حرف كبير واحد على الأقل')
  } else {
    score += 1
  }

  // Lowercase letter check
  if (!/[a-z]/.test(password)) {
    errors.push('حرف صغير واحد على الأقل')
  } else {
    score += 1
  }

  // Number check
  if (!/\d/.test(password)) {
    errors.push('رقم واحد على الأقل')
  } else {
    score += 1
  }

  // Special character check
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('رمز خاص واحد على الأقل')
  } else {
    score += 1
  }

  // Calculate strength
  const strength = getPasswordStrength(score, password.length)

  if (errors.length > 0) {
    return {
      isValid: false,
      message: `كلمة المرور يجب أن تحتوي على: ${errors.join('، ')}`,
      strength
    }
  }

  return {
    isValid: true,
    message: '',
    strength
  }
}

// Get password strength details
export const getPasswordStrength = (score: number, length: number): PasswordStrength => {
  // Bonus points for extra length
  if (length >= 12) score += 0.5
  if (length >= 16) score += 0.5

  if (score <= 1) {
    return { score: 0, label: 'ضعيف جداً', color: 'bg-red-500', percentage: 20 }
  } else if (score <= 2) {
    return { score: 1, label: 'ضعيف', color: 'bg-orange-500', percentage: 40 }
  } else if (score <= 3) {
    return { score: 2, label: 'متوسط', color: 'bg-yellow-500', percentage: 60 }
  } else if (score <= 4) {
    return { score: 3, label: 'قوي', color: 'bg-blue-500', percentage: 80 }
  } else {
    return { score: 4, label: 'قوي جداً', color: 'bg-green-500', percentage: 100 }
  }
}

// Confirm password validation
export const validateConfirmPassword = (password: string, confirmPassword: string): ValidationResult => {
  if (!confirmPassword) {
    return { isValid: false, message: 'تأكيد كلمة المرور مطلوب' }
  }

  if (password !== confirmPassword) {
    return { isValid: false, message: 'كلمات المرور غير متطابقة' }
  }

  return { isValid: true, message: '' }
}

// Username validation
export const validateUsername = (username: string): ValidationResult => {
  if (!username) {
    return { isValid: false, message: 'اسم المستخدم مطلوب' }
  }

  if (username.length < 3) {
    return { isValid: false, message: 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل' }
  }

  if (username.length > 20) {
    return { isValid: false, message: 'اسم المستخدم يجب أن يكون 20 حرف كحد أقصى' }
  }

  // Allow Arabic, English letters, numbers, and underscores
  if (!/^[\u0600-\u06FFa-zA-Z0-9_]+$/.test(username)) {
    return { isValid: false, message: 'اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط' }
  }

  return { isValid: true, message: '' }
}
