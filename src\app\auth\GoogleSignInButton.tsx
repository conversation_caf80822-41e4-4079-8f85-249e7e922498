'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/Button'
import { toast } from 'sonner'
import { authHelpers } from '@/lib/supabase'

interface GoogleSignInButtonProps {
  className?: string
  size?: 'default' | 'sm' | 'lg' | 'xl' | 'icon'
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'link' | 'gradient'
  children?: React.ReactNode
}

export default function GoogleSignInButton({
  className = '',
  size = 'default',
  variant = 'outline',
  children
}: GoogleSignInButtonProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleGoogleSignIn = async () => {
    setIsLoading(true)

    try {
      const result = await authHelpers.signInWithGooglePopup() as { data: any; error: null }

      if (result.data) {
        toast.success('تم تسجيل الدخول بنجاح!')

        // Immediate automatic redirect to homepage
        // Use minimal delay to show success message then redirect automatically
        setTimeout(() => {
          router.push('/')
          router.refresh() // Ensure auth state is properly updated
        }, 800) // Shorter delay for faster automatic redirect
      }
    } catch (error: any) {
      if (error.message === 'Authentication cancelled') {
        toast.error('تم إلغاء تسجيل الدخول')
      } else if (error.message === 'Popup blocked') {
        toast.error('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة')
      } else {
        toast.error('حدث خطأ أثناء تسجيل الدخول بـ Google')
      }
      console.error('Google sign-in error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Button
      type="button"
      variant={variant}
      size={size}
      onClick={handleGoogleSignIn}
      disabled={isLoading}
      className={`w-full font-vazirmatn transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
    >
      <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse">
        {!isLoading && (
          <svg className="w-5 h-5" viewBox="0 0 24 24">
            <path
              fill="#4285F4"
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            />
            <path
              fill="#34A853"
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            />
            <path
              fill="#FBBC05"
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            />
            <path
              fill="#EA4335"
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            />
          </svg>
        )}
        <span>
          {isLoading ? 'جاري تسجيل الدخول...' : (children || 'تسجيل الدخول بـ Google')}
        </span>
      </div>
    </Button>
  )
}
