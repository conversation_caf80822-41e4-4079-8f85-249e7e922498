'use client'

import { motion } from 'framer-motion'
import { 
  FileTex<PERSON>, 
  TrendingUp, 
  <PERSON><PERSON><PERSON>ck, 
  BarChart3, 
  Presentation,
  ExternalLink,
  Download,
  Files
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Document } from '@/lib/dashboard-data'

interface DocumentsTableProps {
  documents: Document[]
}

const iconMap = {
  FileText,
  TrendingUp,
  FileCheck,
  BarChart3,
  Presentation
}

const getTypeColor = (type: string) => {
  switch (type) {
    case 'خطة عمل':
      return 'bg-blue-500/10 text-blue-400 border-blue-500/20'
    case 'دراسة جدوى':
      return 'bg-green-500/10 text-green-400 border-green-500/20'
    case 'عقد':
      return 'bg-orange-500/10 text-orange-400 border-orange-500/20'
    case 'تقرير':
      return 'bg-purple-500/10 text-purple-400 border-purple-500/20'
    case 'عرض تقديمي':
      return 'bg-pink-500/10 text-pink-400 border-pink-500/20'
    default:
      return 'bg-slate-500/10 text-slate-400 border-slate-500/20'
  }
}

export default function DocumentsTable({ documents }: DocumentsTableProps) {
  return (
    <div className="bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
            <Files className="w-4 h-4 text-white" />
          </div>
          <h2 className="text-xl font-semibold text-white font-vazirmatn">
            أحدث المستندات
          </h2>
          <div className="flex-1 h-px bg-gradient-to-l from-slate-700 to-transparent"></div>
        </div>
        <button className="text-sm text-indigo-400 hover:text-indigo-300 font-vazirmatn transition-colors duration-200 px-3 py-1 rounded-lg hover:bg-indigo-500/10">
          عرض الكل
        </button>
      </div>

      {/* Table */}
      <div className="overflow-hidden">
        <div className="space-y-3">
          {documents.map((document, index) => {
            const Icon = iconMap[document.typeIcon as keyof typeof iconMap] || FileText
            
            return (
              <motion.div
                key={document.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg hover:bg-slate-700/50 transition-all duration-200 group"
              >
                {/* Document Info */}
                <div className="flex items-center space-x-4 rtl:space-x-reverse flex-1 min-w-0">
                  {/* Icon */}
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-slate-600 rounded-lg flex items-center justify-center group-hover:bg-slate-500 transition-colors duration-200">
                      <Icon className="w-5 h-5 text-slate-300" />
                    </div>
                  </div>

                  {/* Document Details */}
                  <div className="flex-1 min-w-0">
                    <button className="text-left rtl:text-right w-full">
                      <h3 className="text-sm font-medium text-white font-vazirmatn truncate hover:text-indigo-400 transition-colors duration-200">
                        {document.name}
                      </h3>
                      <div className="flex items-center space-x-3 rtl:space-x-reverse mt-1">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getTypeColor(document.type)} font-vazirmatn`}>
                          {document.type}
                        </span>
                        <span className="text-xs text-slate-400 font-vazirmatn">
                          {document.createdAt}
                        </span>
                        {document.size && (
                          <span className="text-xs text-slate-500 font-vazirmatn">
                            {document.size}
                          </span>
                        )}
                      </div>
                    </button>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2 rtl:space-x-reverse flex-shrink-0">
                  <Button
                    variant="secondary"
                    size="sm"
                    className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 font-vazirmatn"
                  >
                    <ExternalLink className="w-4 h-4 ml-1 rtl:mr-1 rtl:ml-0" />
                    فتح
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 font-vazirmatn"
                  >
                    <Download className="w-4 h-4 ml-1 rtl:mr-1 rtl:ml-0" />
                    تحميل PDF
                  </Button>
                </div>
              </motion.div>
            )
          })}
        </div>
      </div>

      {/* Empty State (if no documents) */}
      {documents.length === 0 && (
        <div className="text-center py-12">
          <Files className="w-12 h-12 text-slate-500 mx-auto mb-4" />
          <p className="text-slate-400 font-vazirmatn">لا توجد مستندات حتى الآن</p>
          <p className="text-sm text-slate-500 font-vazirmatn mt-1">
            ابدأ بإنشاء مستندك الأول
          </p>
        </div>
      )}
    </div>
  )
}
