'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Crown, Zap, Lock, Check, Clock, Star, FileText } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { DocumentTemplate } from '@/lib/document-types'

interface TemplateRequirementsModalProps {
  isOpen: boolean
  onClose: () => void
  template: DocumentTemplate | null
  onContinue: (tier: 'quick' | 'professional') => void
}

export default function TemplateRequirementsModal({
  isOpen,
  onClose,
  template,
  onContinue
}: TemplateRequirementsModalProps) {
  const [selectedTier, setSelectedTier] = useState<'quick' | 'professional'>('professional')

  const getTemplateFeatures = (tier: 'quick' | 'professional') => {
    if (!template) return { sections: [], features: [], lockedSections: [] }
    
    if (template.id === 'business-plan') {
      return tier === 'professional' ? {
        sections: [
          'الملخص التنفيذي', 'نظرة عامة على الشركة ونموذج العمل', 'نظرة عامة على السوق',
          'الطلب في السوق', 'العرض في السوق', 'فجوة العرض والطلب', 'تحليل SWOT',
          'خطة اختراق السوق', 'التحليل الفني وتحليل الموارد', 'تقييم القوى العاملة',
          'متطلبات الاستثمار', 'تكلفة الاستثمار', 'خطة التمويل', 'الافتراضات والمبررات المالية',
          'البيانات المالية', 'تحليل نقطة التعادل', 'تحليل متعدد', 'خطة العمل النشطة', 'الملحق'
        ],
        features: ['19 قسم شامل', 'تحليل مالي متقدم', 'خطط تنفيذية مفصلة', 'دعم فني مخصص'],
        lockedSections: []
      } : {
        sections: [
          'الملخص التنفيذي', 'نظرة عامة على الشركة ونموذج العمل', 'نظرة عامة على السوق',
          'تحليل SWOT', 'البيانات المالية', 'تحليل متعدد'
        ],
        features: ['6 أقسام أساسية', 'قوالب جاهزة', 'إنشاء سريع'],
        lockedSections: [
          'الطلب في السوق', 'العرض في السوق', 'فجوة العرض والطلب',
          'خطة اختراق السوق', 'التحليل الفني وتحليل الموارد', 'تقييم القوى العاملة',
          'متطلبات الاستثمار', 'تكلفة الاستثمار', 'خطة التمويل', 'الافتراضات والمبررات المالية',
          'تحليل نقطة التعادل', 'خطة العمل النشطة', 'الملحق'
        ]
      }
    }
    
    if (template.id === 'feasibility-study') {
      return tier === 'professional' ? {
        sections: [
          'الملخص التنفيذي', 'وصف المشروع', 'تحليل السوق وجدواه', 'دراسة الجدوى الفنية',
          'دراسة الجدوى التنظيمية والإدارية', 'دراسة الجدوى المالية', 'تقييم المخاطر والتخفيف منها',
          'خطة التنفيذ والجدول الزمني', 'الخلاصة والتوصيات', 'الملحق (المستندات الداعمة)',
          'تقييم الأثر البيئي والتحليل', 'تقييم التأثير الاجتماعي وجدوى المجتمع',
          'الجدوى التشغيلية وإدارة الموارد', 'تقييم الامتثال القانوني والتنظيمي',
          'تحليل التكنولوجيا والابتكار', 'تقييم سلسلة التوريد واللوجستيات',
          'تحليل قابلية التوسع وإمكانات النمو', 'الموقع التنافسي واستراتيجية دخول السوق',
          'تحليل الحساسية والسيناريوهات', 'خارطة طريق التنفيذ المفصلة'
        ],
        features: ['20 قسم شامل', 'تحليل متعدد الأبعاد', 'دراسة شاملة للمخاطر', 'خطط تنفيذية مفصلة'],
        lockedSections: []
      } : {
        sections: [
          'الملخص التنفيذي', 'وصف المشروع', 'تحليل السوق وجدواه',
          'دراسة الجدوى المالية', 'الخلاصة والتوصيات'
        ],
        features: ['5 أقسام أساسية', 'تحليل مبسط', 'إنشاء سريع'],
        lockedSections: [
          'دراسة الجدوى الفنية', 'دراسة الجدوى التنظيمية والإدارية',
          'تقييم المخاطر والتخفيف منها', 'خطة التنفيذ والجدول الزمني',
          'الملحق (المستندات الداعمة)', 'تقييم الأثر البيئي والتحليل',
          'تقييم التأثير الاجتماعي وجدوى المجتمع', 'تحليل التكنولوجيا والابتكار',
          'تقييم سلسلة التوريد واللوجستيات', 'تحليل قابلية التوسع وإمكانات النمو',
          'تحليل الحساسية والسيناريوهات', 'خارطة طريق التنفيذ المفصلة'
        ]
      }
    }

    // Training Agreement Template
    if (template.id === 'training-agreement') {
      return tier === 'professional' ? {
        sections: [
          'الأطراف وتاريخ السريان', 'الغرض من التدريب', 'تفاصيل التدريب',
          'المسؤوليات والأهداف التعليمية', 'المكافأة/التعويض والمزايا',
          'السرية والملكية الفكرية', 'القانون الحاكم وتسوية النزاعات'
        ],
        features: ['7 أقسام شاملة', 'حماية قانونية كاملة', 'أهداف تعليمية واضحة', 'تفاصيل التعويض'],
        lockedSections: []
      } : {
        sections: [
          'الأطراف وتاريخ السريان', 'الغرض من التدريب', 'تفاصيل التدريب',
          'المسؤوليات والأهداف التعليمية', 'المكافأة/التعويض والمزايا'
        ],
        features: ['5 أقسام أساسية', 'إعداد سريع', 'تفاصيل أساسية'],
        lockedSections: [
          'السرية والملكية الفكرية', 'القانون الحاكم وتسوية النزاعات'
        ]
      }
    }

    // Consultant Agreement Template
    if (template.id === 'consultant-agreement') {
      return tier === 'professional' ? {
        sections: [
          'الأطراف وتاريخ السريان', 'خدمات الاستشارة', 'التعويض (النقدي و/أو الأسهم)',
          'المدة والإنهاء', 'السرية والملكية الفكرية', 'العلاقة بين الأطراف',
          'تضارب المصالح', 'القانون الحاكم', 'متفرقات'
        ],
        features: ['9 أقسام شاملة', 'تعويض متقدم (نقدي وأسهم)', 'حماية قانونية كاملة', 'إدارة تضارب المصالح'],
        lockedSections: []
      } : {
        sections: [
          'الأطراف وتاريخ السريان', 'خدمات الاستشارة', 'المدة والإنهاء',
          'السرية والملكية الفكرية', 'العلاقة بين الأطراف', 'القانون الحاكم', 'متفرقات'
        ],
        features: ['7 أقسام أساسية', 'إعداد سريع', 'حماية أساسية'],
        lockedSections: [
          'التعويض (النقدي و/أو الأسهم)', 'تضارب المصالح'
        ]
      }
    }

    // Job Offer Letter Template
    if (template.id === 'job-offer-letter') {
      return tier === 'professional' ? {
        sections: [
          'التاريخ ومعلومات المرشح', 'معلومات الشركة', 'المسمى الوظيفي والتسلسل الإداري',
          'تاريخ البدء وموقع العمل', 'التعويض (الراتب)', 'ملخص المزايا',
          'شروط التوظيف', 'بيان التوظيف حسب الرغبة', 'الموعد النهائي للقبول والتوقيع'
        ],
        features: ['9 أقسام شاملة', 'تفاصيل المزايا الكاملة', 'شروط توظيف متقدمة', 'حماية قانونية'],
        lockedSections: []
      } : {
        sections: [
          'التاريخ ومعلومات المرشح', 'معلومات الشركة', 'المسمى الوظيفي والتسلسل الإداري',
          'تاريخ البدء وموقع العمل', 'التعويض (الراتب)', 'بيان التوظيف حسب الرغبة',
          'الموعد النهائي للقبول والتوقيع'
        ],
        features: ['7 أقسام أساسية', 'معلومات أساسية', 'إعداد سريع'],
        lockedSections: [
          'ملخص المزايا', 'شروط التوظيف'
        ]
      }
    }

    // Employment Contract Template
    if (template.id === 'employment-contract') {
      return tier === 'professional' ? {
        sections: [
          'الأطراف وتاريخ السريان', 'المنصب والواجبات', 'التعويض (الراتب، المكافآت، إلخ)',
          'المزايا', 'مدة التوظيف (أو حسب الرغبة)', 'أحكام الإنهاء',
          'السرية والتنازل عن الملكية الفكرية', 'عدم المنافسة وعدم الاستمالة',
          'القانون الحاكم وتسوية النزاعات', 'الاتفاقية الكاملة والتعديلات'
        ],
        features: ['10 أقسام شاملة', 'حماية قانونية كاملة', 'أحكام إنهاء متقدمة', 'حماية الملكية الفكرية'],
        lockedSections: []
      } : {
        sections: [
          'الأطراف وتاريخ السريان', 'المنصب والواجبات', 'التعويض (الراتب، المكافآت، إلخ)',
          'المزايا', 'مدة التوظيف (أو حسب الرغبة)', 'السرية والتنازل عن الملكية الفكرية',
          'الاتفاقية الكاملة والتعديلات'
        ],
        features: ['7 أقسام أساسية', 'عقد توظيف أساسي', 'حماية أساسية'],
        lockedSections: [
          'أحكام الإنهاء', 'عدم المنافسة وعدم الاستمالة', 'القانون الحاكم وتسوية النزاعات'
        ]
      }
    }

    // Training Offer Letter Template
    if (template.id === 'training-offer-letter') {
      return tier === 'professional' ? {
        sections: [
          'التاريخ ومعلومات المرشح', 'معلومات الشركة', 'مسمى التدريب والقسم',
          'الهيكل الإداري/الموجه', 'مدة التدريب والتواريخ المهمة', 'موقع العمل والساعات المتوقعة',
          'المكافأة/التعويض', 'الأهداف التعليمية/المسؤوليات الرئيسية', 'شروط التدريب',
          'بيان حسب الرغبة (إن وجد)', 'الموعد النهائي للقبول والتوقيع'
        ],
        features: ['11 قسم شامل', 'أهداف تعليمية واضحة', 'شروط تدريب متقدمة', 'إطار قانوني كامل'],
        lockedSections: []
      } : {
        sections: [
          'التاريخ ومعلومات المرشح', 'معلومات الشركة', 'مسمى التدريب والقسم',
          'الهيكل الإداري/الموجه', 'مدة التدريب والتواريخ المهمة', 'موقع العمل والساعات المتوقعة',
          'المكافأة/التعويض', 'الأهداف التعليمية/المسؤوليات الرئيسية'
        ],
        features: ['8 أقسام أساسية', 'عرض تدريب أساسي', 'إعداد سريع'],
        lockedSections: [
          'شروط التدريب', 'بيان حسب الرغبة (إن وجد)', 'الموعد النهائي للقبول والتوقيع'
        ]
      }
    }

    // Founders Agreement Template
    if (template.id === 'founders-agreement') {
      return tier === 'professional' ? {
        sections: [
          'الأطراف', 'الغرض والنطاق', 'ملكية الأسهم والاستحقاق',
          'الأدوار والمسؤوليات', 'اتخاذ القرار', 'الملكية الفكرية',
          'مغادرة المؤسس (شروط الخروج)', 'تسوية النزاعات', 'القانون الحاكم'
        ],
        features: ['9 أقسام شاملة', 'إدارة ملكية الأسهم', 'شروط خروج متقدمة', 'حماية قانونية كاملة'],
        lockedSections: []
      } : {
        sections: [
          'الأطراف', 'الغرض والنطاق', 'ملكية الأسهم والاستحقاق',
          'الأدوار والمسؤوليات', 'اتخاذ القرار', 'الملكية الفكرية'
        ],
        features: ['6 أقسام أساسية', 'اتفاقية مؤسسين أساسية', 'إعداد سريع'],
        lockedSections: [
          'مغادرة المؤسس (شروط الخروج)', 'تسوية النزاعات', 'القانون الحاكم'
        ]
      }
    }

    // Privacy Policy Template
    if (template.id === 'privacy-policy') {
      return tier === 'professional' ? {
        sections: [
          'مقدمة ونطاق السياسة', 'المعلومات التي نجمعها', 'كيف نستخدم معلوماتك',
          'مشاركة المعلومات والكشف عنها', 'أمن البيانات', 'حقوقك وخياراتك',
          'التغييرات في سياسة الخصوصية', 'اتصل بنا'
        ],
        features: ['8 أقسام شاملة', 'امتثال قانوني كامل', 'حماية البيانات المتقدمة', 'حقوق المستخدمين'],
        lockedSections: []
      } : {
        sections: [
          'مقدمة ونطاق السياسة', 'المعلومات التي نجمعها', 'كيف نستخدم معلوماتك',
          'التغييرات في سياسة الخصوصية', 'اتصل بنا'
        ],
        features: ['5 أقسام أساسية', 'سياسة خصوصية أساسية', 'إعداد سريع'],
        lockedSections: [
          'مشاركة المعلومات والكشف عنها', 'أمن البيانات', 'حقوقك وخياراتك'
        ]
      }
    }

    // Stock Option Agreement Template
    if (template.id === 'stock-option-agreement') {
      return tier === 'professional' ? {
        sections: [
          'إشعار المنح والتفاصيل', 'منح الخيار', 'جدول الاستحقاق',
          'ممارسة الخيار', 'مدة الخيار', 'أثر إنهاء الخدمة',
          'الامتثال للقوانين والخطة', 'القانون الحاكم'
        ],
        features: ['8 أقسام شاملة', 'جدولة استحقاق متقدمة', 'أحكام إنهاء شاملة', 'امتثال قانوني كامل'],
        lockedSections: []
      } : {
        sections: [
          'إشعار المنح والتفاصيل', 'منح الخيار', 'جدول الاستحقاق',
          'ممارسة الخيار', 'مدة الخيار'
        ],
        features: ['5 أقسام أساسية', 'اتفاقية خيار أسهم أساسية', 'إعداد سريع'],
        lockedSections: [
          'أثر إنهاء الخدمة', 'الامتثال للقوانين والخطة', 'القانون الحاكم'
        ]
      }
    }

    // Terms of Service Template
    if (template.id === 'terms-of-service') {
      return tier === 'professional' ? {
        sections: [
          'مقدمة وقبول الشروط', 'استخدام الخدمة والترخيص', 'محتوى المستخدم وسلوكه',
          'حقوق الملكية الفكرية', 'إخلاء المسؤولية وتحديدها', 'الإنهاء',
          'القانون الحاكم وتسوية النزاعات', 'معلومات الاتصال'
        ],
        features: ['8 أقسام شاملة', 'حماية قانونية كاملة', 'إخلاء مسؤولية متقدم', 'تسوية نزاعات شاملة'],
        lockedSections: []
      } : {
        sections: [
          'مقدمة وقبول الشروط', 'استخدام الخدمة والترخيص', 'محتوى المستخدم وسلوكه',
          'حقوق الملكية الفكرية', 'معلومات الاتصال'
        ],
        features: ['5 أقسام أساسية', 'شروط خدمة أساسية', 'إعداد سريع'],
        lockedSections: [
          'إخلاء المسؤولية وتحديدها', 'الإنهاء', 'القانون الحاكم وتسوية النزاعات'
        ]
      }
    }

    return { sections: [], features: [], lockedSections: [] }
  }

  const handleContinue = () => {
    onContinue(selectedTier)
  }

  if (!isOpen || !template) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-2 sm:p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.3 }}
          className="bg-gradient-to-br from-slate-800 via-slate-800 to-slate-900 border border-slate-700 rounded-2xl max-w-4xl w-full max-h-[90vh] sm:max-h-[85vh] shadow-2xl mx-2 sm:mx-0 flex flex-col"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="relative p-4 border-b border-slate-700/50 bg-gradient-to-r from-slate-800/50 to-slate-900/50">
            {/* Progress Indicator */}
            <div className="absolute top-0 left-0 right-0 h-1 bg-slate-700">
              <div className="h-full w-1/2 bg-gradient-to-r from-indigo-500 to-purple-500"></div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center shadow-lg">
                  <FileText className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <h2 className="text-xl font-bold text-white font-vazirmatn">
                      {template.title}
                    </h2>
                    <span className="text-xs bg-indigo-500/20 text-indigo-300 px-2 py-1 rounded-full font-vazirmatn">
                      1/2
                    </span>
                  </div>
                  <p className="text-slate-400 font-vazirmatn text-sm">
                    {template.description}
                  </p>
                  {template.featured && (
                    <div className="flex items-center gap-2 mt-1">
                      <Star className="w-3 h-3 text-amber-400 fill-current" />
                      <span className="text-amber-400 font-vazirmatn text-xs">قالب مميز</span>
                    </div>
                  )}
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-slate-400 hover:text-white transition-colors duration-200 p-2 hover:bg-slate-700/50 rounded-lg"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Tier Selection Tabs */}
          <div className="flex border-b border-slate-700/50 bg-slate-800/30">
            <motion.button
              onClick={() => setSelectedTier('professional')}
              whileHover={{ scale: 1.01 }}
              whileTap={{ scale: 0.99 }}
              className={`flex-1 px-4 py-3 text-center font-vazirmatn transition-all duration-300 relative ${
                selectedTier === 'professional'
                  ? 'bg-gradient-to-r from-indigo-500/20 to-purple-500/20 text-indigo-300'
                  : 'text-slate-400 hover:text-white hover:bg-slate-700/30'
              }`}
            >
              <div className="flex items-center justify-center gap-2">
                <Crown className={`w-5 h-5 ${selectedTier === 'professional' ? 'text-indigo-400' : ''}`} />
                <div className="text-right">
                  <div className="font-bold">احترافي</div>
                  <div className="text-xs opacity-80">النسخة الكاملة</div>
                </div>
              </div>
              {selectedTier === 'professional' && (
                <motion.div
                  layoutId="activeTab"
                  className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-indigo-500 to-purple-500"
                  transition={{ duration: 0.3 }}
                />
              )}
            </motion.button>
            <motion.button
              onClick={() => setSelectedTier('quick')}
              whileHover={{ scale: 1.01 }}
              whileTap={{ scale: 0.99 }}
              className={`flex-1 px-4 py-3 text-center font-vazirmatn transition-all duration-300 relative ${
                selectedTier === 'quick'
                  ? 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-300'
                  : 'text-slate-400 hover:text-white hover:bg-slate-700/30'
              }`}
            >
              <div className="flex items-center justify-center gap-2">
                <Zap className={`w-5 h-5 ${selectedTier === 'quick' ? 'text-green-400' : ''}`} />
                <div className="text-right">
                  <div className="font-bold">سريع</div>
                  <div className="text-xs opacity-80">النسخة المجانية</div>
                </div>
              </div>
              {selectedTier === 'quick' && (
                <motion.div
                  layoutId="activeTab"
                  className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-green-500 to-emerald-500"
                  transition={{ duration: 0.3 }}
                />
              )}
            </motion.button>
          </div>

          {/* Content */}
          <div className="flex-1 p-4 overflow-y-auto min-h-0">
            <motion.div
              key={`${template?.id}-${selectedTier}`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6"
            >
              {/* Left Column - Sections */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-bold text-white font-vazirmatn">
                    الأقسام المتضمنة
                  </h3>
                  <div className="flex items-center gap-2">
                    <div className={`px-2 py-1 rounded-full text-xs font-vazirmatn font-bold ${
                      selectedTier === 'professional'
                        ? 'bg-indigo-500/20 text-indigo-300 border border-indigo-500/30'
                        : 'bg-green-500/20 text-green-300 border border-green-500/30'
                    }`}>
                      {getTemplateFeatures(selectedTier).sections.length} قسم
                    </div>
                    {selectedTier === 'quick' && getTemplateFeatures(selectedTier).lockedSections.length > 0 && (
                      <div className="px-2 py-1 rounded-full text-xs font-vazirmatn bg-amber-500/20 text-amber-300 border border-amber-500/30">
                        +{getTemplateFeatures(selectedTier).lockedSections.length} مدفوع
                      </div>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 max-h-48 overflow-y-auto pr-2">
                  {getTemplateFeatures(selectedTier).sections.map((section, index) => (
                    <motion.div
                      key={`${selectedTier}-section-${index}`}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="flex items-center gap-3 p-3 bg-gradient-to-r from-slate-700/50 to-slate-800/50 rounded-lg border border-slate-600/30 hover:border-slate-500/50 transition-all duration-200"
                    >
                      <div className={`w-6 h-6 rounded-lg flex items-center justify-center text-white text-xs font-vazirmatn font-bold shadow-lg ${
                        selectedTier === 'professional'
                          ? 'bg-gradient-to-r from-indigo-500 to-purple-500'
                          : 'bg-gradient-to-r from-green-500 to-emerald-500'
                      }`}>
                        {index + 1}
                      </div>
                      <span className="text-slate-200 font-vazirmatn text-xs flex-1 leading-relaxed">{section}</span>
                    </motion.div>
                  ))}
                  
                  {/* Show locked sections for free tier */}
                  {selectedTier === 'quick' && getTemplateFeatures(selectedTier).lockedSections.map((section, index) => (
                    <motion.div
                      key={`${selectedTier}-locked-${index}`}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: (getTemplateFeatures(selectedTier).sections.length + index) * 0.05 }}
                      className="flex items-center gap-3 p-3 bg-slate-800/30 rounded-lg border border-slate-700/50 opacity-60"
                    >
                      <div className="w-6 h-6 bg-slate-700 rounded-lg flex items-center justify-center shadow-lg">
                        <Lock className="w-3 h-3 text-amber-400" />
                      </div>
                      <span className="text-slate-400 font-vazirmatn text-xs flex-1 leading-relaxed">{section}</span>
                      <span className="text-xs text-amber-400 font-vazirmatn bg-amber-400/10 px-2 py-1 rounded-full">مدفوع</span>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Right Column - Features & Info */}
              <div className="space-y-3">
                <h3 className="text-lg font-bold text-white font-vazirmatn">
                  {selectedTier === 'professional' ? 'النسخة الاحترافية' : 'النسخة السريعة'}
                </h3>

                {/* Features Card */}
                <div className={`p-3 rounded-xl border shadow-lg ${
                  selectedTier === 'professional'
                    ? 'bg-gradient-to-br from-indigo-500/10 via-purple-500/5 to-indigo-500/10 border-indigo-500/30'
                    : 'bg-gradient-to-br from-green-500/10 via-emerald-500/5 to-green-500/10 border-green-500/30'
                }`}>
                  <div className="flex items-center gap-2 mb-2">
                    {selectedTier === 'professional' ? (
                      <Crown className="w-4 h-4 text-indigo-400" />
                    ) : (
                      <Zap className="w-4 h-4 text-green-400" />
                    )}
                    <span className={`font-bold font-vazirmatn ${
                      selectedTier === 'professional' ? 'text-indigo-300' : 'text-green-300'
                    }`}>
                      المميزات المتضمنة
                    </span>
                  </div>
                  
                  <div className="space-y-2">
                    {getTemplateFeatures(selectedTier).features.map((feature, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                        className="flex items-center gap-2"
                      >
                        <Check className={`w-4 h-4 ${
                          selectedTier === 'professional' ? 'text-indigo-400' : 'text-green-400'
                        }`} />
                        <span className="text-slate-200 font-vazirmatn text-sm">{feature}</span>
                      </motion.div>
                    ))}
                  </div>
                </div>

                {/* Time & Difficulty Info */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  <div className="p-2 bg-slate-700/30 rounded-lg border border-slate-600/30">
                    <div className="flex items-center gap-2 mb-1">
                      <Clock className="w-3 h-3 text-slate-400" />
                      <span className="text-slate-300 font-vazirmatn font-semibold text-xs">وقت الإنشاء</span>
                    </div>
                    <span className="text-white font-vazirmatn text-sm">{template.estimatedTime}</span>
                  </div>

                  <div className="p-2 bg-slate-700/30 rounded-lg border border-slate-600/30">
                    <div className="flex items-center gap-2 mb-1">
                      <div className="w-3 h-3 bg-slate-500 rounded-full"></div>
                      <span className="text-slate-300 font-vazirmatn font-semibold text-xs">مستوى الصعوبة</span>
                    </div>
                    <span className="text-white font-vazirmatn text-sm">
                      {template.difficulty === 'easy' ? 'سهل' : template.difficulty === 'medium' ? 'متوسط' : 'صعب'}
                    </span>
                  </div>
                </div>

                {/* Upgrade Prompt for Quick Tier */}
                {selectedTier === 'quick' && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="p-3 bg-gradient-to-r from-amber-500/10 to-orange-500/10 border border-amber-500/30 rounded-lg"
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <Crown className="w-4 h-4 text-amber-400" />
                      <span className="text-amber-300 font-vazirmatn font-bold text-sm">ترقية للنسخة الاحترافية</span>
                    </div>
                    <p className="text-slate-300 font-vazirmatn mb-2 text-xs">
                      احصل على جميع الأقسام المتقدمة والمميزات الإضافية
                    </p>
                    <Button
                      onClick={() => setSelectedTier('professional')}
                      size="sm"
                      className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 font-vazirmatn text-xs"
                    >
                      <Crown className="w-3 h-3 ml-1" />
                      ترقية الآن
                    </Button>
                  </motion.div>
                )}
              </div>
            </motion.div>
          </div>

          {/* Footer - Fixed Position */}
          <div className="flex-shrink-0 p-6 border-t border-slate-700/50 bg-slate-800/30">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  onClick={onClose}
                  className="font-vazirmatn text-slate-400 hover:text-white"
                >
                  إلغاء
                </Button>
                <div className="text-slate-500 font-vazirmatn text-sm">
                  الخطوة 1 من 2
                </div>
              </div>

              <Button
                onClick={handleContinue}
                className={`font-vazirmatn px-8 py-3 shadow-lg transition-all duration-200 ${
                  selectedTier === 'professional'
                    ? 'bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600'
                    : 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600'
                }`}
              >
                <span className="flex items-center gap-2">
                  متابعة إنشاء المستند
                  <motion.div
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    ←
                  </motion.div>
                </span>
              </Button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
