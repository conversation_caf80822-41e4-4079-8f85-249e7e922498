'use client'

import { useState, useRef } from 'react'
import { motion } from 'framer-motion'
import { Upload, FileText, X, Eye, Download, CheckCircle, <PERSON> } from 'lucide-react'
import { toast } from 'sonner'
import ProjectInfoChecklist from '@/components/dashboard/ProjectInfoChecklist'
import Breadcrumb from '@/components/dashboard/Breadcrumb'
import ProgressBar from '@/components/ui/ProgressBar'
import { Button } from '@/components/ui/Button'
import { PROJECT_CHECKLIST_ITEMS, UploadedFile } from '@/lib/project-creation-types'

export default function DocumentUploadPage() {
  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [dragActive, setDragActive] = useState(false)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysisResult, setAnalysisResult] = useState<{
    success: boolean
    completedItems: string[]
    missingItems: string[]
    message: string
    extractedData?: any
  } | null>(null)

  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    const files = e.dataTransfer.files
    if (files && files[0]) {
      handleFileUpload(files[0])
    }
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files[0]) {
      handleFileUpload(files[0])
    }
  }

  const handleFileUpload = async (file: File) => {
    // Validate file type
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']
    if (!allowedTypes.includes(file.type)) {
      toast.error('يرجى رفع ملف PDF أو Word أو نص صالح')
      return
    }

    // Validate file size (max 50MB)
    if (file.size > 50 * 1024 * 1024) {
      toast.error('حجم الملف كبير جداً. الحد الأقصى 50 ميجابايت')
      return
    }

    setIsUploading(true)
    setUploadProgress(0)

    try {
      // Simulate upload progress
      const uploadedFileData: UploadedFile = {
        name: file.name,
        size: file.size,
        type: file.type,
        url: URL.createObjectURL(file)
      }

      // Simulate progressive upload with progress updates
      for (let i = 0; i <= 100; i += 10) {
        setUploadProgress(i)
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      setUploadedFile(uploadedFileData)
      toast.success('تم رفع المستند بنجاح')
    } catch (error) {
      toast.error('فشل في رفع الملف. يرجى المحاولة مرة أخرى')
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
    }
  }

  const removeFile = () => {
    if (uploadedFile?.url) {
      URL.revokeObjectURL(uploadedFile.url)
    }
    setUploadedFile(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 بايت'
    const k = 1024
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (type: string) => {
    if (type.includes('pdf')) return '📄'
    if (type.includes('word') || type.includes('document')) return '📝'
    if (type.includes('text')) return '📃'
    return '📄'
  }

  const handleAnalyzeFile = async () => {
    if (!uploadedFile || !uploadedFile.url) {
      toast.error('لا يوجد ملف للتحليل')
      return
    }

    setIsAnalyzing(true)

    try {
      // Convert blob URL to file
      const response = await fetch(uploadedFile.url)
      const blob = await response.blob()
      const file = new File([blob], uploadedFile.name, { type: uploadedFile.type })

      // Create form data
      const formData = new FormData()
      formData.append('file', file)

      // Send to analysis API
      const analysisResponse = await fetch('/api/analyze-pdf', {
        method: 'POST',
        body: formData
      })

      if (!analysisResponse.ok) {
        const errorData = await analysisResponse.json()
        throw new Error(errorData.error || 'فشل في تحليل الملف')
      }

      const result = await analysisResponse.json()
      setAnalysisResult(result)

      if (result.success) {
        toast.success('تم تحليل الملف بنجاح! تم العثور على جميع المعلومات المطلوبة')
      } else {
        toast.warning('تم تحليل الملف ولكن بعض المعلومات مفقودة')
      }

    } catch (error) {
      console.error('Analysis error:', error)
      toast.error(error instanceof Error ? error.message : 'فشل في تحليل الملف')
    } finally {
      setIsAnalyzing(false)
    }
  }

  const handleCreateProject = async () => {
    if (!uploadedFile) {
      toast.error('يرجى رفع مستند أولاً')
      return
    }

    // Check if analysis was performed and successful
    if (!analysisResult) {
      toast.error('يرجى تحليل الملف أولاً للتأكد من اكتمال المعلومات')
      return
    }

    if (!analysisResult.success) {
      toast.error('لا يمكن إنشاء المشروع. الملف لا يحتوي على جميع المعلومات المطلوبة')
      return
    }

    try {
      // Simulate project creation
      toast.success('تم إنشاء المشروع بنجاح!')
      // Redirect to projects list or project details
    } catch (error) {
      toast.error('فشل في إنشاء المشروع. يرجى المحاولة مرة أخرى')
    }
  }

  const breadcrumbItems = [
    { label: 'لوحة التحكم', href: '/dashboard' },
    { label: 'المشاريع', href: '/dashboard/projects' },
    { label: 'إنشاء مشروع', href: '/dashboard/projects/create' },
    { label: 'رفع مستند' }
  ]

  return (
    <div className="space-y-8">
      {/* Breadcrumb */}
      <Breadcrumb items={breadcrumbItems} />

      {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-3xl font-bold text-white font-vazirmatn mb-2">
            رفع معلومات المشروع
          </h1>
          <p className="text-slate-400 font-vazirmatn">
            قم بتحميل مستند يحتوي على معلومات مشروعك وتأكد أنه يحتوي على النقاط التالية
          </p>
        </motion.div>

        {/* Upload Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8"
        >
          <h2 className="text-xl font-semibold text-white font-vazirmatn mb-6">
            رفع المحتوى
          </h2>

          {!uploadedFile ? (
            <div
              className={`border-2 border-dashed rounded-2xl p-12 text-center transition-all duration-300 ${
                dragActive
                  ? 'border-indigo-500 bg-indigo-500/10'
                  : 'border-slate-600 hover:border-slate-500'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <div className="flex flex-col items-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-2xl flex items-center justify-center">
                  <FileText className="w-8 h-8 text-white" />
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white font-vazirmatn mb-2">
                    رفع مستند المشروع
                  </h3>
                  <p className="text-slate-400 font-vazirmatn mb-4">
                    ارفع مستند PDF يحتوي على معلومات مشروعك
                  </p>
                </div>

                <Button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploading}
                  className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600"
                >
                  <Upload className="w-4 h-4 ml-2" />
                  {isUploading ? 'جاري الرفع...' : 'اختر ملف PDF'}
                </Button>

                {isUploading && (
                  <div className="w-full max-w-md">
                    <ProgressBar progress={uploadProgress} />
                  </div>
                )}

                <p className="text-xs text-slate-500 font-vazirmatn">
                  نوع الملف المقبول: PDF أو أي ملف مستند (حتى 50 ميجابايت)
                </p>
              </div>

              <input
                ref={fileInputRef}
                type="file"
                accept=".pdf,.doc,.docx,.txt"
                onChange={handleFileSelect}
                className="hidden"
              />
            </div>
          ) : (
            <div className="space-y-6">
              {/* File Info */}
              <div className="bg-slate-700/30 rounded-xl p-6 border border-slate-600/30">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                      <CheckCircle className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <span className="text-2xl">{getFileIcon(uploadedFile.type)}</span>
                        <h4 className="font-semibold text-white font-vazirmatn">
                          {uploadedFile.name}
                        </h4>
                      </div>
                      <p className="text-sm text-slate-400 font-vazirmatn">
                        {formatFileSize(uploadedFile.size)}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    {uploadedFile.url && (
                      <>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={handleAnalyzeFile}
                          disabled={isAnalyzing}
                          className="text-green-400 hover:text-green-300 hover:bg-green-500/10 flex items-center space-x-1 rtl:space-x-reverse"
                          title="تحليل الملف بالذكاء الاصطناعي"
                        >
                          {isAnalyzing ? (
                            <div className="w-4 h-4 animate-spin rounded-full border-2 border-green-400 border-t-transparent" />
                          ) : (
                            <>
                              <Brain className="w-4 h-4" />
                              <span className="text-xs font-vazirmatn">تحليل</span>
                            </>
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(uploadedFile.url, '_blank')}
                          className="text-indigo-400 hover:text-indigo-300 hover:bg-indigo-500/10"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            const link = document.createElement('a')
                            link.href = uploadedFile.url!
                            link.download = uploadedFile.name
                            link.click()
                          }}
                          className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10"
                        >
                          <Download className="w-4 h-4" />
                        </Button>
                      </>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={removeFile}
                      className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* File Preview Info */}
                <div className="bg-slate-800/50 rounded-lg p-4">
                  {!analysisResult ? (
                    <p className="text-sm text-slate-300 font-vazirmatn">
                      📋 تم رفع المستند بنجاح. اضغط على زر "تحليل" لاستخراج معلومات المشروع بالذكاء الاصطناعي.
                    </p>
                  ) : (
                    <div className="space-y-2">
                      <p className="text-sm text-slate-300 font-vazirmatn">
                        🤖 تم تحليل المستند بالذكاء الاصطناعي
                      </p>
                      {analysisResult.success ? (
                        <p className="text-sm text-green-400 font-vazirmatn">
                          ✅ تم العثور على جميع المعلومات المطلوبة
                        </p>
                      ) : (
                        <p className="text-sm text-amber-400 font-vazirmatn">
                          ⚠️ بعض المعلومات مفقودة - راجع القائمة أدناه
                        </p>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Create Project Button */}
              <div className="flex justify-end">
                <Button
                  onClick={handleCreateProject}
                  disabled={!analysisResult?.success}
                  className={`${
                    analysisResult?.success
                      ? 'bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600'
                      : 'bg-slate-600 cursor-not-allowed opacity-50'
                  }`}
                >
                  إنشاء المشروع
                </Button>
              </div>
            </div>
          )}
        </motion.div>

        {/* Project Information Checklist */}
        <ProjectInfoChecklist
          items={PROJECT_CHECKLIST_ITEMS}
          completedCount={0}
          analysisResult={analysisResult || undefined}
        />
      </div>
  )
}
