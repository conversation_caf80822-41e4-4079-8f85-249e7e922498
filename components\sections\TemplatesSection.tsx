'use client'

import { useTranslations } from 'next-intl'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { FileText, PresentationChart, Calculator, FileContract, BarChart, Users } from 'lucide-react'
import { motion } from 'framer-motion'

export default function TemplatesSection() {
  const t = useTranslations()

  const templates = [
    {
      icon: FileText,
      title: t('templates.businessPlan'),
      description: 'خطة عمل شاملة مع التحليل المالي والاستراتيجي',
      color: 'from-blue-500 to-indigo-500',
      popular: true,
    },
    {
      icon: Calculator,
      title: t('templates.feasibilityStudy'),
      description: 'دراسة جدوى مفصلة مع التحليل الاقتصادي',
      color: 'from-emerald-500 to-teal-500',
      popular: false,
    },
    {
      icon: Presentation<PERSON>hart,
      title: t('templates.presentation'),
      description: 'عروض تقديمية احترافية للمستثمرين والعملاء',
      color: 'from-violet-500 to-purple-500',
      popular: false,
    },
    {
      icon: FileContract,
      title: t('templates.proposal'),
      description: 'اقتراحات مشاريع مقنعة ومفصلة',
      color: 'from-orange-500 to-red-500',
      popular: false,
    },
    {
      icon: BarChart,
      title: t('templates.report'),
      description: 'تقارير تحليلية شاملة مع الرسوم البيانية',
      color: 'from-cyan-500 to-blue-500',
      popular: false,
    },
    {
      icon: Users,
      title: t('templates.contract'),
      description: 'عقود قانونية محكمة وواضحة',
      color: 'from-pink-500 to-rose-500',
      popular: false,
    },
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  }

  return (
    <section id="templates" className="section-padding bg-slate-800/50">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6">
            {t('templates.title')}
          </h2>
          <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
            {t('templates.subtitle')}
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {templates.map((template, index) => {
            const Icon = template.icon
            return (
              <motion.div key={index} variants={itemVariants}>
                <Card className="h-full group hover:scale-105 transition-all duration-300 hover:shadow-2xl hover:shadow-indigo-500/10 relative overflow-hidden">
                  {template.popular && (
                    <div className="absolute top-4 right-4 rtl:right-auto rtl:left-4 bg-gradient-to-r from-indigo-500 to-violet-500 text-white text-xs px-3 py-1 rounded-full font-medium">
                      الأكثر شعبية
                    </div>
                  )}
                  
                  <CardHeader className="text-center pb-4">
                    <div className="mx-auto mb-4 relative">
                      <div className={`w-16 h-16 bg-gradient-to-r ${template.color} rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300`}>
                        <Icon className="w-8 h-8 text-white" />
                      </div>
                      {/* Glow effect */}
                      <div className={`absolute inset-0 w-16 h-16 bg-gradient-to-r ${template.color} rounded-2xl blur-xl opacity-20 group-hover:opacity-40 transition-opacity duration-300`}></div>
                    </div>
                    <CardTitle className="text-lg mb-2 group-hover:text-indigo-400 transition-colors duration-300">
                      {template.title}
                    </CardTitle>
                  </CardHeader>
                  
                  <CardContent className="text-center">
                    <CardDescription className="mb-6 leading-relaxed">
                      {template.description}
                    </CardDescription>
                    
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full group-hover:border-indigo-500 group-hover:text-indigo-400 transition-colors duration-300"
                    >
                      معاينة القالب
                    </Button>
                  </CardContent>

                  {/* Background decoration */}
                  <div className={`absolute -bottom-2 -right-2 rtl:-left-2 rtl:right-auto w-20 h-20 bg-gradient-to-r ${template.color} rounded-full blur-2xl opacity-10 group-hover:opacity-20 transition-opacity duration-300`}></div>
                </Card>
              </motion.div>
            )
          })}
        </motion.div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-indigo-500/10 to-violet-500/10 rounded-3xl p-8 border border-indigo-500/20">
            <h3 className="text-2xl font-bold text-white mb-4">
              لا تجد القالب المناسب؟
            </h3>
            <p className="text-slate-400 mb-6 max-w-2xl mx-auto">
              يمكننا إنشاء قالب مخصص لاحتياجاتك الخاصة. تواصل معنا وسنساعدك في تصميم القالب المثالي لعملك.
            </p>
            <Button variant="gradient" size="lg">
              طلب قالب مخصص
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
