// Mock data for dashboard components

export interface ActivitySummary {
  id: string
  title: string
  count: string
  description: string
  icon: string
  buttonText: string
  color: string
}

export interface RecentActivity {
  id: string
  type: 'created' | 'edited' | 'saved' | 'downloaded'
  description: string
  documentName: string
  timestamp: string
  icon: string
}

export interface Document {
  id: string
  name: string
  type: string
  typeIcon: string
  createdAt: string
  size?: string
}

export interface Recommendation {
  id: string
  title: string
  description: string
  icon: string
  action: string
}

// Activity Summary Cards Data
export const getActivitySummary = (): ActivitySummary[] => [
  {
    id: '1',
    title: 'المستندات',
    count: '12',
    description: 'لديك 12 مستند من أنواع مختلفة',
    icon: 'FileText',
    buttonText: 'عرض الكل',
    color: 'bg-blue-500'
  },
  {
    id: '2',
    title: 'المشاريع',
    count: '4',
    description: '3 مشاريع نشطة - 1 مكتمل',
    icon: 'Folder',
    buttonText: 'عرض المشاريع',
    color: 'bg-green-500'
  },
  {
    id: '3',
    title: 'التقارير',
    count: '4',
    description: 'أنشأت 4 تقارير تحليلية',
    icon: 'BarChart3',
    buttonText: 'عرض التقارير',
    color: 'bg-purple-500'
  },
  {
    id: '4',
    title: 'العقود',
    count: '2',
    description: '2 عقد قانوني محفوظ',
    icon: 'FileCheck',
    buttonText: 'عرض العقود',
    color: 'bg-orange-500'
  },
  {
    id: '5',
    title: 'العروض التقديمية',
    count: '3',
    description: '3 عروض جاهزة للعرض',
    icon: 'Presentation',
    buttonText: 'عرض العروض',
    color: 'bg-pink-500'
  },
  {
    id: '6',
    title: 'دراسات الجدوى',
    count: '5',
    description: '5 دراسات معدة',
    icon: 'TrendingUp',
    buttonText: 'مراجعة الدراسات',
    color: 'bg-indigo-500'
  }
]

// Recent Activity Data
export const getRecentActivity = (): RecentActivity[] => [
  {
    id: '1',
    type: 'created',
    description: 'تم إنشاء',
    documentName: 'خطة عمل مشروع التجارة الإلكترونية',
    timestamp: 'منذ ساعتين',
    icon: 'Plus'
  },
  {
    id: '2',
    type: 'edited',
    description: 'تم تعديل',
    documentName: 'دراسة جدوى مطعم الوجبات السريعة',
    timestamp: 'منذ 4 ساعات',
    icon: 'Edit'
  },
  {
    id: '3',
    type: 'saved',
    description: 'تم حفظ',
    documentName: 'عقد شراكة تجارية',
    timestamp: 'أمس',
    icon: 'Save'
  },
  {
    id: '4',
    type: 'downloaded',
    description: 'تم تحميل',
    documentName: 'تقرير الأداء الشهري',
    timestamp: 'أمس',
    icon: 'Download'
  },
  {
    id: '5',
    type: 'created',
    description: 'تم إنشاء',
    documentName: 'عرض تقديمي للمستثمرين',
    timestamp: 'منذ يومين',
    icon: 'Plus'
  },
  {
    id: '6',
    type: 'edited',
    description: 'تم تعديل',
    documentName: 'خطة التسويق الرقمي',
    timestamp: 'منذ يومين',
    icon: 'Edit'
  },
  {
    id: '7',
    type: 'saved',
    description: 'تم حفظ',
    documentName: 'دراسة السوق المحلي',
    timestamp: 'منذ 3 أيام',
    icon: 'Save'
  },
  {
    id: '8',
    type: 'created',
    description: 'تم إنشاء',
    documentName: 'اتفاقية عدم إفشاء',
    timestamp: 'منذ 3 أيام',
    icon: 'Plus'
  },
  {
    id: '9',
    type: 'downloaded',
    description: 'تم تحميل',
    documentName: 'تحليل المنافسين',
    timestamp: 'منذ أسبوع',
    icon: 'Download'
  },
  {
    id: '10',
    type: 'edited',
    description: 'تم تعديل',
    documentName: 'خطة الموارد البشرية',
    timestamp: 'منذ أسبوع',
    icon: 'Edit'
  }
]

// Latest Documents Data
export const getLatestDocuments = (): Document[] => [
  {
    id: '1',
    name: 'خطة عمل مشروع التجارة الإلكترونية',
    type: 'خطة عمل',
    typeIcon: 'FileText',
    createdAt: 'منذ ساعتين',
    size: '2.4 MB'
  },
  {
    id: '2',
    name: 'دراسة جدوى مطعم الوجبات السريعة',
    type: 'دراسة جدوى',
    typeIcon: 'TrendingUp',
    createdAt: 'منذ 4 ساعات',
    size: '1.8 MB'
  },
  {
    id: '3',
    name: 'عقد شراكة تجارية',
    type: 'عقد',
    typeIcon: 'FileCheck',
    createdAt: 'أمس',
    size: '856 KB'
  },
  {
    id: '4',
    name: 'تقرير الأداء الشهري',
    type: 'تقرير',
    typeIcon: 'BarChart3',
    createdAt: 'أمس',
    size: '3.2 MB'
  },
  {
    id: '5',
    name: 'عرض تقديمي للمستثمرين',
    type: 'عرض تقديمي',
    typeIcon: 'Presentation',
    createdAt: 'منذ يومين',
    size: '4.1 MB'
  }
]

// AI Recommendations Data
export const getRecommendations = (): Recommendation[] => [
  {
    id: '1',
    title: 'أنشئ تقريراً شهرياً',
    description: 'بناءً على مشروعك الأخير، يمكنك إنشاء تقرير أداء شهري',
    icon: 'BarChart3',
    action: 'إنشاء تقرير'
  },
  {
    id: '2',
    title: 'جرب قالب عرض تقديمي جديد',
    description: 'قوالب عروض تقديمية احترافية لمشاريعك القادمة',
    icon: 'Presentation',
    action: 'استكشاف القوالب'
  },
  {
    id: '3',
    title: 'حسّن عقداً قديماً',
    description: 'قم بتحديث عقودك القديمة بنسخ محسنة ومطورة',
    icon: 'FileCheck',
    action: 'تحسين العقد'
  }
]

// User Statistics
export const getUserStats = () => ({
  documentsThisWeek: 3,
  totalDocuments: 12,
  activeProjects: 3,
  completedProjects: 1,
  planUsage: {
    used: 3,
    total: 10,
    percentage: 30
  }
})
