'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { Mail, ArrowLeft, ArrowRight, CheckCircle } from 'lucide-react'
import { motion } from 'framer-motion'
import { toast } from 'sonner'
import { supabase } from '@/lib/supabase'
import { validateEmail } from '@/lib/validation'

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('')
  const [errors, setErrors] = useState<{[key: string]: string}>({})
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Enhanced form validation
    const newErrors: {[key: string]: string} = {}

    // Email validation
    const emailValidation = validateEmail(email)
    if (!emailValidation.isValid) {
      newErrors.email = emailValidation.message
    }

    setErrors(newErrors)

    if (Object.keys(newErrors).length === 0) {
      setIsLoading(true)

      try {
        const { error } = await supabase.auth.resetPasswordForEmail(email, {
          redirectTo: `${window.location.origin}/auth/reset-password`
        })

        if (error) {
          toast.error('حدث خطأ أثناء إرسال رابط إعادة التعيين')
        } else {
          setIsSubmitted(true)
        }
      } catch (error) {
        toast.error('حدث خطأ غير متوقع')
      } finally {
        setIsLoading(false)
      }
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setEmail(value)

    // Real-time email validation
    if (value) {
      const emailValidation = validateEmail(value)
      if (!emailValidation.isValid) {
        setErrors(prev => ({ ...prev, email: emailValidation.message }))
      } else {
        setErrors(prev => ({ ...prev, email: '' }))
      }
    } else {
      setErrors(prev => ({ ...prev, email: '' }))
    }
  }

  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="text-center"
      >
        <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
          <CheckCircle className="w-8 h-8 text-green-400" />
        </div>
        
        <h1 className="text-3xl font-bold text-white mb-4 font-vazirmatn">
          تم إرسال الرابط
        </h1>
        
        <p className="text-slate-400 mb-6 font-vazirmatn leading-relaxed">
          تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني
          <br />
          <span className="text-indigo-400 font-medium">{email}</span>
        </p>
        
        <p className="text-sm text-slate-500 mb-8 font-vazirmatn">
          تحقق من صندوق الوارد وصندوق الرسائل المزعجة. قد يستغرق الأمر بضع دقائق.
        </p>
        
        <div className="space-y-4">
          <Button
            onClick={() => setIsSubmitted(false)}
            variant="secondary"
            size="lg"
            className="w-full font-vazirmatn"
          >
            إرسال مرة أخرى
          </Button>
          
          <Link href="/auth/login">
            <Button
              variant="ghost"
              size="lg"
              className="w-full font-vazirmatn"
            >
              <ArrowRight className="w-5 h-5 ml-2 rtl:mr-2 rtl:ml-0" />
              العودة لتسجيل الدخول
            </Button>
          </Link>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-white mb-2 font-vazirmatn">
          نسيت كلمة المرور؟
        </h1>
        <p className="text-slate-400 font-vazirmatn">
          لا تقلق، سنرسل لك رابط إعادة تعيين كلمة المرور
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Email Field */}
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-slate-300 mb-2 font-vazirmatn">
            البريد الإلكتروني
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 right-0 rtl:left-0 rtl:right-auto pr-3 rtl:pl-3 rtl:pr-0 flex items-center pointer-events-none">
              <Mail className="h-5 w-5 text-slate-400" />
            </div>
            <input
              id="email"
              name="email"
              type="email"
              value={email}
              onChange={handleInputChange}
              className={`block w-full pr-10 rtl:pl-10 rtl:pr-3 py-3 px-3 border rounded-lg bg-slate-700/50 text-white placeholder-slate-400 font-vazirmatn focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 ${
                errors.email ? 'border-red-500' : 'border-slate-600'
              }`}
              placeholder="أدخل بريدك الإلكتروني"
            />
          </div>
          {errors.email && (
            <p className="mt-1 text-sm text-red-400 font-vazirmatn">{errors.email}</p>
          )}
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          variant="gradient"
          size="lg"
          disabled={isLoading}
          className="w-full font-vazirmatn text-lg py-3 shadow-2xl shadow-indigo-500/25 hover:shadow-indigo-500/40 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'جاري الإرسال...' : 'إرسال رابط إعادة التعيين'}
          {!isLoading && <ArrowLeft className="w-5 h-5 mr-2 group-hover:translate-x-1 transition-transform" />}
        </Button>
      </form>

      {/* Back to Login */}
      <div className="mt-8 text-center">
        <Link
          href="/auth/login"
          className="inline-flex items-center text-slate-400 hover:text-slate-300 font-vazirmatn transition-colors duration-200"
        >
          <ArrowRight className="w-4 h-4 ml-1 rtl:mr-1 rtl:ml-0" />
          العودة لتسجيل الدخول
        </Link>
      </div>

      {/* Help Text */}
      <div className="mt-8 p-4 bg-slate-700/30 rounded-lg border border-slate-600/50">
        <h3 className="text-sm font-medium text-slate-300 mb-2 font-vazirmatn">
          لم تتلق الرسالة؟
        </h3>
        <ul className="text-sm text-slate-400 space-y-1 font-vazirmatn">
          <li>• تحقق من صندوق الرسائل المزعجة</li>
          <li>• تأكد من صحة عنوان البريد الإلكتروني</li>
          <li>• انتظر بضع دقائق قبل المحاولة مرة أخرى</li>
        </ul>
      </div>
    </motion.div>
  )
}
