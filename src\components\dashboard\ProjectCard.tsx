'use client'

import { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import {
  Briefcase,
  BarChart3,
  TrendingUp,
  Calculator,
  BookOpen,
  FileText,
  Calendar,
  FileCheck,
  MoreVertical,
  Edit,
  Trash2,
  ExternalLink,
  Copy,
  Archive,
  Share2,
  Clock,
  Activity
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Project } from '@/types/database'

// Status labels for display
const PROJECT_STATUS_LABELS: Record<string, string> = {
  active: 'نشط',
  paused: 'متوقف',
  draft: 'مسودة',
  completed: 'مكتمل'
}

// Status colors for styling
const PROJECT_STATUS_COLORS: Record<string, string> = {
  active: 'text-green-400 bg-green-500/20',
  paused: 'text-yellow-400 bg-yellow-500/20',
  draft: 'text-blue-400 bg-blue-500/20',
  completed: 'text-purple-400 bg-purple-500/20'
}

interface ProjectCardProps {
  project: Project
  index: number
  onEdit?: (project: Project) => void
  onDelete?: (project: Project) => void
  onView?: (project: Project) => void
  onDuplicate?: (project: Project) => void
  onArchive?: (project: Project) => void
  onShare?: (project: Project) => void
}

const iconMap = {
  Briefcase,
  BarChart3,
  TrendingUp,
  Calculator,
  BookOpen,
  FileText
}

export default function ProjectCard({
  project,
  index,
  onEdit,
  onDelete,
  onView,
  onDuplicate,
  onArchive,
  onShare
}: ProjectCardProps) {
  const router = useRouter()
  const [showActionsMenu, setShowActionsMenu] = useState(false)
  const actionsMenuRef = useRef<HTMLDivElement>(null)
  // Default icon for all projects
  const Icon = Briefcase

  // Get industry-based color scheme
  const getProjectColor = (industry?: string | null) => {
    const colors = [
      'bg-gradient-to-br from-indigo-500 to-purple-500',
      'bg-gradient-to-br from-blue-500 to-cyan-500',
      'bg-gradient-to-br from-green-500 to-emerald-500',
      'bg-gradient-to-br from-orange-500 to-red-500',
      'bg-gradient-to-br from-purple-500 to-pink-500'
    ]
    const index = industry ? industry.length % colors.length : 0
    return colors[index]
  }

  // Calculate progress based on status
  const getProjectProgress = (status: string) => {
    switch (status) {
      case 'completed': return 100
      case 'active': return 65
      case 'paused': return 30
      case 'draft': return 10
      default: return 0
    }
  }

  const progress = getProjectProgress(project.status)

  // Close actions menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (actionsMenuRef.current && !actionsMenuRef.current.contains(event.target as Node)) {
        setShowActionsMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="relative bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 hover:border-slate-600/50 transition-all duration-300 group hover:shadow-2xl hover:shadow-slate-900/50 hover:scale-[1.02] overflow-hidden"
    >
      {/* Background Gradient */}
      <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-white/5 to-transparent rounded-full blur-xl"></div>
      
      {/* Header */}
      <div className="relative z-10 flex items-start justify-between mb-4">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <div className={`relative w-14 h-14 ${getProjectColor(project.industry)} rounded-xl flex items-center justify-center group-hover:scale-110 transition-all duration-300 shadow-lg`}>
            <Icon className="w-7 h-7 text-white" />
            {/* Status Indicator */}
            <div className={`absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-slate-800 ${
              project.status === 'active' ? 'bg-green-500' :
              project.status === 'completed' ? 'bg-blue-500' :
              project.status === 'paused' ? 'bg-yellow-500' :
              'bg-slate-500'
            }`}></div>
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 rtl:space-x-reverse mb-1">
              <h3 className="text-lg font-semibold text-white font-vazirmatn truncate">
                {project.name}
              </h3>
              {/* Document Count Badge */}
              <div className="flex items-center space-x-1 rtl:space-x-reverse bg-indigo-500/20 text-indigo-400 px-2 py-1 rounded-full text-xs font-medium">
                <FileCheck className="w-3 h-3" />
                <span>{project.documents_count}</span>
              </div>
            </div>
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border ${PROJECT_STATUS_COLORS[project.status]} font-vazirmatn`}>
                {PROJECT_STATUS_LABELS[project.status]}
              </span>
              <span className="text-xs text-slate-400 font-vazirmatn">
                {project.industry || 'مشروع عام'}
              </span>
            </div>
          </div>
        </div>

        {/* Actions Menu */}
        <div className="relative" ref={actionsMenuRef}>
          <button
            onClick={() => setShowActionsMenu(!showActionsMenu)}
            className="opacity-0 group-hover:opacity-100 p-2 text-slate-400 hover:text-white hover:bg-slate-700/50 rounded-lg transition-all duration-200"
          >
            <MoreVertical className="w-4 h-4" />
          </button>

          {/* Actions Dropdown */}
          {showActionsMenu && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -10 }}
              className="absolute left-0 rtl:right-0 rtl:left-auto top-full mt-2 w-48 bg-slate-800 border border-slate-700/50 rounded-xl shadow-2xl z-20 overflow-hidden"
            >
              <div className="py-2">
                <button
                  onClick={() => { onDuplicate?.(project); setShowActionsMenu(false) }}
                  className="w-full px-4 py-2 text-right rtl:text-left hover:bg-slate-700/50 transition-colors duration-200 flex items-center space-x-3 rtl:space-x-reverse text-slate-300 hover:text-white"
                >
                  <Copy className="w-4 h-4" />
                  <span className="text-sm font-vazirmatn">نسخ المشروع</span>
                </button>
                <button
                  onClick={() => { onShare?.(project); setShowActionsMenu(false) }}
                  className="w-full px-4 py-2 text-right rtl:text-left hover:bg-slate-700/50 transition-colors duration-200 flex items-center space-x-3 rtl:space-x-reverse text-slate-300 hover:text-white"
                >
                  <Share2 className="w-4 h-4" />
                  <span className="text-sm font-vazirmatn">مشاركة</span>
                </button>
                <button
                  onClick={() => { onArchive?.(project); setShowActionsMenu(false) }}
                  className="w-full px-4 py-2 text-right rtl:text-left hover:bg-slate-700/50 transition-colors duration-200 flex items-center space-x-3 rtl:space-x-reverse text-slate-300 hover:text-white"
                >
                  <Archive className="w-4 h-4" />
                  <span className="text-sm font-vazirmatn">أرشفة</span>
                </button>
                <div className="border-t border-slate-700/50 my-1"></div>
                <button
                  onClick={() => { onDelete?.(project); setShowActionsMenu(false) }}
                  className="w-full px-4 py-2 text-right rtl:text-left hover:bg-red-500/10 transition-colors duration-200 flex items-center space-x-3 rtl:space-x-reverse text-slate-300 hover:text-red-300"
                >
                  <Trash2 className="w-4 h-4" />
                  <span className="text-sm font-vazirmatn">حذف</span>
                </button>
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Description */}
      <p className="relative z-10 text-slate-300 text-sm font-vazirmatn mb-4 leading-relaxed line-clamp-2">
        {project.description}
      </p>

      {/* Enhanced Progress Bar */}
      <div className="relative z-10 mb-4">
        <div className="flex justify-between items-center text-xs font-vazirmatn mb-3">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <Activity className="w-3 h-3 text-slate-400" />
            <span className="text-slate-400">التقدم</span>
          </div>
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <span className={`font-semibold ${
              progress >= 80 ? 'text-green-400' :
              progress >= 50 ? 'text-yellow-400' :
              'text-slate-400'
            }`}>
              {progress}%
            </span>
            <div className={`w-2 h-2 rounded-full ${
              progress >= 80 ? 'bg-green-400' :
              progress >= 50 ? 'bg-yellow-400' :
              'bg-slate-400'
            }`}></div>
          </div>
        </div>
        <div className="relative w-full bg-slate-700/50 rounded-full h-3 overflow-hidden">
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 1, delay: index * 0.1 + 0.5 }}
            className={`h-3 rounded-full ${
              progress >= 80 ? 'bg-gradient-to-r from-green-500 to-emerald-500' :
              progress >= 50 ? 'bg-gradient-to-r from-yellow-500 to-orange-500' :
              'bg-gradient-to-r from-indigo-500 to-purple-500'
            }`}
          />
          {/* Progress Glow Effect */}
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 1, delay: index * 0.1 + 0.5 }}
            className={`absolute top-0 h-3 rounded-full opacity-50 blur-sm ${
              progress >= 80 ? 'bg-gradient-to-r from-green-400 to-emerald-400' :
              progress >= 50 ? 'bg-gradient-to-r from-yellow-400 to-orange-400' :
              'bg-gradient-to-r from-indigo-400 to-purple-400'
            }`}
          />
        </div>
      </div>

      {/* Enhanced Stats */}
      <div className="relative z-10 mb-4">
        <div className="grid grid-cols-2 gap-3">
          <div className="flex items-center space-x-2 rtl:space-x-reverse text-xs font-vazirmatn">
            <div className="w-6 h-6 bg-slate-700/50 rounded-lg flex items-center justify-center">
              <FileCheck className="w-3 h-3 text-indigo-400" />
            </div>
            <div>
              <div className="text-white font-semibold">{project.documents_count}</div>
              <div className="text-slate-500">مستندات</div>
            </div>
          </div>
          <div className="flex items-center space-x-2 rtl:space-x-reverse text-xs font-vazirmatn">
            <div className="w-6 h-6 bg-slate-700/50 rounded-lg flex items-center justify-center">
              <Clock className="w-3 h-3 text-green-400" />
            </div>
            <div>
              <div className="text-white font-semibold">نشط</div>
              <div className="text-slate-500">{new Date(project.updated_at).toLocaleDateString('ar-SA')}</div>
            </div>
          </div>
        </div>

        {/* Last Activity */}
        <div className="mt-3 pt-3 border-t border-slate-700/30">
          <div className="flex items-center space-x-2 rtl:space-x-reverse text-xs text-slate-400 font-vazirmatn">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span>آخر نشاط: {new Date(project.updated_at).toLocaleDateString('ar-SA')}</span>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="relative z-10 flex items-center space-x-2 rtl:space-x-reverse">
        <Button
          variant="primary"
          size="sm"
          className="flex-1 font-vazirmatn text-sm"
          onClick={() => {
            // Navigate to project details page
            router.push(`/dashboard/projects/${encodeURIComponent(project.id)}`)
            // Also call the original onView if provided
            onView?.(project)
          }}
        >
          <ExternalLink className="w-4 h-4 ml-1 rtl:mr-1 rtl:ml-0" />
          فتح المشروع
        </Button>
        <Button
          variant="secondary"
          size="sm"
          className="font-vazirmatn text-sm border border-slate-600/50 hover:border-slate-500/50 backdrop-blur-sm"
          onClick={() => {
            // Navigate to edit page
            router.push(`/dashboard/projects/create/manual-entry?edit=${encodeURIComponent(project.id)}`)
            // Also call the original onEdit if provided
            onEdit?.(project)
          }}
        >
          <Edit className="w-4 h-4 ml-1 rtl:mr-1 rtl:ml-0" />
          تعديل
        </Button>
      </div>
    </motion.div>
  )
}
