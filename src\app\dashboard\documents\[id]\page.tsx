'use client'

import { useState, useEffect, useRef } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { ArrowLeft, FileText, Download, Share2, Edit3, Save, Eye, Calendar, User, Building } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { GeneratedDocument } from '@/types/document-generation'
import DocumentSidebar from '@/components/documents/DocumentSidebar'

export default function DocumentViewPage() {
  const params = useParams()
  const router = useRouter()
  const [document, setDocument] = useState<GeneratedDocument | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [activeSection, setActiveSection] = useState<string>('')
  const sectionRefs = useRef<{ [key: string]: HTMLDivElement | null }>({})

  useEffect(() => {
    const loadDocument = async () => {
      setIsLoading(true)
      try {
        const response = await fetch(`/api/documents/${params.id}`)

        if (!response.ok) {
          if (response.status === 404) {
            setDocument(null)
            return
          }
          throw new Error('Failed to fetch document')
        }

        const data = await response.json()
        setDocument(data.document)

        // Set the first section as active by default
        if (data.document?.sections?.length > 0) {
          setActiveSection(data.document.sections[0].id)
        }
      } catch (error) {
        console.error('Error loading document:', error)
        setDocument(null)
      } finally {
        setIsLoading(false)
      }
    }

    if (params.id) {
      loadDocument()
    }
  }, [params.id])

  // Handle section navigation
  const handleSectionClick = (sectionId: string) => {
    const element = sectionRefs.current[sectionId]
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      })
      setActiveSection(sectionId)
    }
  }

  // Track active section on scroll
  useEffect(() => {
    const handleScroll = () => {
      if (!document?.sections) return

      const scrollPosition = window.scrollY + 200 // Offset for header

      for (let i = document.sections.length - 1; i >= 0; i--) {
        const section = document.sections[i]
        const element = sectionRefs.current[section.id]

        if (element && element.offsetTop <= scrollPosition) {
          setActiveSection(section.id)
          break
        }
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [document?.sections])

  const handleSave = async () => {
    if (!document) return

    setIsSaving(true)
    try {
      const response = await fetch(`/api/documents/${document.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: document.title,
          description: document.description,
          sections: document.sections
        })
      })

      if (!response.ok) {
        throw new Error('Failed to save document')
      }

      setIsEditing(false)
    } catch (error) {
      console.error('Error saving document:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const handleExport = () => {
    if (!document) return
    
    const content = document.sections
      .map(section => `${section.title}\n${'='.repeat(section.title.length)}\n\n${section.content}\n\n`)
      .join('')
    
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${document.title}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleShare = () => {
    if (document && navigator.share) {
      navigator.share({
        title: document.title,
        text: document.description,
        url: window.location.href
      })
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 animate-spin rounded-full border-2 border-indigo-500 border-t-transparent mx-auto mb-4" />
          <p className="text-slate-400 font-vazirmatn">جاري تحميل المستند...</p>
        </div>
      </div>
    )
  }

  if (!document) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <FileText className="w-16 h-16 text-slate-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-white font-vazirmatn mb-2">المستند غير موجود</h2>
          <p className="text-slate-400 font-vazirmatn mb-6">لم يتم العثور على المستند المطلوب</p>
          <Button onClick={() => router.back()} variant="ghost" className="font-vazirmatn">
            <ArrowLeft className="w-4 h-4 ml-2" />
            العودة
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-900 flex">
      {/* Sidebar */}
      {document && (
        <DocumentSidebar
          sections={document.sections}
          activeSection={activeSection}
          onSectionClick={handleSectionClick}
          className="hidden lg:block"
        />
      )}

      {/* Main Content */}
      <div className="flex-1 min-h-screen">
        {/* Header */}
        <div className="sticky top-0 z-30 bg-slate-900/95 backdrop-blur-sm border-b border-slate-700">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                onClick={() => router.back()}
                variant="ghost"
                size="sm"
                className="text-slate-400 hover:text-white font-vazirmatn"
              >
                <ArrowLeft className="w-4 h-4 ml-2" />
                العودة إلى المستندات
              </Button>
              <div className="w-px h-6 bg-slate-600" />
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <FileText className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h1 className="text-lg font-bold text-white font-vazirmatn">
                    {document.title}
                  </h1>
                  <div className="flex items-center gap-4 text-sm text-slate-400 font-vazirmatn">
                    <span className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      {new Date(document.createdAt).toLocaleDateString('ar-SA')}
                    </span>
                    <span className="flex items-center gap-1">
                      <User className="w-3 h-3" />
                      {document.tier === 'professional' ? 'احترافية' : 'سريعة'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {isEditing ? (
                <>
                  <Button
                    onClick={() => setIsEditing(false)}
                    variant="ghost"
                    size="sm"
                    className="font-vazirmatn text-slate-400 hover:text-white"
                  >
                    إلغاء
                  </Button>
                  <Button
                    onClick={handleSave}
                    disabled={isSaving}
                    size="sm"
                    className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 font-vazirmatn"
                  >
                    {isSaving ? (
                      <div className="w-4 h-4 animate-spin rounded-full border-2 border-white border-t-transparent ml-2" />
                    ) : (
                      <Save className="w-4 h-4 ml-2" />
                    )}
                    {isSaving ? 'جاري الحفظ...' : 'حفظ'}
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    onClick={() => setIsEditing(true)}
                    variant="ghost"
                    size="sm"
                    className="font-vazirmatn text-slate-400 hover:text-white"
                  >
                    <Edit3 className="w-4 h-4 ml-2" />
                    تعديل
                  </Button>
                  <Button
                    onClick={handleExport}
                    variant="ghost"
                    size="sm"
                    className="font-vazirmatn text-slate-400 hover:text-white"
                  >
                    <Download className="w-4 h-4 ml-2" />
                    تصدير
                  </Button>
                  <Button
                    onClick={handleShare}
                    variant="ghost"
                    size="sm"
                    className="font-vazirmatn text-slate-400 hover:text-white"
                  >
                    <Share2 className="w-4 h-4 ml-2" />
                    مشاركة
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

        {/* Content */}
        <div className="max-w-4xl mx-auto px-4 py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="space-y-8"
          >
            {document.sections.map((section, index) => (
              <motion.div
                key={section.id}
                ref={(el) => {
                  sectionRefs.current[section.id] = el
                }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-2xl p-8 shadow-lg"
                id={section.id}
              >
                <h2 className="text-2xl font-bold text-slate-800 font-vazirmatn mb-6 pb-4 border-b border-slate-200">
                  {section.title}
                </h2>
                <div className="prose prose-lg max-w-none text-slate-700 font-vazirmatn leading-relaxed">
                  {section.content.split('\n').map((paragraph, pIndex) => (
                    <p key={pIndex} className="mb-4 text-right">
                      {paragraph}
                    </p>
                  ))}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Mobile Sidebar */}
        {document && (
          <DocumentSidebar
            sections={document.sections}
            activeSection={activeSection}
            onSectionClick={handleSectionClick}
            className="lg:hidden"
          />
        )}
      </div>
    </div>
  )
}
