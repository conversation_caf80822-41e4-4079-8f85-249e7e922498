{"name": "builddocwithai", "private": true, "version": "0.1.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@supabase/auth-helpers-nextjs": "^0.10.0", "@tailwindcss/line-clamp": "^0.4.4", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.23.3", "lucide-react": "^0.462.0", "multer": "^2.0.2", "next": "^14.2.15", "pdf-parse": "^1.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "sonner": "^2.0.6", "tailwind-merge": "^2.5.2", "tailwindcss": "^3.4.11", "tailwindcss-animate": "^1.0.7", "tailwindcss-rtl": "^0.9.0", "typescript": "^5.5.3"}, "devDependencies": {"@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-next": "^14.2.15", "postcss": "^8.4.47"}}