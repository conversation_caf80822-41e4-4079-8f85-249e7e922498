'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, FileText, Download, Eye, Save, Share2, CheckCircle, Clock, FileCheck } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { GeneratedDocument } from '@/types/document-generation'
import { useRouter } from 'next/navigation'

interface DocumentCompletionModalProps {
  isOpen: boolean
  onClose: () => void
  document: GeneratedDocument | null
}

export default function DocumentCompletionModal({
  isOpen,
  onClose,
  document
}: DocumentCompletionModalProps) {
  const router = useRouter()
  const [isSaving, setIsSaving] = useState(false)
  const [isExporting, setIsExporting] = useState(false)

  const handlePreview = () => {
    if (document) {
      router.push(`/dashboard/documents/${document.id}`)
      onClose()
    }
  }

  const handleDocumentComplete = () => {
    if (document) {
      router.push(`/dashboard/documents/${document.id}`)
      onClose()
    }
  }

  const handleSave = async () => {
    if (!document) return
    
    setIsSaving(true)
    try {
      // Mock save to database
      await new Promise(resolve => setTimeout(resolve, 1500))
      console.log('Document saved:', document.id)
    } catch (error) {
      console.error('Error saving document:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const handleExport = async () => {
    if (!document) return

    setIsExporting(true)
    try {
      // Mock export functionality
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Create a simple text export
      const content = document.sections
        .map(section => `${section.title}\n${'='.repeat(section.title.length)}\n\n${section.content}\n\n`)
        .join('')

      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const a = globalThis.document.createElement('a')
      a.href = url
      a.download = `${document.title}.txt`
      globalThis.document.body.appendChild(a)
      a.click()
      globalThis.document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error exporting document:', error)
    } finally {
      setIsExporting(false)
    }
  }

  const handleShare = () => {
    if (document && navigator.share) {
      navigator.share({
        title: document.title,
        text: document.description,
        url: window.location.origin + `/dashboard/documents/${document.id}`
      })
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (!isOpen || !document) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.3 }}
          className="bg-gradient-to-br from-slate-800 via-slate-800 to-slate-900 border border-slate-700 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="relative p-6 border-b border-slate-700/50 bg-gradient-to-r from-slate-800/50 to-slate-900/50">
            <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-green-500 to-emerald-500"></div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg">
                  <CheckCircle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white font-vazirmatn">
                    تم إنشاء المستند بنجاح!
                  </h2>
                  <p className="text-slate-400 font-vazirmatn">
                    {document.title}
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-slate-400 hover:text-white transition-colors duration-200 p-2 hover:bg-slate-700/50 rounded-lg"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            {/* Success Stats */}
            <div className="mt-6 grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="bg-green-500/10 border border-green-500/20 rounded-xl p-4">
                <div className="text-green-400 font-vazirmatn text-sm">الأقسام المكتملة</div>
                <div className="text-2xl font-bold text-white font-vazirmatn">
                  {document.metadata?.completedSections || document.sections.length}
                </div>
              </div>
              <div className="bg-blue-500/10 border border-blue-500/20 rounded-xl p-4">
                <div className="text-blue-400 font-vazirmatn text-sm">إجمالي الوقت</div>
                <div className="text-2xl font-bold text-white font-vazirmatn">
                  {document.metadata?.actualTotalTime ? formatTime(document.metadata.actualTotalTime) : '15:30'}
                </div>
              </div>
              <div className="bg-purple-500/10 border border-purple-500/20 rounded-xl p-4">
                <div className="text-purple-400 font-vazirmatn text-sm">نوع الخطة</div>
                <div className="text-2xl font-bold text-white font-vazirmatn">
                  {document.tier === 'professional' ? 'احترافية' : 'سريعة'}
                </div>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto" style={{ maxHeight: 'calc(90vh - 300px)' }}>
            {/* Document Preview */}
            <div className="mb-6">
              <h3 className="text-xl font-bold text-white font-vazirmatn mb-4">
                معاينة المستند
              </h3>
              <div className="bg-slate-700/30 rounded-xl p-6 border border-slate-600/30">
                <div className="flex items-start gap-4">
                  <div className="w-16 h-20 bg-gradient-to-b from-slate-600 to-slate-700 rounded-lg flex items-center justify-center shadow-lg">
                    <FileText className="w-8 h-8 text-slate-300" />
                  </div>
                  <div className="flex-1">
                    <h4 className="text-lg font-bold text-white font-vazirmatn mb-2">
                      {document.title}
                    </h4>
                    <p className="text-slate-400 font-vazirmatn mb-4 leading-relaxed">
                      {document.description}
                    </p>
                    <div className="flex items-center gap-4 text-sm text-slate-500 font-vazirmatn">
                      <span className="flex items-center gap-1">
                        <FileCheck className="w-4 h-4" />
                        {document.sections.length} قسم
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        تم الإنشاء الآن
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Sections Summary */}
            <div className="mb-6">
              <h3 className="text-xl font-bold text-white font-vazirmatn mb-4">
                أقسام المستند
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {document.sections.map((section, index) => (
                  <div
                    key={section.id}
                    className="bg-slate-700/20 rounded-lg p-3 border border-slate-600/20"
                  >
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-400" />
                      <span className="text-white font-vazirmatn text-sm">
                        {section.title}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button
                onClick={handlePreview}
                className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 font-vazirmatn"
              >
                <Eye className="w-4 h-4 ml-2" />
                معاينة المستند
              </Button>
              
              <Button
                onClick={handleSave}
                disabled={isSaving}
                variant="ghost"
                className="bg-slate-700/30 hover:bg-slate-700/50 text-white border border-slate-600/30 font-vazirmatn"
              >
                {isSaving ? (
                  <div className="w-4 h-4 animate-spin rounded-full border-2 border-white border-t-transparent ml-2" />
                ) : (
                  <Save className="w-4 h-4 ml-2" />
                )}
                {isSaving ? 'جاري الحفظ...' : 'حفظ المستند'}
              </Button>
              
              <Button
                onClick={handleExport}
                disabled={isExporting}
                variant="ghost"
                className="bg-slate-700/30 hover:bg-slate-700/50 text-white border border-slate-600/30 font-vazirmatn"
              >
                {isExporting ? (
                  <div className="w-4 h-4 animate-spin rounded-full border-2 border-white border-t-transparent ml-2" />
                ) : (
                  <Download className="w-4 h-4 ml-2" />
                )}
                {isExporting ? 'جاري التصدير...' : 'تصدير'}
              </Button>
              
              <Button
                onClick={handleShare}
                variant="ghost"
                className="bg-slate-700/30 hover:bg-slate-700/50 text-white border border-slate-600/30 font-vazirmatn"
              >
                <Share2 className="w-4 h-4 ml-2" />
                مشاركة
              </Button>
            </div>
          </div>

          {/* Footer */}
          <div className="p-6 border-t border-slate-700/50 bg-slate-800/30">
            <div className="flex items-center justify-between">
              <div className="text-slate-400 font-vazirmatn text-sm">
                يمكنك الآن عرض المستند وتعديله أو تصديره بصيغ مختلفة
              </div>
              <Button
                onClick={onClose}
                variant="ghost"
                className="font-vazirmatn text-slate-400 hover:text-white"
              >
                إغلاق
              </Button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
