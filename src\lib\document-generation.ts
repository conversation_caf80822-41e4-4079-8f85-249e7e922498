import { DocumentCreationData } from '@/components/documents/DocumentCreationModal'

export interface DocumentSection {
  id: string
  title: string
  content: string
  isLocked: boolean
  order: number
}

export interface GeneratedDocument {
  id: string
  title: string
  description: string
  sections: DocumentSection[]
  tier: 'quick' | 'professional'
  templateId: string
  createdAt: string
  updatedAt: string
}

// Business Plan Template Sections
const businessPlanSections = {
  professional: [
    { id: 'executive-summary', title: 'الملخص التنفيذي', order: 1, isLocked: false },
    { id: 'company-overview', title: 'نظرة عامة على الشركة ونموذج العمل', order: 2, isLocked: false },
    { id: 'market-overview', title: 'نظرة عامة على السوق', order: 3, isLocked: false },
    { id: 'market-demand', title: 'الطلب في السوق', order: 4, isLocked: false },
    { id: 'market-supply', title: 'العرض في السوق', order: 5, isLocked: false },
    { id: 'supply-demand-gap', title: 'فجوة العرض والطلب', order: 6, isLocked: false },
    { id: 'swot-analysis', title: 'تحليل SWOT', order: 7, isLocked: false },
    { id: 'market-penetration', title: 'خطة اختراق السوق', order: 8, isLocked: false },
    { id: 'technical-analysis', title: 'التحليل الفني وتحليل الموارد', order: 9, isLocked: false },
    { id: 'workforce-assessment', title: 'تقييم القوى العاملة', order: 10, isLocked: false },
    { id: 'licensing-requirements', title: 'متطلبات الترخيص', order: 11, isLocked: false },
    { id: 'investment-costs', title: 'تكلفة الاستثمار', order: 12, isLocked: false },
    { id: 'financing-plan', title: 'خطة التمويل', order: 13, isLocked: false },
    { id: 'financial-assumptions', title: 'الافتراضات والمبررات المالية', order: 14, isLocked: false },
    { id: 'financial-statements', title: 'البيانات المالية', order: 15, isLocked: false },
    { id: 'break-even-analysis', title: 'تحليل نقطة التعادل', order: 16, isLocked: false },
    { id: 'risk-analysis', title: 'تحليل المخاطر', order: 17, isLocked: false },
    { id: 'executive-action-plan', title: 'خطة العمل التنفيذية', order: 18, isLocked: false },
    { id: 'appendix', title: 'الملحق', order: 19, isLocked: false }
  ],
  quick: [
    { id: 'executive-summary', title: 'الملخص التنفيذي', order: 1, isLocked: false },
    { id: 'company-overview', title: 'نظرة عامة على الشركة ونموذج العمل', order: 2, isLocked: false },
    { id: 'market-overview', title: 'نظرة عامة على السوق', order: 3, isLocked: false },
    { id: 'swot-analysis', title: 'تحليل SWOT', order: 4, isLocked: false },
    { id: 'financial-statements', title: 'البيانات المالية', order: 5, isLocked: false },
    { id: 'risk-analysis', title: 'تحليل المخاطر', order: 6, isLocked: false },
    // Locked sections for free tier
    { id: 'market-demand', title: 'الطلب في السوق', order: 7, isLocked: true },
    { id: 'market-supply', title: 'العرض في السوق', order: 8, isLocked: true },
    { id: 'supply-demand-gap', title: 'فجوة العرض والطلب', order: 9, isLocked: true },
    { id: 'market-penetration', title: 'خطة اختراق السوق', order: 10, isLocked: true },
    { id: 'technical-analysis', title: 'التحليل الفني وتحليل الموارد', order: 11, isLocked: true },
    { id: 'workforce-assessment', title: 'تقييم القوى العاملة', order: 12, isLocked: true },
    { id: 'licensing-requirements', title: 'متطلبات الترخيص', order: 13, isLocked: true },
    { id: 'investment-costs', title: 'تكلفة الاستثمار', order: 14, isLocked: true },
    { id: 'financing-plan', title: 'خطة التمويل', order: 15, isLocked: true },
    { id: 'financial-assumptions', title: 'الافتراضات والمبررات المالية', order: 16, isLocked: true },
    { id: 'break-even-analysis', title: 'تحليل نقطة التعادل', order: 17, isLocked: true },
    { id: 'executive-action-plan', title: 'خطة العمل التنفيذية', order: 18, isLocked: true },
    { id: 'appendix', title: 'الملحق', order: 19, isLocked: true }
  ]
}

// Feasibility Study Template Sections
const feasibilityStudySections = {
  professional: [
    { id: 'executive-summary', title: 'الملخص التنفيذي', order: 1, isLocked: false },
    { id: 'project-description', title: 'وصف المشروع', order: 2, isLocked: false },
    { id: 'market-analysis', title: 'تحليل السوق وجدواه', order: 3, isLocked: false },
    { id: 'technical-feasibility', title: 'دراسة الجدوى الفنية', order: 4, isLocked: false },
    { id: 'organizational-feasibility', title: 'دراسة الجدوى التنظيمية والإدارية', order: 5, isLocked: false },
    { id: 'financial-feasibility', title: 'دراسة الجدوى المالية', order: 6, isLocked: false },
    { id: 'risk-assessment', title: 'تقييم المخاطر والتخفيف منها', order: 7, isLocked: false },
    { id: 'implementation-plan', title: 'خطة التنفيذ والجدول الزمني', order: 8, isLocked: false },
    { id: 'conclusions', title: 'الخلاصة والتوصيات', order: 9, isLocked: false },
    { id: 'appendix', title: 'الملحق (المستندات الداعمة)', order: 10, isLocked: false },
    { id: 'environmental-impact', title: 'تقييم الأثر البيئي والتحليل', order: 11, isLocked: false },
    { id: 'social-impact', title: 'تقييم التأثير الاجتماعي وجدوى المجتمع', order: 12, isLocked: false },
    { id: 'operational-feasibility', title: 'الجدوى التشغيلية وإدارة الموارد', order: 13, isLocked: false },
    { id: 'legal-compliance', title: 'تقييم الامتثال القانوني والتنظيمي', order: 14, isLocked: false },
    { id: 'technology-analysis', title: 'تحليل التكنولوجيا والابتكار', order: 15, isLocked: false },
    { id: 'supply-chain', title: 'تقييم سلسلة التوريد واللوجستيات', order: 16, isLocked: false },
    { id: 'scalability-analysis', title: 'تحليل قابلية التوسع وإمكانات النمو', order: 17, isLocked: false },
    { id: 'competitive-position', title: 'الموقع التنافسي واستراتيجية دخول السوق', order: 18, isLocked: false },
    { id: 'sensitivity-analysis', title: 'تحليل الحساسية والسيناريوهات', order: 19, isLocked: false },
    { id: 'implementation-roadmap', title: 'خارطة طريق التنفيذ المفصلة', order: 20, isLocked: false }
  ],
  quick: [
    { id: 'executive-summary', title: 'الملخص التنفيذي', order: 1, isLocked: false },
    { id: 'project-description', title: 'وصف المشروع', order: 2, isLocked: false },
    { id: 'market-analysis', title: 'تحليل السوق وجدواه', order: 3, isLocked: false },
    { id: 'financial-feasibility', title: 'دراسة الجدوى المالية', order: 4, isLocked: false },
    { id: 'conclusions', title: 'الخلاصة والتوصيات', order: 5, isLocked: false },
    // Locked sections for free tier
    { id: 'technical-feasibility', title: 'دراسة الجدوى الفنية', order: 6, isLocked: true },
    { id: 'organizational-feasibility', title: 'دراسة الجدوى التنظيمية والإدارية', order: 7, isLocked: true },
    { id: 'risk-assessment', title: 'تقييم المخاطر والتخفيف منها', order: 8, isLocked: true },
    { id: 'implementation-plan', title: 'خطة التنفيذ والجدول الزمني', order: 9, isLocked: true },
    { id: 'appendix', title: 'الملحق (المستندات الداعمة)', order: 10, isLocked: true },
    { id: 'environmental-impact', title: 'تقييم الأثر البيئي والتحليل', order: 11, isLocked: true },
    { id: 'social-impact', title: 'تقييم التأثير الاجتماعي وجدوى المجتمع', order: 12, isLocked: true },
    { id: 'technology-analysis', title: 'تحليل التكنولوجيا والابتكار', order: 13, isLocked: true },
    { id: 'supply-chain', title: 'تقييم سلسلة التوريد واللوجستيات', order: 14, isLocked: true },
    { id: 'scalability-analysis', title: 'تحليل قابلية التوسع وإمكانات النمو', order: 15, isLocked: true },
    { id: 'sensitivity-analysis', title: 'تحليل الحساسية والسيناريوهات', order: 16, isLocked: true },
    { id: 'implementation-roadmap', title: 'خارطة طريق التنفيذ المفصلة', order: 17, isLocked: true }
  ]
}

// Generate content for each section using AI (mock implementation)
const generateSectionContent = async (
  sectionId: string, 
  sectionTitle: string, 
  projectData: DocumentCreationData['projectData'],
  templateId: string
): Promise<string> => {
  // This would typically call your AI service (OpenAI, Gemini, etc.)
  // For now, we'll return mock content based on the section
  
  const { projectName, projectDescription, industry, targetMarket, budget, timeline } = projectData
  
  // Mock content generation based on section type
  switch (sectionId) {
    case 'executive-summary':
      return `# ${sectionTitle}

## نظرة عامة على المشروع
${projectName} هو مشروع ${industry} يهدف إلى ${projectDescription}. يستهدف المشروع ${targetMarket} بميزانية قدرها ${budget} خلال فترة زمنية تقدر بـ ${timeline}.

## الأهداف الرئيسية
- تقديم حلول مبتكرة في مجال ${industry}
- الوصول إلى ${targetMarket} بفعالية
- تحقيق عائد استثمار مجدي خلال ${timeline}

## الفرص المتاحة
يتميز السوق الحالي بوجود فرص نمو كبيرة في قطاع ${industry}، مما يوفر بيئة مناسبة لنجاح ${projectName}.

*تم إنشاء هذا المحتوى بالذكاء الاصطناعي ويمكن تعديله حسب احتياجاتك.*`

    case 'company-overview':
      return `# ${sectionTitle}

## رؤية الشركة
تسعى ${projectName} لتكون الرائدة في مجال ${industry} من خلال تقديم حلول مبتكرة ومتطورة.

## رسالة الشركة
نلتزم بتقديم خدمات عالية الجودة تلبي احتياجات ${targetMarket} وتساهم في تطوير القطاع.

## نموذج العمل
- **القطاع المستهدف**: ${industry}
- **الجمهور المستهدف**: ${targetMarket}
- **الميزانية المخصصة**: ${budget}
- **الإطار الزمني**: ${timeline}

## القيم الأساسية
- الابتكار والتطوير المستمر
- الجودة والتميز في الخدمة
- الشفافية والمصداقية
- المسؤولية الاجتماعية

*تم إنشاء هذا المحتوى بالذكاء الاصطناعي ويمكن تعديله حسب احتياجاتك.*`

    case 'market-overview':
      return `# ${sectionTitle}

## حجم السوق
يشهد قطاع ${industry} نمواً مستمراً مع توقعات إيجابية للسنوات القادمة.

## الاتجاهات السوقية
- زيادة الطلب على الحلول المبتكرة
- نمو الاستثمار في قطاع ${industry}
- تطور احتياجات ${targetMarket}

## الفرص المتاحة
- إمكانية الدخول في شراكات استراتيجية
- توسع السوق المحلي والإقليمي
- الاستفادة من التطورات التكنولوجية

## التحديات
- المنافسة المتزايدة في السوق
- التغيرات في متطلبات العملاء
- الحاجة للاستثمار في التطوير

*تم إنشاء هذا المحتوى بالذكاء الاصطناعي ويمكن تعديله حسب احتياجاتك.*`

    case 'swot-analysis':
      return `# ${sectionTitle}

## نقاط القوة (Strengths)
- فريق عمل متخصص ومؤهل
- رؤية واضحة ومحددة للمشروع
- ميزانية كافية للبدء والتطوير
- فهم عميق لاحتياجات ${targetMarket}

## نقاط الضعف (Weaknesses)
- حداثة العلامة التجارية في السوق
- الحاجة لبناء قاعدة عملاء
- محدودية الخبرة في بعض المجالات

## الفرص (Opportunities)
- نمو السوق في قطاع ${industry}
- إمكانية التوسع الجغرافي
- الاستفادة من التطورات التقنية
- بناء شراكات استراتيجية

## التهديدات (Threats)
- دخول منافسين جدد للسوق
- التغيرات في الأنظمة واللوائح
- التقلبات الاقتصادية
- تغير أذواق العملاء

*تم إنشاء هذا المحتوى بالذكاء الاصطناعي ويمكن تعديله حسب احتياجاتك.*`

    case 'financial-statements':
      return `# ${sectionTitle}

## الميزانية المتوقعة
**إجمالي الاستثمار المطلوب**: ${budget}

## توزيع التكاليف
- تكاليف التأسيس: 30%
- رأس المال العامل: 40%
- التسويق والإعلان: 20%
- الطوارئ: 10%

## الإيرادات المتوقعة
- السنة الأولى: تقدير أولي بناءً على دراسة السوق
- السنة الثانية: نمو متوقع 25-30%
- السنة الثالثة: استقرار وتوسع

## مؤشرات الأداء المالي
- فترة الاسترداد المتوقعة: ${timeline}
- معدل العائد على الاستثمار: يحدد بناءً على الأداء الفعلي
- نقطة التعادل: تحدد بعد بدء العمليات

*تم إنشاء هذا المحتوى بالذكاء الاصطناعي ويمكن تعديله حسب احتياجاتك.*`

    case 'risk-analysis':
      return `# ${sectionTitle}

## المخاطر التشغيلية
- تأخير في بدء العمليات
- صعوبات في التوظيف والتدريب
- مشاكل في سلسلة التوريد

## المخاطر المالية
- تجاوز الميزانية المحددة
- تأخير في تحصيل الإيرادات
- تقلبات في أسعار الصرف

## المخاطر السوقية
- تغيرات في طلب ${targetMarket}
- دخول منافسين جدد
- تغيرات في الأنظمة واللوائح

## استراتيجيات التخفيف
- وضع خطط طوارئ مفصلة
- تنويع مصادر الإيراد
- بناء احتياطيات مالية
- التأمين ضد المخاطر الرئيسية

*تم إنشاء هذا المحتوى بالذكاء الاصطناعي ويمكن تعديله حسب احتياجاتك.*`

    default:
      return `# ${sectionTitle}

هذا القسم يحتاج إلى تطوير المحتوى بناءً على متطلبات ${projectName} الخاصة.

## النقاط الرئيسية
- تحليل مفصل للموضوع
- البيانات والإحصائيات ذات الصلة
- التوصيات والخطوات التالية

## المعلومات المطلوبة
- بيانات خاصة بـ ${industry}
- تحليل ${targetMarket}
- الاعتبارات المالية والزمنية

*تم إنشاء هذا المحتوى بالذكاء الاصطناعي ويمكن تعديله حسب احتياجاتك.*`
  }
}

// Main document generation function
export const generateDocument = async (data: DocumentCreationData): Promise<GeneratedDocument> => {
  const { templateId, title, description, tier, projectData } = data
  
  // Get template sections based on template ID and tier
  let templateSections: any[] = []
  
  if (templateId === 'business-plan') {
    templateSections = businessPlanSections[tier]
  } else if (templateId === 'feasibility-study') {
    templateSections = feasibilityStudySections[tier]
  }
  
  // Generate content for each section
  const sections: DocumentSection[] = []
  
  for (const section of templateSections) {
    let content = ''
    
    if (!section.isLocked) {
      // Generate content for unlocked sections
      content = await generateSectionContent(
        section.id,
        section.title,
        projectData,
        templateId
      )
    } else {
      // Locked content message
      content = `# ${section.title}

🔒 **هذا القسم متاح في النسخة الاحترافية فقط**

للوصول إلى هذا المحتوى المتقدم، يرجى الترقية إلى النسخة الاحترافية التي تتضمن:
- تحليل مفصل ومتخصص
- بيانات وإحصائيات متقدمة
- توصيات عملية قابلة للتطبيق
- دعم فني مخصص

[ترقية إلى النسخة الاحترافية](#upgrade)`
    }
    
    sections.push({
      id: section.id,
      title: section.title,
      content,
      isLocked: section.isLocked,
      order: section.order
    })
  }
  
  // Create the generated document
  const generatedDocument: GeneratedDocument = {
    id: `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    title,
    description,
    sections: sections.sort((a, b) => a.order - b.order),
    tier,
    templateId,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  
  return generatedDocument
}
