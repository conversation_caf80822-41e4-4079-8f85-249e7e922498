'use client'

import { useState } from 'react'
import { DocumentTemplate } from '@/lib/document-types'
import TemplateRequirementsModal from './TemplateRequirementsModal'
import ProjectSelectionModal, { DocumentCreationData } from './ProjectSelectionModal'
import UniversalDocumentGenerationModal from './UniversalDocumentGenerationModal'
import DocumentCompletionModal from './DocumentCompletionModal'
import { GeneratedDocument } from '@/types/document-generation'

interface DocumentCreationModalProps {
  isOpen: boolean
  onClose: () => void
  template: DocumentTemplate | null
  onCreateDocument: (data: DocumentCreationData) => void
}

export type { DocumentCreationData }

export default function DocumentCreationModal({
  isOpen,
  onClose,
  template,
  onCreateDocument
}: DocumentCreationModalProps) {
  const [currentStep, setCurrentStep] = useState<'requirements' | 'project' | 'generating' | 'completed'>('requirements')
  const [selectedTier, setSelectedTier] = useState<'quick' | 'professional'>('professional')
  const [generatedDocument, setGeneratedDocument] = useState<GeneratedDocument | null>(null)
  const [documentId, setDocumentId] = useState<string>('')
  const [documentTitle, setDocumentTitle] = useState<string>('')
  

  const handleContinueFromRequirements = (tier: 'quick' | 'professional') => {
    setSelectedTier(tier)
    setCurrentStep('project')
  }

  const handleBackToRequirements = () => {
    setCurrentStep('requirements')
  }

  const handleCreateDocument = async (data: DocumentCreationData) => {
    try {
      // Start document generation
      const newDocumentId = `doc_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      setDocumentId(newDocumentId)
      setDocumentTitle(data.title)
      setCurrentStep('generating')

      // Call the generation API
      const response = await fetch('/api/generate-document', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          templateId: data.templateId,
          title: data.title,
          description: data.description,
          tier: data.tier,
          projectData: {
            id: 'project-123', // Mock project ID
            name: data.projectData.projectName,
            description: data.projectData.projectDescription,
            industry: data.projectData.industry,
            target_market: data.projectData.targetMarket,
            budget: data.projectData.budget,
            timeline: data.projectData.timeline
          }
        })
      })

      if (!response.ok) {
        throw new Error('Failed to generate document')
      }

      const result = await response.json()
      setGeneratedDocument(result.document)

    } catch (error) {
      console.error('Error creating document:', error)
      // Handle error - could show error modal or go back to previous step
    }
  }

  const handleGenerationComplete = () => {
    setCurrentStep('completed')
  }

  const handleClose = () => {
    setCurrentStep('requirements')
    setSelectedTier('professional')
    setGeneratedDocument(null)
    setDocumentId('')
    onClose()
  }

  return (
    <>
      <TemplateRequirementsModal
        isOpen={isOpen && currentStep === 'requirements'}
        onClose={handleClose}
        template={template}
        onContinue={handleContinueFromRequirements}
      />

      <ProjectSelectionModal
        isOpen={isOpen && currentStep === 'project'}
        onClose={handleClose}
        onBack={handleBackToRequirements}
        template={template}
        selectedTier={selectedTier}
        onCreateDocument={handleCreateDocument}
      />

      <UniversalDocumentGenerationModal
        isOpen={isOpen && currentStep === 'generating'}
        onClose={handleClose}
        onComplete={handleGenerationComplete}
        documentId={documentId}
        documentTitle={documentTitle}
        templateTitle={template?.title || ''}
        projectName="مشروع تجريبي"
        tier={selectedTier}
      />

      <DocumentCompletionModal
        isOpen={isOpen && currentStep === 'completed'}
        onClose={handleClose}
        document={generatedDocument}
      />
    </>
  )
}
