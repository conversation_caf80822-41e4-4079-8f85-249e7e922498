import type { Metadata } from 'next'
import { vazirmatnFont } from '@/lib/fonts'
import { Toaster } from 'sonner'
import { AuthProvider } from '@/contexts/AuthContext'
import './globals.css'

export const metadata: Metadata = {
  title: 'BuildDocwithai - أنشئ مستندات عملك بالذكاء الاصطناعي',
  description: 'منصة متطورة تساعدك في إنشاء خطط العمل ودراسات الجدوى والعروض التقديمية في دقائق معدودة',
  keywords: ['ذكاء اصطناعي', 'مستندات', 'خطط عمل', 'دراسات جدوى', 'عروض تقديمية'],
  authors: [{ name: 'BuildDocwithai Team' }],
  creator: 'BuildDocwithai',
  publisher: 'BuildDocwithai',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://builddocwithai.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'BuildDocwithai - أنشئ مستندات عملك بالذكاء الاصطناعي',
    description: 'منصة متطورة تساعدك في إنشاء خطط العمل ودراسات الجدوى والعروض التقديمية في دقائق معدودة',
    url: 'https://builddocwithai.com',
    siteName: 'BuildDocwithai',
    locale: 'ar_SA',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'BuildDocwithai - أنشئ مستندات عملك بالذكاء الاصطناعي',
    description: 'منصة متطورة تساعدك في إنشاء خطط العمل ودراسات الجدوى والعروض التقديمية في دقائق معدودة',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl" className={vazirmatnFont.variable}>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Vazirmatn:wght@100..900&display=swap" rel="stylesheet" />
      </head>
      <body className={`${vazirmatnFont.className} antialiased bg-slate-900 text-white`}>
        <AuthProvider>
          {children}
          <Toaster
            position="bottom-right"
            richColors
            theme="dark"
            toastOptions={{
              style: {
                fontFamily: 'var(--font-vazirmatn)',
                direction: 'rtl',
                textAlign: 'right'
              }
            }}
          />
        </AuthProvider>
      </body>
    </html>
  )
}
