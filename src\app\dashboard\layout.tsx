import type { Metadata } from 'next'
import Sidebar from '@/components/dashboard/Sidebar'

export const metadata: Metadata = {
  title: 'لوحة التحكم - BuildDocwithai',
  description: 'لوحة تحكم BuildDocwithai لإدارة مستنداتك ومشاريعك',
  robots: {
    index: false, // Dashboard should not be indexed
    follow: false,
  },
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-900 to-slate-800" dir="rtl">
      {/* Sidebar - Fixed positioned on the right for RTL */}
      <Sidebar />

      {/* Main Content - Properly spaced from sidebar */}
      <div className="lg:pr-80 min-h-screen">
        <main className="p-4 sm:p-6 lg:p-8 max-w-full">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
