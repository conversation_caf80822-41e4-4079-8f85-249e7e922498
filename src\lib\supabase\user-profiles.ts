import { supabase } from '@/lib/supabase'
import { UserProfile, CreateUserProfileData, UpdateUserProfileData } from '@/types/database'

// Get user profile for the authenticated user
export async function getUserProfile(): Promise<{ data: UserProfile | null; error: any }> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return { data: null, error: { message: 'User not authenticated' } }
    }

    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', user.id)
      .single()

    if (profileError) {
      // If profile doesn't exist, return null (not an error)
      if (profileError.code === 'PGRST116') {
        return { data: null, error: null }
      }
      console.error('Error fetching user profile:', profileError)
      return { data: null, error: profileError }
    }

    return { data: profile, error: null }
  } catch (error) {
    console.error('Error in getUserProfile:', error)
    return { data: null, error }
  }
}

// Create a new user profile
export async function createUserProfile(profileData: CreateUserProfileData): Promise<{ data: UserProfile | null; error: any }> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return { data: null, error: { message: 'User not authenticated' } }
    }

    // Ensure user_id matches authenticated user
    const dataToInsert = {
      ...profileData,
      user_id: user.id
    }

    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .insert(dataToInsert)
      .select()
      .single()

    if (profileError) {
      console.error('Error creating user profile:', profileError)
      return { data: null, error: profileError }
    }

    return { data: profile, error: null }
  } catch (error) {
    console.error('Error in createUserProfile:', error)
    return { data: null, error }
  }
}

// Update user profile
export async function updateUserProfile(profileData: UpdateUserProfileData): Promise<{ data: UserProfile | null; error: any }> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return { data: null, error: { message: 'User not authenticated' } }
    }

    const { id, ...updateData } = profileData

    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', user.id) // Ensure user can only update their own profile
      .select()
      .single()

    if (profileError) {
      console.error('Error updating user profile:', profileError)
      return { data: null, error: profileError }
    }

    return { data: profile, error: null }
  } catch (error) {
    console.error('Error in updateUserProfile:', error)
    return { data: null, error }
  }
}

// Delete user profile (and optionally the auth user)
export async function deleteUserProfile(deleteAuthUser: boolean = false): Promise<{ error: any }> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return { error: { message: 'User not authenticated' } }
    }

    // Delete the user profile
    const { error: profileError } = await supabase
      .from('user_profiles')
      .delete()
      .eq('user_id', user.id)

    if (profileError) {
      console.error('Error deleting user profile:', profileError)
      return { error: profileError }
    }

    // Optionally delete the auth user account
    if (deleteAuthUser) {
      const { error: authError } = await supabase.auth.admin.deleteUser(user.id)
      
      if (authError) {
        console.error('Error deleting auth user:', authError)
        return { error: authError }
      }
    }

    return { error: null }
  } catch (error) {
    console.error('Error in deleteUserProfile:', error)
    return { error }
  }
}

// Get or create user profile (helper function)
export async function getOrCreateUserProfile(): Promise<{ data: UserProfile | null; error: any }> {
  try {
    // First try to get existing profile
    const { data: existingProfile, error: getError } = await getUserProfile()

    if (getError) {
      return { data: null, error: getError }
    }

    // If profile exists, return it
    if (existingProfile) {
      return { data: existingProfile, error: null }
    }

    // If no profile exists, create a basic one
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return { data: null, error: { message: 'User not authenticated' } }
    }

    const basicProfileData: CreateUserProfileData = {
      user_id: user.id,
      username: user.user_metadata?.name || user.email?.split('@')[0] || 'مستخدم',
      plan_type: 'free',
      locale: 'ar',
      preferences: {
        theme: 'dark',
        language: 'ar',
        rtl_layout: true,
        notifications: true
      }
    }

    return await createUserProfile(basicProfileData)
  } catch (error) {
    console.error('Error in getOrCreateUserProfile:', error)
    return { data: null, error }
  }
}
