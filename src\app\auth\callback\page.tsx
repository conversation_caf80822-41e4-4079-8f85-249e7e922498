'use client'

import { useEffect } from 'react'
import { supabase } from '@/lib/supabase'

export default function AuthCallbackPage() {
  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        const { data, error } = await supabase.auth.getSession()
        
        if (error) {
          // Send error to parent window
          window.opener?.postMessage({
            type: 'SUPABASE_AUTH_ERROR',
            error: error.message
          }, window.location.origin)
        } else if (data.session) {
          // Send success to parent window
          window.opener?.postMessage({
            type: 'SUPABASE_AUTH_SUCCESS',
            session: data.session
          }, window.location.origin)
        }
        
        // Close popup
        window.close()
      } catch (error) {
        window.opener?.postMessage({
          type: 'SUPABASE_AUTH_ERROR',
          error: 'Authentication failed'
        }, window.location.origin)
        window.close()
      }
    }

    handleAuthCallback()
  }, [])

  return (
    <div className="min-h-screen bg-slate-900 flex items-center justify-center">
      <div className="text-center">
        <div className="w-8 h-8 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-white font-vazirmatn">جاري تسجيل الدخول...</p>
      </div>
    </div>
  )
}
