import { NextRequest, NextResponse } from 'next/server'
import { GoogleGenerativeAI } from '@google/generative-ai'

// Initialize Gemini AI with environment variable
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!)

// Simple PDF parsing function with workaround for Next.js
const parsePDF = async (buffer: Buffer) => {
  try {
    // Create the missing test directory and file to prevent the error
    const fs = require('fs')
    const path = require('path')

    const testDir = path.join(process.cwd(), 'test', 'data')
    const testFile = path.join(testDir, '05-versions-space.pdf')

    // Create directories if they don't exist
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true })
    }

    // Create empty test file if it doesn't exist
    if (!fs.existsSync(testFile)) {
      fs.writeFileSync(testFile, Buffer.alloc(0))
    }

    // Now use pdf-parse
    const pdfParse = require('pdf-parse')
    const data = await pdfParse(buffer)
    return data.text
  } catch (error) {
    console.error('PDF parsing error details:', error)
    throw error
  }
}

export async function GET() {
  return NextResponse.json({ message: 'Analyze PDF API is accessible' })
}

export async function POST(request: NextRequest) {
  try {
    console.log('POST request received at /api/analyze-pdf')
    
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json(
        { error: 'No file found' },
        { status: 400 }
      )
    }

    console.log('File received:', file.name, file.type, file.size)

    // Validate file type
    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'File must be a PDF' },
        { status: 400 }
      )
    }

    // Convert file to buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Extract text from PDF
    let extractedText: string
    try {
      extractedText = await parsePDF(buffer)
      console.log('Extracted text length:', extractedText.length)
      console.log('First 200 characters:', extractedText.substring(0, 200))
    } catch (error) {
      console.error('PDF parsing error:', error)
      return NextResponse.json(
        { error: 'Failed to parse PDF. Make sure the file is not password protected.' },
        { status: 400 }
      )
    }

    if (!extractedText || extractedText.trim().length === 0) {
      return NextResponse.json(
        { error: 'PDF is empty or contains no readable text' },
        { status: 400 }
      )
    }

    // For testing: return extracted text first to verify PDF parsing works
    if (extractedText.length > 0) {
      console.log('PDF parsing successful! Text extracted.')

      // Return a simple success response for now to test PDF parsing
      const testResult = {
        success: true,
        completedItems: ['project-name', 'project-description'],
        missingItems: ['project-location', 'founders'],
        extractedData: {
          projectName: 'Test Project',
          projectDescription: 'Test Description',
          projectLocation: null,
          founders: null
        },
        message: 'تم استخراج النص من الملف بنجاح - نسخة تجريبية'
      }

      return NextResponse.json(testResult)
    }

    // AI analysis will be added back after PDF parsing is confirmed working

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Server error occurred' },
      { status: 500 }
    )
  }
}
