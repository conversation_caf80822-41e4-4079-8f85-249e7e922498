'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { ArrowLeft, Play } from 'lucide-react'
import { motion } from 'framer-motion'

export default function HeroSection() {

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-slate-900 to-slate-800">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 rtl:right-1/4 rtl:left-auto w-72 h-72 md:w-96 md:h-96 bg-indigo-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 rtl:left-1/4 rtl:right-auto w-72 h-72 md:w-96 md:h-96 bg-violet-500/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-indigo-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center min-h-screen py-20">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center lg:text-start rtl:lg:text-end order-2 lg:order-1"
          >
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-white mb-6 leading-tight font-vazirmatn"
            >
              أنشئ مستندات عملك الاحترافية
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-violet-400">
                بالذكاء الاصطناعي
              </span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-lg sm:text-xl lg:text-2xl text-slate-300 mb-8 leading-relaxed max-w-2xl mx-auto lg:mx-0 rtl:lg:mr-0 font-vazirmatn"
            >
              منصة متطورة تساعدك في إنشاء خطط العمل ودراسات الجدوى والعروض التقديمية في دقائق معدودة
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start rtl:lg:justify-end mb-8"
            >
              <Link href="/signup" className="w-full sm:w-auto">
                <Button variant="gradient" size="lg" className="group w-full sm:w-auto font-vazirmatn text-lg px-8 py-4 shadow-2xl shadow-indigo-500/25 hover:shadow-indigo-500/40 transition-all duration-300">
                  ابدأ مجاناً الآن
                  <ArrowLeft className="w-5 h-5 mr-2 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>

              <Button variant="secondary" size="lg" className="group w-full sm:w-auto font-vazirmatn text-lg px-8 py-4 hover:bg-slate-700/50 transition-all duration-300">
                <Play className="w-5 h-5 mr-2 rtl:ml-2 rtl:mr-0 group-hover:scale-110 transition-transform" />
                شاهد العرض التوضيحي
              </Button>
            </motion.div>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 mt-8 pt-8 border-t border-slate-700/50"
            >
              <div className="text-center lg:text-start rtl:lg:text-end">
                <div className="text-3xl sm:text-2xl lg:text-3xl font-bold text-white mb-2 font-vazirmatn">10K+</div>
                <div className="text-sm sm:text-xs lg:text-sm text-slate-400 font-vazirmatn">مستندات تم إنشاؤها</div>
              </div>
              <div className="text-center lg:text-start rtl:lg:text-end">
                <div className="text-3xl sm:text-2xl lg:text-3xl font-bold text-white mb-2 font-vazirmatn">500+</div>
                <div className="text-sm sm:text-xs lg:text-sm text-slate-400 font-vazirmatn">عميل راضٍ</div>
              </div>
              <div className="text-center lg:text-start rtl:lg:text-end">
                <div className="text-3xl sm:text-2xl lg:text-3xl font-bold text-white mb-2 font-vazirmatn">99%</div>
                <div className="text-sm sm:text-xs lg:text-sm text-slate-400 font-vazirmatn">معدل الرضا</div>
              </div>
            </motion.div>
          </motion.div>

          {/* Illustration */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 0.4 }}
            className="relative order-1 lg:order-2 mb-8 lg:mb-0"
          >
            <div className="relative z-10">
              {/* Main illustration placeholder */}
              <div className="bg-gradient-to-br from-indigo-500/20 to-violet-500/20 rounded-3xl p-6 sm:p-8 backdrop-blur-sm border border-slate-700/50 shadow-2xl">
                <div className="grid grid-cols-2 gap-3 sm:gap-4">
                  {/* Document cards */}
                  {[
                    { title: 'خطة عمل', color: 'from-indigo-400 to-blue-400' },
                    { title: 'دراسة جدوى', color: 'from-violet-400 to-purple-400' },
                    { title: 'عرض تقديمي', color: 'from-emerald-400 to-teal-400' },
                    { title: 'تقرير', color: 'from-orange-400 to-red-400' }
                  ].map((doc, i) => (
                    <motion.div
                      key={i}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.8 + i * 0.1 }}
                      className="bg-white/10 backdrop-blur-sm rounded-xl p-3 sm:p-4 border border-white/20 hover:bg-white/15 transition-all duration-300"
                    >
                      <div className={`h-3 bg-gradient-to-r ${doc.color} rounded mb-2`}></div>
                      <div className="space-y-2">
                        <div className="h-2 bg-white/30 rounded w-3/4"></div>
                        <div className="h-2 bg-white/20 rounded w-1/2"></div>
                        <div className="h-2 bg-white/20 rounded w-2/3"></div>
                      </div>
                      <div className="text-xs text-white/70 mt-2 font-vazirmatn">{doc.title}</div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>

            {/* Floating elements */}
            <motion.div
              animate={{ y: [-10, 10, -10] }}
              transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
              className="absolute -top-4 -right-4 rtl:-left-4 rtl:right-auto w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-r from-indigo-500 to-violet-500 rounded-full flex items-center justify-center shadow-xl"
            >
              <span className="text-white font-bold text-sm sm:text-base font-vazirmatn">AI</span>
            </motion.div>

            <motion.div
              animate={{ y: [10, -10, 10] }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
              className="absolute -bottom-4 -left-4 rtl:-right-4 rtl:left-auto w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-violet-500 to-indigo-500 rounded-full flex items-center justify-center shadow-xl"
            >
              <span className="text-white text-xs sm:text-sm">📄</span>
            </motion.div>

            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              className="absolute top-1/2 -right-8 rtl:-left-8 rtl:right-auto w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full opacity-60"
            ></motion.div>
          </motion.div>
        </div>
      </div>

      {/* Scroll indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1.5 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          className="w-6 h-10 border-2 border-slate-600 rounded-full flex justify-center"
        >
          <div className="w-1 h-3 bg-slate-600 rounded-full mt-2"></div>
        </motion.div>
      </motion.div>
    </section>
  )
}
