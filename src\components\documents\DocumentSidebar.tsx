'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { ChevronRight, ChevronLeft, FileText, Menu, X } from 'lucide-react'
import { DocumentSection } from '@/types/document-generation'
import { Button } from '@/components/ui/Button'

interface DocumentSidebarProps {
  sections: DocumentSection[]
  activeSection: string
  onSectionClick: (sectionId: string) => void
  className?: string
}

export default function DocumentSidebar({
  sections,
  activeSection,
  onSectionClick,
  className = ''
}: DocumentSidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileOpen, setIsMobileOpen] = useState(false)

  // Close mobile sidebar when section is clicked
  const handleSectionClick = (sectionId: string) => {
    onSectionClick(sectionId)
    setIsMobileOpen(false)
  }

  // Close mobile sidebar on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setIsMobileOpen(false)
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [])

  return (
    <>
      {/* Mobile Menu Button */}
      <div className="lg:hidden fixed top-20 right-4 z-50">
        <Button
          onClick={() => setIsMobileOpen(true)}
          size="sm"
          className="bg-slate-800 hover:bg-slate-700 text-white shadow-lg"
        >
          <Menu className="w-4 h-4 ml-2" />
          فهرس المحتويات
        </Button>
      </div>

      {/* Mobile Overlay */}
      {isMobileOpen && (
        <div 
          className="lg:hidden fixed inset-0 bg-black/50 z-40"
          onClick={() => setIsMobileOpen(false)}
        />
      )}

      {/* Sidebar */}
      <motion.div
        initial={false}
        animate={{
          width: isCollapsed ? 60 : 320,
          x: isMobileOpen ? 0 : (window.innerWidth < 1024 ? -320 : 0)
        }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
        className={`
          fixed right-0 top-0 h-full bg-slate-800 border-l border-slate-700 z-40
          lg:relative lg:translate-x-0
          ${className}
        `}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-700">
          {!isCollapsed && (
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                <FileText className="w-4 h-4 text-white" />
              </div>
              <h3 className="font-bold text-white font-vazirmatn">فهرس المحتويات</h3>
            </div>
          )}
          
          <div className="flex items-center gap-2">
            {/* Mobile Close Button */}
            <Button
              onClick={() => setIsMobileOpen(false)}
              variant="ghost"
              size="sm"
              className="lg:hidden text-slate-400 hover:text-white"
            >
              <X className="w-4 h-4" />
            </Button>
            
            {/* Desktop Collapse Button */}
            <Button
              onClick={() => setIsCollapsed(!isCollapsed)}
              variant="ghost"
              size="sm"
              className="hidden lg:flex text-slate-400 hover:text-white"
            >
              {isCollapsed ? (
                <ChevronLeft className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Sections List */}
        <div className="flex-1 overflow-y-auto p-2">
          {!isCollapsed && (
            <div className="space-y-1">
              {sections.map((section, index) => {
                const isActive = activeSection === section.id
                
                return (
                  <motion.button
                    key={section.id}
                    onClick={() => handleSectionClick(section.id)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className={`
                      w-full text-right p-3 rounded-xl transition-all duration-200
                      ${isActive 
                        ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white shadow-lg' 
                        : 'text-slate-300 hover:bg-slate-700 hover:text-white'
                      }
                    `}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="font-vazirmatn font-medium text-sm leading-relaxed">
                          {section.title}
                        </div>
                        <div className="text-xs opacity-75 mt-1">
                          القسم {index + 1}
                        </div>
                      </div>
                      
                      {isActive && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="w-2 h-2 bg-white rounded-full ml-2"
                        />
                      )}
                    </div>
                  </motion.button>
                )
              })}
            </div>
          )}
          
          {isCollapsed && (
            <div className="space-y-2">
              {sections.map((section, index) => {
                const isActive = activeSection === section.id
                
                return (
                  <motion.button
                    key={section.id}
                    onClick={() => handleSectionClick(section.id)}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className={`
                      w-full h-10 rounded-lg flex items-center justify-center transition-all duration-200
                      ${isActive 
                        ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white' 
                        : 'text-slate-400 hover:bg-slate-700 hover:text-white'
                      }
                    `}
                    title={section.title}
                  >
                    <span className="text-sm font-bold">{index + 1}</span>
                  </motion.button>
                )
              })}
            </div>
          )}
        </div>

        {/* Footer */}
        {!isCollapsed && (
          <div className="p-4 border-t border-slate-700">
            <div className="text-xs text-slate-400 font-vazirmatn text-center">
              {sections.length} قسم • {sections.filter(s => s.status === 'completed').length} مكتمل
            </div>
          </div>
        )}
      </motion.div>
    </>
  )
}
