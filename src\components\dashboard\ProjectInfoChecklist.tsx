'use client'

import { motion } from 'framer-motion'
import { CheckCircle2, Circle, Info } from 'lucide-react'
import { ChecklistItem } from '@/lib/project-creation-types'

interface ProjectInfoChecklistProps {
  items: ChecklistItem[]
  completedCount: number
  analysisResult?: {
    success: boolean
    completedItems: string[]
    missingItems: string[]
    message: string
  }
}

export default function ProjectInfoChecklist({ items, completedCount, analysisResult }: ProjectInfoChecklistProps) {
  // Map analysis result to checklist items
  const updatedItems = items.map(item => {
    if (analysisResult) {
      const isCompleted = analysisResult.completedItems.includes(item.id)
      return { ...item, completed: isCompleted }
    }
    return item
  })

  const actualCompletedCount = analysisResult
    ? analysisResult.completedItems.length
    : completedCount

  const totalItems = items.length
  const progressPercentage = (actualCompletedCount / totalItems) * 100

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 mb-8"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center">
            <Info className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white font-vazirmatn">
              قائمة مراجعة معلومات المشروع
            </h3>
            <p className="text-sm text-slate-400 font-vazirmatn">
              {completedCount}/{totalItems} مكتمل
            </p>
          </div>
        </div>
        
        {/* Progress Circle */}
        <div className="relative w-16 h-16">
          <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 64 64">
            <circle
              cx="32"
              cy="32"
              r="28"
              stroke="currentColor"
              strokeWidth="4"
              fill="none"
              className="text-slate-700"
            />
            <motion.circle
              cx="32"
              cy="32"
              r="28"
              stroke="currentColor"
              strokeWidth="4"
              fill="none"
              strokeLinecap="round"
              className="text-indigo-500"
              initial={{ strokeDasharray: "0 175.93" }}
              animate={{ strokeDasharray: `${(progressPercentage / 100) * 175.93} 175.93` }}
              transition={{ duration: 1, ease: "easeInOut" }}
            />
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-sm font-bold text-white font-vazirmatn">
              {Math.round(progressPercentage)}%
            </span>
          </div>
        </div>
      </div>

      {/* Description */}
      <p className="text-slate-300 text-sm font-vazirmatn mb-6 leading-relaxed">
        {analysisResult
          ? 'نتائج تحليل الملف بالذكاء الاصطناعي:'
          : 'تأكد من أن ملفك المرفوع يحتوي على جميع المعلومات المطلوبة أدناه'
        }
      </p>

      {/* Analysis Result Message */}
      {analysisResult && (
        <div className={`mb-6 p-4 rounded-xl border ${
          analysisResult.success
            ? 'bg-green-500/10 border-green-500/20 text-green-300'
            : 'bg-amber-500/10 border-amber-500/20 text-amber-300'
        }`}>
          <p className="text-sm font-vazirmatn">
            {analysisResult.message}
          </p>
        </div>
      )}

      {/* Checklist Items */}
      <div className="space-y-3">
        {updatedItems.map((item, index) => (
          <motion.div
            key={item.id}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className={`flex items-center space-x-3 rtl:space-x-reverse p-3 rounded-xl transition-all duration-200 ${
              item.completed 
                ? 'bg-green-500/10 border border-green-500/20' 
                : 'bg-slate-700/30 border border-slate-600/30 hover:bg-slate-700/50'
            }`}
          >
            <div className="flex-shrink-0">
              {item.completed ? (
                <CheckCircle2 className="w-5 h-5 text-green-400" />
              ) : (
                <Circle className="w-5 h-5 text-slate-400" />
              )}
            </div>
            
            <div className="flex-1">
              <span className={`text-sm font-vazirmatn ${
                item.completed ? 'text-green-300' : 'text-slate-300'
              }`}>
                {item.label}
              </span>
              {!item.required && (
                <span className="text-xs text-slate-500 font-vazirmatn mr-2 rtl:ml-2 rtl:mr-0">
                  (اختياري)
                </span>
              )}
            </div>
            
            {item.required && (
              <div className="flex-shrink-0">
                <span className="text-xs bg-red-500/20 text-red-400 px-2 py-1 rounded-full font-vazirmatn">
                  مطلوب
                </span>
              </div>
            )}
          </motion.div>
        ))}
      </div>

      {/* Footer Note */}
      <div className="mt-6 p-4 bg-indigo-500/10 border border-indigo-500/20 rounded-xl">
        <p className="text-xs text-indigo-300 font-vazirmatn text-center">
          💡 ستُستخدم هذه البيانات لإنشاء المستندات المستقبلية. سيتم تنفيذ عملية التحقق لاحقاً بواسطة الذكاء الاصطناعي
        </p>
      </div>
    </motion.div>
  )
}
