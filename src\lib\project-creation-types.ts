export interface ProjectCreationData {
  name: string
  description: string
  projectFile?: string
  targetCustomers?: string
  customerProblem?: string
  solution?: string
  location: {
    useCurrentLocation: boolean
    address?: string
    coordinates?: {
      lat: number
      lng: number
    }
  }
  founders: Founder[]
}

export interface Founder {
  id: string
  name: string
  share?: number
}

export interface ChecklistItem {
  id: string
  label: string
  required: boolean
  completed: boolean
}

export const PROJECT_CHECKLIST_ITEMS: ChecklistItem[] = [
  {
    id: 'project-name',
    label: 'إسم المشروع',
    required: true,
    completed: false
  },
  {
    id: 'project-description',
    label: 'وصف المشروع',
    required: true,
    completed: false
  },
  {
    id: 'target-customers',
    label: 'شريحة العملاء المستهدفة (اختياري)',
    required: false,
    completed: false
  },
  {
    id: 'customer-problem',
    label: 'مشكلة العملاء (اختياري)',
    required: false,
    completed: false
  },
  {
    id: 'solution',
    label: 'حل مشروعك (اختياري)',
    required: false,
    completed: false
  },
  {
    id: 'project-location',
    label: 'موقع مشروعك',
    required: true,
    completed: false
  },
  {
    id: 'founders',
    label: 'مؤسسو المشروع وحصصهم',
    required: true,
    completed: false
  }
]

export interface UploadedFile {
  name: string
  size: number
  type: string
  url?: string
  uploadProgress?: number
}

export interface VoiceRecording {
  id: string
  duration: number
  url?: string
  isRecording: boolean
  isPaused: boolean
}
