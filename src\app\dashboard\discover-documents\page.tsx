'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { 
  Search, 
  Filter, 
  Star, 
  Clock, 
  ChevronDown, 
  ChevronUp,
  Rocket,
  Users,
  Building,
  Briefcase,
  BarChart3,
  UserCheck,
  FileText,
  Mail,
  GraduationCap,
  Award,
  Handshake,
  Shield,
  TrendingUp,
  FileCheck,
  ArrowLeft
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { documentCategories, DocumentTemplate } from '@/lib/document-types'
import DocumentCreationModal, { DocumentCreationData } from '@/components/documents/DocumentCreationModal'
import { generateDocument } from '@/lib/document-generation'
import { toast } from 'sonner'

export default function DiscoverDocumentsPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [expandedSections, setExpandedSections] = useState<string[]>(['project-launch'])
  const [sortBy, setSortBy] = useState<'featured' | 'name' | 'difficulty'>('featured')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<DocumentTemplate | null>(null)

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev =>
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    )
  }

  const handleCreateDocument = (template: DocumentTemplate) => {
    setSelectedTemplate(template)
    setIsModalOpen(true)
  }

  const handleModalClose = () => {
    setIsModalOpen(false)
    setSelectedTemplate(null)
  }

  const handleDocumentCreation = async (data: DocumentCreationData) => {
    try {
      console.log('Creating document with data:', data)

      // The new DocumentCreationModal handles the entire workflow internally
      // including generation, progress tracking, and completion
      // This function is now mainly for cleanup and notifications

      toast.success(`بدء إنشاء ${data.title}...`)

      // Close the modal - the generation modal will take over
      setIsModalOpen(false)
      setSelectedTemplate(null)

    } catch (error) {
      console.error('Document creation error:', error)
      toast.error('فشل في بدء إنشاء المستند. يرجى المحاولة مرة أخرى')
      throw error
    }
  }

  const getIcon = (iconName: string) => {
    const icons: { [key: string]: any } = {
      rocket: Rocket,
      users: Users,
      building: Building,
      briefcase: Briefcase,
      'chart-bar': BarChart3,
      'user-check': UserCheck,
      'file-text': FileText,
      mail: Mail,
      'graduation-cap': GraduationCap,
      award: Award,
      handshake: Handshake,
      shield: Shield,
      'trending-up': TrendingUp,
      'file-check': FileCheck
    }
    return icons[iconName] || FileText
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-400 bg-green-500/10 border-green-500/20'
      case 'medium': return 'text-amber-400 bg-amber-500/10 border-amber-500/20'
      case 'hard': return 'text-red-400 bg-red-500/10 border-red-500/20'
      default: return 'text-slate-400 bg-slate-500/10 border-slate-500/20'
    }
  }

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'سهل'
      case 'medium': return 'متوسط'
      case 'hard': return 'صعب'
      default: return difficulty
    }
  }

  const filteredCategories = documentCategories.map(category => ({
    ...category,
    templates: category.templates.filter(template => {
      const matchesSearch = template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           template.description.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesCategory = selectedCategory === 'all' || category.id === selectedCategory
      return matchesSearch && matchesCategory
    })
  })).filter(category => category.templates.length > 0)

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center gap-4 mb-6">
          <Link href="/dashboard/documents">
            <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
              <ArrowLeft className="w-4 h-4 ml-2" />
              العودة للمستندات
            </Button>
          </Link>
        </div>
        
        <h1 className="text-3xl font-bold text-white font-vazirmatn mb-2">
          اكتشف قوالب المستندات
        </h1>
        <p className="text-slate-400 font-vazirmatn">
          اختر من مجموعة واسعة من القوالب الاحترافية لإنشاء مستنداتك بالذكاء الاصطناعي
        </p>
      </motion.div>

      {/* Filters and Search */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6"
      >
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
            <Input
              placeholder="البحث في القوالب..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pr-10 bg-slate-700/50 border-slate-600 text-white placeholder-slate-400 font-vazirmatn"
            />
          </div>

          {/* Category Filter */}
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-slate-400" />
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white font-vazirmatn text-sm"
            >
              <option value="all">جميع المجموعات</option>
              {documentCategories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.titleArabic}
                </option>
              ))}
            </select>
          </div>

          {/* Sort */}
          <div className="flex items-center gap-2">
            <span className="text-slate-400 text-sm font-vazirmatn">ترتيب:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white font-vazirmatn text-sm"
            >
              <option value="featured">المميزة أولاً</option>
              <option value="name">الاسم</option>
              <option value="difficulty">مستوى الصعوبة</option>
            </select>
          </div>
        </div>
      </motion.div>

      {/* Document Categories */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="space-y-8"
      >
        {filteredCategories.map((category, categoryIndex) => {
          const CategoryIcon = getIcon(category.icon)
          const isExpanded = expandedSections.includes(category.id)
          
          return (
            <div
              key={category.id}
              className="bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl overflow-hidden"
            >
              {/* Category Header */}
              <button
                onClick={() => toggleSection(category.id)}
                className="w-full p-6 flex items-center justify-between hover:bg-slate-700/20 transition-all duration-300"
              >
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center">
                    <CategoryIcon className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-right">
                    <h2 className="text-xl font-bold text-white font-vazirmatn">
                      {category.titleArabic}
                    </h2>
                    <p className="text-slate-400 font-vazirmatn text-sm">
                      {category.description}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <span className="text-sm text-slate-400 font-vazirmatn">
                    {category.templates.length} قالب
                  </span>
                  {isExpanded ? (
                    <ChevronUp className="w-5 h-5 text-slate-400" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-slate-400" />
                  )}
                </div>
              </button>

              {/* Category Templates */}
              {isExpanded && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="px-6 pb-6"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {category.templates
                      .sort((a, b) => {
                        if (sortBy === 'featured') return b.featured ? 1 : -1
                        if (sortBy === 'name') return a.title.localeCompare(b.title)
                        if (sortBy === 'difficulty') {
                          const difficultyOrder = { easy: 1, medium: 2, hard: 3 }
                          return difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty]
                        }
                        return 0
                      })
                      .map((template, templateIndex) => {
                        const TemplateIcon = getIcon(template.icon)
                        
                        return (
                          <motion.div
                            key={template.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3, delay: templateIndex * 0.1 }}
                            className="bg-slate-700/30 border border-slate-600/50 rounded-xl p-4 hover:border-indigo-500/30 transition-all duration-300 group"
                          >
                            {/* Template Header */}
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                                  <TemplateIcon className="w-5 h-5 text-white" />
                                </div>
                                <div>
                                  <h3 className="font-semibold text-white font-vazirmatn group-hover:text-indigo-300 transition-colors duration-300">
                                    {template.title}
                                  </h3>
                                  {template.featured && (
                                    <div className="flex items-center gap-1 mt-1">
                                      <Star className="w-3 h-3 text-amber-400 fill-current" />
                                      <span className="text-xs text-amber-400 font-vazirmatn">مميز</span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>

                            {/* Template Description */}
                            <p className="text-slate-400 text-sm font-vazirmatn mb-4 line-clamp-2">
                              {template.description}
                            </p>

                            {/* Template Meta */}
                            <div className="flex items-center gap-4 mb-4 text-xs">
                              <div className="flex items-center gap-1 text-slate-500">
                                <Clock className="w-3 h-3" />
                                <span className="font-vazirmatn">{template.estimatedTime}</span>
                              </div>
                              <span className={`px-2 py-1 rounded-full border font-vazirmatn ${getDifficultyColor(template.difficulty)}`}>
                                {getDifficultyText(template.difficulty)}
                              </span>
                            </div>

                            {/* Template Actions */}
                            <div className="flex items-center gap-2">
                              <Button
                                size="sm"
                                onClick={() => handleCreateDocument(template)}
                                className="flex-1 bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 font-vazirmatn"
                              >
                                إنشاء مستند
                              </Button>
                              <Button size="sm" variant="ghost" className="text-slate-400 hover:text-white hover:bg-slate-600/50 font-vazirmatn">
                                عرض التفاصيل
                              </Button>
                            </div>
                          </motion.div>
                        )
                      })}
                  </div>
                </motion.div>
              )}
            </div>
          )
        })}
      </motion.div>

      {/* No Results */}
      {filteredCategories.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center py-12"
        >
          <Search className="w-16 h-16 text-slate-500 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-slate-300 font-vazirmatn mb-2">
            لا توجد نتائج
          </h3>
          <p className="text-slate-500 font-vazirmatn">
            جرب تغيير مصطلحات البحث أو الفلاتر
          </p>
        </motion.div>
      )}

      {/* Document Creation Modal */}
      <DocumentCreationModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        template={selectedTemplate}
        onCreateDocument={handleDocumentCreation}
      />
    </div>
  )
}
