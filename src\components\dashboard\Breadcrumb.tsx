'use client'

import { motion } from 'framer-motion'
import { ChevronLeft, ArrowRight } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/Button'

interface BreadcrumbItem {
  label: string
  href?: string
}

interface BreadcrumbProps {
  items: BreadcrumbItem[]
  showBackButton?: boolean
}

export default function Breadcrumb({ items, showBackButton = true }: BreadcrumbProps) {
  const router = useRouter()

  const handleBack = () => {
    router.back()
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="flex items-center justify-between mb-6"
    >
      {/* Breadcrumb Navigation */}
      <nav className="flex items-center space-x-2 rtl:space-x-reverse">
        <ol className="flex items-center space-x-2 rtl:space-x-reverse">
          {items.map((item, index) => (
            <li key={index} className="flex items-center">
              {index > 0 && (
                <ChevronLeft className="w-4 h-4 text-slate-500 mx-2 rtl:rotate-180" />
              )}
              
              {item.href && index < items.length - 1 ? (
                <Link
                  href={item.href}
                  className="text-slate-400 hover:text-indigo-400 transition-colors duration-200 font-vazirmatn text-sm hover:underline"
                >
                  {item.label}
                </Link>
              ) : (
                <span
                  className={`font-vazirmatn text-sm ${
                    index === items.length - 1
                      ? 'text-white font-medium'
                      : 'text-slate-400'
                  }`}
                >
                  {item.label}
                </span>
              )}
            </li>
          ))}
        </ol>
      </nav>

      {/* Back Button */}
      {showBackButton && (
        <Button
          variant="outline"
          size="sm"
          onClick={handleBack}
          className="text-slate-400 border-slate-600 hover:text-white hover:border-indigo-500 hover:bg-indigo-500/10 transition-all duration-200"
        >
          <ArrowRight className="w-4 h-4 ml-2" />
          رجوع
        </Button>
      )}
    </motion.div>
  )
}
