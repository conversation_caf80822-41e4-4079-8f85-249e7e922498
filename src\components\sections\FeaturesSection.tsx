'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Bot, FileText, Layout } from 'lucide-react'
import { motion } from 'framer-motion'

export default function FeaturesSection() {
  const features = [
    {
      icon: Bot,
      title: 'إنشاء ذكي بالذكاء الاصطناعي',
      description: 'احصل على مستندات احترافية مخصصة لعملك في ثوانٍ معدودة',
      gradient: 'from-indigo-500 to-blue-500',
    },
    {
      icon: FileText,
      title: 'تصدير PDF عالي الجودة',
      description: 'صدّر مستنداتك بتنسيق PDF احترافي جاهز للطباعة والمشاركة',
      gradient: 'from-violet-500 to-purple-500',
    },
    {
      icon: Layout,
      title: 'قوالب متنوعة ومتخصصة',
      description: 'اختر من مجموعة واسعة من القوالب المصممة خصيصاً لمختلف الصناعات',
      gradient: 'from-emerald-500 to-teal-500',
    },
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  }

  return (
    <section id="features" className="section-padding bg-slate-900">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6">
            مميزات تجعل عملك أسهل
          </h2>
          <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
            اكتشف كيف يمكن للذكاء الاصطناعي أن يحول طريقة إنشاء مستنداتك
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {features.map((feature, index) => {
            const Icon = feature.icon
            return (
              <motion.div key={index} variants={itemVariants}>
                <Card className="h-full group hover:scale-105 transition-all duration-300 hover:shadow-2xl hover:shadow-indigo-500/10">
                  <CardHeader className="text-center">
                    <div className="mx-auto mb-4 relative">
                      <div className={`w-16 h-16 bg-gradient-to-r ${feature.gradient} rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300`}>
                        <Icon className="w-8 h-8 text-white" />
                      </div>
                      {/* Glow effect */}
                      <div className={`absolute inset-0 w-16 h-16 bg-gradient-to-r ${feature.gradient} rounded-2xl blur-xl opacity-20 group-hover:opacity-40 transition-opacity duration-300`}></div>
                    </div>
                    <CardTitle className="text-xl mb-2 group-hover:text-indigo-400 transition-colors duration-300">
                      {feature.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-center leading-relaxed">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </motion.div>

        {/* Additional feature highlights */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8"
        >
          {[
            { number: '50+', label: 'قوالب متخصصة' },
            { number: '10', label: 'لغات مدعومة' },
            { number: '24/7', label: 'دعم فني' },
            { number: '99.9%', label: 'وقت تشغيل' },
          ].map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="text-3xl font-bold text-white mb-2">{stat.number}</div>
              <div className="text-slate-400 text-sm">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
