'use client'

import { motion } from 'framer-motion'
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  Folder, 
  BarChart<PERSON>, 
  File<PERSON>heck, 
  Presentation, 
  TrendingUp,
  ArrowLeft
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { ActivitySummary } from '@/lib/dashboard-data'

interface ActivityCardProps {
  activity: ActivitySummary
  index: number
}

const iconMap = {
  FileText,
  Folder,
  BarChart3,
  FileCheck,
  Presentation,
  TrendingUp
}

export default function ActivityCard({ activity, index }: ActivityCardProps) {
  const Icon = iconMap[activity.icon as keyof typeof iconMap] || FileText

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="relative bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 hover:border-slate-600/50 transition-all duration-300 group hover:shadow-2xl hover:shadow-slate-900/50 overflow-hidden"
    >
      {/* Background Gradient */}
      <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-white/5 to-transparent rounded-full blur-xl"></div>

      {/* Header */}
      <div className="relative z-10 flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <div className={`w-14 h-14 ${activity.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-all duration-300 shadow-lg`}>
            <Icon className="w-7 h-7 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white font-vazirmatn mb-1">
              {activity.title}
            </h3>
            <p className="text-3xl font-bold text-white">
              {activity.count}
            </p>
          </div>
        </div>
      </div>

      {/* Description */}
      <p className="relative z-10 text-slate-300 text-sm font-vazirmatn mb-6 leading-relaxed">
        {activity.description}
      </p>

      {/* Action Button */}
      <Button
        variant="secondary"
        size="sm"
        className="relative z-10 w-full font-vazirmatn text-sm group-hover:bg-slate-600/50 transition-all duration-300 border border-slate-600/50 hover:border-slate-500/50 backdrop-blur-sm"
      >
        <span>{activity.buttonText}</span>
        <ArrowLeft className="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0 group-hover:translate-x-1 rtl:group-hover:-translate-x-1 transition-transform duration-300" />
      </Button>
    </motion.div>
  )
}
