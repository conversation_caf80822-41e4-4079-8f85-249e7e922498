/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/projects/page";
exports.ids = ["app/dashboard/projects/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fprojects%2Fpage&page=%2Fdashboard%2Fprojects%2Fpage&appPaths=%2Fdashboard%2Fprojects%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fprojects%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmoham%5COneDrive%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5Cbuilddocwithai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmoham%5COneDrive%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5Cbuilddocwithai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fprojects%2Fpage&page=%2Fdashboard%2Fprojects%2Fpage&appPaths=%2Fdashboard%2Fprojects%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fprojects%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmoham%5COneDrive%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5Cbuilddocwithai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmoham%5COneDrive%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5Cbuilddocwithai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'projects',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/projects/page.tsx */ \"(rsc)/./src/app/dashboard/projects/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/projects/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/projects/page\",\n        pathname: \"/dashboard/projects\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fprojects%2Fpage&page=%2Fdashboard%2Fprojects%2Fpage&appPaths=%2Fdashboard%2Fprojects%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fprojects%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmoham%5COneDrive%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5Cbuilddocwithai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmoham%5COneDrive%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5Cbuilddocwithai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vaGFtJTVDJTVDT25lRHJpdmUlNUMlNUMlRDglQjMlRDglQjclRDglQUQlMjAlRDglQTclRDklODQlRDklODUlRDklODMlRDglQUElRDglQTglNUMlNUNidWlsZGRvY3dpdGhhaSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdNQUFzSSIsInNvdXJjZXMiOlsid2VicGFjazovL2J1aWxkZG9jd2l0aGFpLz9jZmJlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbW9oYW1cXFxcT25lRHJpdmVcXFxc2LPYt9itINin2YTZhdmD2KrYqFxcXFxidWlsZGRvY3dpdGhhaVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vaGFtJTVDJTVDT25lRHJpdmUlNUMlNUMlRDglQjMlRDglQjclRDglQUQlMjAlRDglQTclRDklODQlRDklODUlRDklODMlRDglQUElRDglQTglNUMlNUNidWlsZGRvY3dpdGhhaSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q3Nvbm5lciU1QyU1Q2Rpc3QlNUMlNUNpbmRleC5tanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUb2FzdGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vaGFtJTVDJTVDT25lRHJpdmUlNUMlNUMlRDglQjMlRDglQjclRDglQUQlMjAlRDglQTclRDklODQlRDklODUlRDklODMlRDglQUElRDglQTglNUMlNUNidWlsZGRvY3dpdGhhaSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vaGFtJTVDJTVDT25lRHJpdmUlNUMlNUMlRDglQjMlRDglQjclRDglQUQlMjAlRDglQTclRDklODQlRDklODUlRDklODMlRDglQUElRDglQTglNUMlNUNidWlsZGRvY3dpdGhhaSU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvTEFBK0o7QUFDL0o7QUFDQSx3S0FBNkoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idWlsZGRvY3dpdGhhaS8/MjMyNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRvYXN0ZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxtb2hhbVxcXFxPbmVEcml2ZVxcXFzYs9i32K0g2KfZhNmF2YPYqtioXFxcXGJ1aWxkZG9jd2l0aGFpXFxcXG5vZGVfbW9kdWxlc1xcXFxzb25uZXJcXFxcZGlzdFxcXFxpbmRleC5tanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXG1vaGFtXFxcXE9uZURyaXZlXFxcXNiz2LfYrSDYp9mE2YXZg9iq2KhcXFxcYnVpbGRkb2N3aXRoYWlcXFxcc3JjXFxcXGNvbnRleHRzXFxcXEF1dGhDb250ZXh0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5CProjectsView.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5CProjectsView.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/dashboard/ProjectsView.tsx */ \"(ssr)/./src/components/dashboard/ProjectsView.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vaGFtJTVDJTVDT25lRHJpdmUlNUMlNUMlRDglQjMlRDglQjclRDglQUQlMjAlRDglQTclRDklODQlRDklODUlRDklODMlRDglQUElRDglQTglNUMlNUNidWlsZGRvY3dpdGhhaSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNkYXNoYm9hcmQlNUMlNUNQcm9qZWN0c1ZpZXcudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa01BQXNLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVpbGRkb2N3aXRoYWkvPzI3YjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcbW9oYW1cXFxcT25lRHJpdmVcXFxc2LPYt9itINin2YTZhdmD2KrYqFxcXFxidWlsZGRvY3dpdGhhaVxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxkYXNoYm9hcmRcXFxcUHJvamVjdHNWaWV3LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5CProjectsView.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/dashboard/Sidebar.tsx */ \"(ssr)/./src/components/dashboard/Sidebar.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vaGFtJTVDJTVDT25lRHJpdmUlNUMlNUMlRDglQjMlRDglQjclRDglQUQlMjAlRDglQTclRDklODQlRDklODUlRDklODMlRDglQUElRDglQTglNUMlNUNidWlsZGRvY3dpdGhhaSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNkYXNoYm9hcmQlNUMlNUNTaWRlYmFyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdMQUFpSyIsInNvdXJjZXMiOlsid2VicGFjazovL2J1aWxkZG9jd2l0aGFpLz8zOTNkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXG1vaGFtXFxcXE9uZURyaXZlXFxcXNiz2LfYrSDYp9mE2YXZg9iq2KhcXFxcYnVpbGRkb2N3aXRoYWlcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcZGFzaGJvYXJkXFxcXFNpZGViYXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmoham%5C%5COneDrive%5C%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5C%5Cbuilddocwithai%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/CreateProjectModal.tsx":
/*!*********************************************************!*\
  !*** ./src/components/dashboard/CreateProjectModal.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectTypeSelectionModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit3_FileText_Mic_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit3,FileText,Mic,Plus,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit3_FileText_Mic_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit3,FileText,Mic,Plus,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit3_FileText_Mic_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit3,FileText,Mic,Plus,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit3_FileText_Mic_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit3,FileText,Mic,Plus,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit3_FileText_Mic_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit3,FileText,Mic,Plus,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit3_FileText_Mic_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit3,FileText,Mic,Plus,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit3_FileText_Mic_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit3,FileText,Mic,Plus,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst PROJECT_CREATION_OPTIONS = [\n    {\n        id: \"audio-upload\",\n        title: \"رفع ملف صوتي\",\n        description: \"قم برفع ملف صوتي لتحويله إلى مشروع تلقائياً\",\n        icon: _barrel_optimize_names_ArrowLeft_Edit3_FileText_Mic_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        route: \"/dashboard/projects/create/audio-upload\",\n        color: \"from-blue-500 to-indigo-500\",\n        bgColor: \"from-blue-500/10 to-indigo-500/10\",\n        borderColor: \"border-blue-500/20\"\n    },\n    {\n        id: \"document-upload\",\n        title: \"رفع مستند\",\n        description: \"ارفع مستند موجود لتحويله إلى مشروع منظم\",\n        icon: _barrel_optimize_names_ArrowLeft_Edit3_FileText_Mic_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        route: \"/dashboard/projects/create/document-upload\",\n        color: \"from-green-500 to-emerald-500\",\n        bgColor: \"from-green-500/10 to-emerald-500/10\",\n        borderColor: \"border-green-500/20\"\n    },\n    {\n        id: \"voice-recording\",\n        title: \"تسجيل صوتي للبيانات\",\n        description: \"سجل بياناتك صوتياً وسيتم تحويلها إلى مشروع\",\n        icon: _barrel_optimize_names_ArrowLeft_Edit3_FileText_Mic_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        route: \"/dashboard/projects/create/voice-recording\",\n        color: \"from-purple-500 to-pink-500\",\n        bgColor: \"from-purple-500/10 to-pink-500/10\",\n        borderColor: \"border-purple-500/20\"\n    },\n    {\n        id: \"manual-entry\",\n        title: \"تعبئة يدوية\",\n        description: \"أدخل بيانات المشروع يدوياً خطوة بخطوة\",\n        icon: _barrel_optimize_names_ArrowLeft_Edit3_FileText_Mic_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        route: \"/dashboard/projects/create/manual-entry\",\n        color: \"from-orange-500 to-red-500\",\n        bgColor: \"from-orange-500/10 to-red-500/10\",\n        borderColor: \"border-orange-500/20\"\n    }\n];\nfunction ProjectTypeSelectionModal({ isOpen, onClose }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const handleOptionSelect = (route)=>{\n        onClose();\n        router.push(route);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"absolute inset-0 bg-black/60 backdrop-blur-sm\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    transition: {\n                        type: \"spring\",\n                        duration: 0.5\n                    },\n                    className: \"relative w-full max-w-2xl bg-gradient-to-br from-slate-800 via-slate-800 to-slate-900 border border-slate-700/50 rounded-2xl shadow-2xl overflow-hidden\",\n                    dir: \"rtl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-slate-700/50 bg-gradient-to-l from-indigo-500/5 to-purple-500/5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit3_FileText_Mic_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-bold text-white font-vazirmatn\",\n                                                    children: \"إنشاء مشروع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-slate-400 font-vazirmatn\",\n                                                    children: \"اختر طريقة إنشاء مشروعك\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"p-2 text-slate-400 hover:text-white hover:bg-slate-700/50 rounded-lg transition-colors duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit3_FileText_Mic_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: PROJECT_CREATION_OPTIONS.map((option, index)=>{\n                                        const Icon = option.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: index * 0.1\n                                            },\n                                            onClick: ()=>handleOptionSelect(option.route),\n                                            className: `relative p-6 bg-gradient-to-br ${option.bgColor} border ${option.borderColor} rounded-xl hover:border-opacity-50 transition-all duration-300 group text-right overflow-hidden`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-white/5 to-transparent rounded-full blur-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `w-12 h-12 bg-gradient-to-r ${option.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg`,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                        className: \"w-6 h-6 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                                                        lineNumber: 138,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                                                    lineNumber: 137,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit3_FileText_Mic_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-slate-400 group-hover:text-white group-hover:translate-x-1 transition-all duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                                                    lineNumber: 140,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white font-vazirmatn mb-2\",\n                                                            children: option.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-400 font-vazirmatn leading-relaxed\",\n                                                            children: option.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, option.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center my-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 h-px bg-slate-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-4 text-sm text-slate-500 font-vazirmatn\",\n                                            children: \"أو\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 h-px bg-slate-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-slate-400 font-vazirmatn mb-4\",\n                                            children: \"تفضل الطريقة التقليدية؟\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleOptionSelect(\"/dashboard/projects/create/manual-entry\"),\n                                            className: \"text-indigo-400 hover:text-indigo-300 font-vazirmatn text-sm transition-colors duration-200 hover:underline\",\n                                            children: \"تعبئة يدوية\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n            lineNumber: 75,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\CreateProjectModal.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/CreateProjectModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/ProjectCard.tsx":
/*!**************************************************!*\
  !*** ./src/components/dashboard/ProjectCard.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart3,BookOpen,Briefcase,Calculator,Clock,Copy,Edit,ExternalLink,FileCheck,FileText,MoreVertical,Share2,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart3,BookOpen,Briefcase,Calculator,Clock,Copy,Edit,ExternalLink,FileCheck,FileText,MoreVertical,Share2,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart3,BookOpen,Briefcase,Calculator,Clock,Copy,Edit,ExternalLink,FileCheck,FileText,MoreVertical,Share2,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart3,BookOpen,Briefcase,Calculator,Clock,Copy,Edit,ExternalLink,FileCheck,FileText,MoreVertical,Share2,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart3,BookOpen,Briefcase,Calculator,Clock,Copy,Edit,ExternalLink,FileCheck,FileText,MoreVertical,Share2,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart3,BookOpen,Briefcase,Calculator,Clock,Copy,Edit,ExternalLink,FileCheck,FileText,MoreVertical,Share2,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart3,BookOpen,Briefcase,Calculator,Clock,Copy,Edit,ExternalLink,FileCheck,FileText,MoreVertical,Share2,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart3,BookOpen,Briefcase,Calculator,Clock,Copy,Edit,ExternalLink,FileCheck,FileText,MoreVertical,Share2,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart3,BookOpen,Briefcase,Calculator,Clock,Copy,Edit,ExternalLink,FileCheck,FileText,MoreVertical,Share2,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart3,BookOpen,Briefcase,Calculator,Clock,Copy,Edit,ExternalLink,FileCheck,FileText,MoreVertical,Share2,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart3,BookOpen,Briefcase,Calculator,Clock,Copy,Edit,ExternalLink,FileCheck,FileText,MoreVertical,Share2,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart3,BookOpen,Briefcase,Calculator,Clock,Copy,Edit,ExternalLink,FileCheck,FileText,MoreVertical,Share2,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart3,BookOpen,Briefcase,Calculator,Clock,Copy,Edit,ExternalLink,FileCheck,FileText,MoreVertical,Share2,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart3,BookOpen,Briefcase,Calculator,Clock,Copy,Edit,ExternalLink,FileCheck,FileText,MoreVertical,Share2,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart3,BookOpen,Briefcase,Calculator,Clock,Copy,Edit,ExternalLink,FileCheck,FileText,MoreVertical,Share2,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart3,BookOpen,Briefcase,Calculator,Clock,Copy,Edit,ExternalLink,FileCheck,FileText,MoreVertical,Share2,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Status labels for display\nconst PROJECT_STATUS_LABELS = {\n    active: \"نشط\",\n    paused: \"متوقف\",\n    draft: \"مسودة\",\n    completed: \"مكتمل\"\n};\n// Status colors for styling\nconst PROJECT_STATUS_COLORS = {\n    active: \"text-green-400 bg-green-500/20\",\n    paused: \"text-yellow-400 bg-yellow-500/20\",\n    draft: \"text-blue-400 bg-blue-500/20\",\n    completed: \"text-purple-400 bg-purple-500/20\"\n};\nconst iconMap = {\n    Briefcase: _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    BarChart3: _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    TrendingUp: _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    Calculator: _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    BookOpen: _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    FileText: _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n};\nfunction ProjectCard({ project, index, onEdit, onDelete, onView, onDuplicate, onArchive, onShare }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [showActionsMenu, setShowActionsMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const actionsMenuRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Default icon for all projects\n    const Icon = _barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n    // Get industry-based color scheme\n    const getProjectColor = (industry)=>{\n        const colors = [\n            \"bg-gradient-to-br from-indigo-500 to-purple-500\",\n            \"bg-gradient-to-br from-blue-500 to-cyan-500\",\n            \"bg-gradient-to-br from-green-500 to-emerald-500\",\n            \"bg-gradient-to-br from-orange-500 to-red-500\",\n            \"bg-gradient-to-br from-purple-500 to-pink-500\"\n        ];\n        const index = industry ? industry.length % colors.length : 0;\n        return colors[index];\n    };\n    // Calculate progress based on status\n    const getProjectProgress = (status)=>{\n        switch(status){\n            case \"completed\":\n                return 100;\n            case \"active\":\n                return 65;\n            case \"paused\":\n                return 30;\n            case \"draft\":\n                return 10;\n            default:\n                return 0;\n        }\n    };\n    const progress = getProjectProgress(project.status);\n    // Close actions menu when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (actionsMenuRef.current && !actionsMenuRef.current.contains(event.target)) {\n                setShowActionsMenu(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.5,\n            delay: index * 0.1\n        },\n        className: \"relative bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 hover:border-slate-600/50 transition-all duration-300 group hover:shadow-2xl hover:shadow-slate-900/50 hover:scale-[1.02] overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-white/5 to-transparent rounded-full blur-xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex items-start justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4 rtl:space-x-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `relative w-14 h-14 ${getProjectColor(project.industry)} rounded-xl flex items-center justify-center group-hover:scale-110 transition-all duration-300 shadow-lg`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-7 h-7 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-slate-800 ${project.status === \"active\" ? \"bg-green-500\" : project.status === \"completed\" ? \"bg-blue-500\" : project.status === \"paused\" ? \"bg-yellow-500\" : \"bg-slate-500\"}`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 rtl:space-x-reverse mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white font-vazirmatn truncate\",\n                                                children: project.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1 rtl:space-x-reverse bg-indigo-500/20 text-indigo-400 px-2 py-1 rounded-full text-xs font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: project.documents_count\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border ${PROJECT_STATUS_COLORS[project.status]} font-vazirmatn`,\n                                                children: PROJECT_STATUS_LABELS[project.status]\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-slate-400 font-vazirmatn\",\n                                                children: project.industry || \"مشروع عام\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        ref: actionsMenuRef,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowActionsMenu(!showActionsMenu),\n                                className: \"opacity-0 group-hover:opacity-100 p-2 text-slate-400 hover:text-white hover:bg-slate-700/50 rounded-lg transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this),\n                            showActionsMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.95,\n                                    y: -10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    scale: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    scale: 0.95,\n                                    y: -10\n                                },\n                                className: \"absolute left-0 rtl:right-0 rtl:left-auto top-full mt-2 w-48 bg-slate-800 border border-slate-700/50 rounded-xl shadow-2xl z-20 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"py-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                onDuplicate?.(project);\n                                                setShowActionsMenu(false);\n                                            },\n                                            className: \"w-full px-4 py-2 text-right rtl:text-left hover:bg-slate-700/50 transition-colors duration-200 flex items-center space-x-3 rtl:space-x-reverse text-slate-300 hover:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-vazirmatn\",\n                                                    children: \"نسخ المشروع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                onShare?.(project);\n                                                setShowActionsMenu(false);\n                                            },\n                                            className: \"w-full px-4 py-2 text-right rtl:text-left hover:bg-slate-700/50 transition-colors duration-200 flex items-center space-x-3 rtl:space-x-reverse text-slate-300 hover:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-vazirmatn\",\n                                                    children: \"مشاركة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                onArchive?.(project);\n                                                setShowActionsMenu(false);\n                                            },\n                                            className: \"w-full px-4 py-2 text-right rtl:text-left hover:bg-slate-700/50 transition-colors duration-200 flex items-center space-x-3 rtl:space-x-reverse text-slate-300 hover:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-vazirmatn\",\n                                                    children: \"أرشفة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t border-slate-700/50 my-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                onDelete?.(project);\n                                                setShowActionsMenu(false);\n                                            },\n                                            className: \"w-full px-4 py-2 text-right rtl:text-left hover:bg-red-500/10 transition-colors duration-200 flex items-center space-x-3 rtl:space-x-reverse text-slate-300 hover:text-red-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-vazirmatn\",\n                                                    children: \"حذف\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"relative z-10 text-slate-300 text-sm font-vazirmatn mb-4 leading-relaxed line-clamp-2\",\n                children: project.description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center text-xs font-vazirmatn mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-3 h-3 text-slate-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-slate-400\",\n                                        children: \"التقدم\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `font-semibold ${progress >= 80 ? \"text-green-400\" : progress >= 50 ? \"text-yellow-400\" : \"text-slate-400\"}`,\n                                        children: [\n                                            progress,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-2 h-2 rounded-full ${progress >= 80 ? \"bg-green-400\" : progress >= 50 ? \"bg-yellow-400\" : \"bg-slate-400\"}`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full bg-slate-700/50 rounded-full h-3 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                initial: {\n                                    width: 0\n                                },\n                                animate: {\n                                    width: `${progress}%`\n                                },\n                                transition: {\n                                    duration: 1,\n                                    delay: index * 0.1 + 0.5\n                                },\n                                className: `h-3 rounded-full ${progress >= 80 ? \"bg-gradient-to-r from-green-500 to-emerald-500\" : progress >= 50 ? \"bg-gradient-to-r from-yellow-500 to-orange-500\" : \"bg-gradient-to-r from-indigo-500 to-purple-500\"}`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                initial: {\n                                    width: 0\n                                },\n                                animate: {\n                                    width: `${progress}%`\n                                },\n                                transition: {\n                                    duration: 1,\n                                    delay: index * 0.1 + 0.5\n                                },\n                                className: `absolute top-0 h-3 rounded-full opacity-50 blur-sm ${progress >= 80 ? \"bg-gradient-to-r from-green-400 to-emerald-400\" : progress >= 50 ? \"bg-gradient-to-r from-yellow-400 to-orange-400\" : \"bg-gradient-to-r from-indigo-400 to-purple-400\"}`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 rtl:space-x-reverse text-xs font-vazirmatn\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 bg-slate-700/50 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-3 h-3 text-indigo-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white font-semibold\",\n                                                children: project.documents_count\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-slate-500\",\n                                                children: \"مستندات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 rtl:space-x-reverse text-xs font-vazirmatn\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 bg-slate-700/50 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-3 h-3 text-green-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white font-semibold\",\n                                                children: \"نشط\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-slate-500\",\n                                                children: new Date(project.updated_at).toLocaleDateString(\"ar-SA\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 pt-3 border-t border-slate-700/30\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 rtl:space-x-reverse text-xs text-slate-400 font-vazirmatn\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"آخر نشاط: \",\n                                        new Date(project.updated_at).toLocaleDateString(\"ar-SA\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex items-center space-x-2 rtl:space-x-reverse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"primary\",\n                        size: \"sm\",\n                        className: \"flex-1 font-vazirmatn text-sm\",\n                        onClick: ()=>{\n                            // Navigate to project details page\n                            router.push(`/dashboard/projects/${encodeURIComponent(project.id)}`);\n                            // Also call the original onView if provided\n                            onView?.(project);\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"w-4 h-4 ml-1 rtl:mr-1 rtl:ml-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, this),\n                            \"فتح المشروع\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"secondary\",\n                        size: \"sm\",\n                        className: \"font-vazirmatn text-sm border border-slate-600/50 hover:border-slate-500/50 backdrop-blur-sm\",\n                        onClick: ()=>{\n                            // Navigate to edit page\n                            router.push(`/dashboard/projects/create/manual-entry?edit=${encodeURIComponent(project.id)}`);\n                            // Also call the original onEdit if provided\n                            onEdit?.(project);\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart3_BookOpen_Briefcase_Calculator_Clock_Copy_Edit_ExternalLink_FileCheck_FileText_MoreVertical_Share2_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"w-4 h-4 ml-1 rtl:mr-1 rtl:ml-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this),\n                            \"تعديل\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectCard.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/ProjectCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/ProjectsView.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/ProjectsView.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectsView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Folder_Grid3X3_List_Loader2_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Folder,Grid3X3,List,Loader2,Plus,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Folder_Grid3X3_List_Loader2_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Folder,Grid3X3,List,Loader2,Plus,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_Folder_Grid3X3_List_Loader2_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Folder,Grid3X3,List,Loader2,Plus,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Folder_Grid3X3_List_Loader2_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Folder,Grid3X3,List,Loader2,Plus,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Folder_Grid3X3_List_Loader2_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Folder,Grid3X3,List,Loader2,Plus,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Folder_Grid3X3_List_Loader2_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Folder,Grid3X3,List,Loader2,Plus,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _ProjectCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ProjectCard */ \"(ssr)/./src/components/dashboard/ProjectCard.tsx\");\n/* harmony import */ var _CreateProjectModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CreateProjectModal */ \"(ssr)/./src/components/dashboard/CreateProjectModal.tsx\");\n/* harmony import */ var _lib_supabase_projects__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase/projects */ \"(ssr)/./src/lib/supabase/projects.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nconst PROJECT_STATUS_LABELS = {\n    active: \"نشط\",\n    paused: \"متوقف\",\n    draft: \"مسودة\",\n    completed: \"مكتمل\"\n};\nfunction ProjectsView() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const [projects, setProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isCreateModalOpen, setIsCreateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    // Fetch projects on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProjects();\n    }, []);\n    const fetchProjects = async ()=>{\n        try {\n            setLoading(true);\n            const { data, error } = await (0,_lib_supabase_projects__WEBPACK_IMPORTED_MODULE_5__.getProjects)();\n            if (error) {\n                console.error(\"Error fetching projects:\", error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"حدث خطأ في تحميل المشاريع\");\n                return;\n            }\n            setProjects(data || []);\n        } catch (error) {\n            console.error(\"Error in fetchProjects:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"حدث خطأ في تحميل المشاريع\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleEditProject = (project)=>{\n        // Navigate to edit page with project ID\n        router.push(`/dashboard/projects/create/manual-entry?edit=${project.id}`);\n    };\n    const handleDeleteProject = async (project)=>{\n        if (!confirm(`هل أنت متأكد من حذف المشروع \"${project.name}\"؟ هذا الإجراء لا يمكن التراجع عنه.`)) {\n            return;\n        }\n        try {\n            const { error } = await (0,_lib_supabase_projects__WEBPACK_IMPORTED_MODULE_5__.deleteProject)(project.id);\n            if (error) {\n                console.error(\"Error deleting project:\", error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"حدث خطأ في حذف المشروع\");\n                return;\n            }\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success(`تم حذف المشروع \"${project.name}\" بنجاح`);\n            // Refresh projects list\n            fetchProjects();\n        } catch (error) {\n            console.error(\"Error in handleDeleteProject:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"حدث خطأ في حذف المشروع\");\n        }\n    };\n    const handleViewProject = (project)=>{\n        router.push(`/dashboard/projects/${project.id}`);\n    };\n    const handleDuplicateProject = (project)=>{\n        sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success(`سيتم نسخ المشروع \"${project.name}\" قريباً`);\n    };\n    const handleArchiveProject = (project)=>{\n        sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success(`تم أرشفة المشروع: ${project.name}`);\n    };\n    const handleShareProject = (project)=>{\n        sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success(`تم نسخ رابط المشروع: ${project.name}`);\n    };\n    // Filter projects based on search and status\n    const filteredProjects = projects.filter((project)=>{\n        const matchesSearch = project.name.toLowerCase().includes(searchQuery.toLowerCase()) || project.description.toLowerCase().includes(searchQuery.toLowerCase());\n        const matchesStatus = statusFilter === \"all\" || project.status === statusFilter;\n        return matchesSearch && matchesStatus;\n    });\n    // Show loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Folder_Grid3X3_List_Loader2_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-indigo-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-slate-400 font-vazirmatn\",\n                        children: \"جاري تحميل المشاريع...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: -20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5\n                },\n                className: \"relative bg-gradient-to-br from-indigo-500/10 via-purple-500/10 to-pink-500/10 border border-indigo-500/20 rounded-2xl p-8 overflow-hidden backdrop-blur-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5 opacity-50\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 rtl:space-x-reverse mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Folder_Grid3X3_List_Loader2_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-4xl font-bold text-white font-vazirmatn bg-gradient-to-l from-white to-indigo-200 bg-clip-text text-transparent\",\n                                                children: \"مشاريعي\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-300 font-vazirmatn text-lg leading-relaxed\",\n                                        children: \"إدارة وتنظيم جميع مشاريعك في مكان واحد\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center space-x-4 rtl:space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center p-4 bg-white/5 rounded-xl backdrop-blur-sm border border-white/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-1\",\n                                                children: projects.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-slate-400 font-vazirmatn\",\n                                                children: \"إجمالي المشاريع\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center p-4 bg-white/5 rounded-xl backdrop-blur-sm border border-white/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-1\",\n                                                children: projects.filter((p)=>p.status === \"active\").length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-slate-400 font-vazirmatn\",\n                                                children: \"مشاريع نشطة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5,\n                    delay: 0.1\n                },\n                className: \"flex justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"gradient\",\n                    size: \"xl\",\n                    onClick: ()=>setIsCreateModalOpen(true),\n                    className: \"font-vazirmatn text-lg px-10 py-5 shadow-2xl shadow-indigo-500/30 hover:shadow-indigo-500/50 transition-all duration-300 hover:scale-105 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 hover:from-indigo-600 hover:via-purple-600 hover:to-pink-600 border border-indigo-400/20 hover:border-indigo-400/40\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Folder_Grid3X3_List_Loader2_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"w-6 h-6 ml-2 rtl:mr-2 rtl:ml-0\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        \"إنشاء مشروع جديد\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5,\n                    delay: 0.2\n                },\n                className: \"flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 sm:space-x-4 rtl:sm:space-x-reverse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1 max-w-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Folder_Grid3X3_List_Loader2_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"absolute right-3 rtl:left-3 rtl:right-auto top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"البحث في المشاريع...\",\n                                value: searchQuery,\n                                onChange: (e)=>setSearchQuery(e.target.value),\n                                className: \"w-full pl-10 rtl:pr-10 rtl:pl-4 pr-4 py-3 bg-slate-800/50 border border-slate-700/50 rounded-xl text-white placeholder-slate-400 font-vazirmatn focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: statusFilter,\n                                onChange: (e)=>setStatusFilter(e.target.value),\n                                className: \"px-4 py-3 bg-slate-800/50 border border-slate-700/50 rounded-xl text-white font-vazirmatn focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"all\",\n                                        children: \"جميع الحالات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 13\n                                    }, this),\n                                    Object.entries(PROJECT_STATUS_LABELS).map(([status, label])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: status,\n                                            children: label\n                                        }, status, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center bg-slate-800/50 border border-slate-700/50 rounded-xl p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setViewMode(\"grid\"),\n                                        className: `p-2 rounded-lg transition-colors duration-200 ${viewMode === \"grid\" ? \"bg-indigo-500 text-white\" : \"text-slate-400 hover:text-white\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Folder_Grid3X3_List_Loader2_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setViewMode(\"list\"),\n                                        className: `p-2 rounded-lg transition-colors duration-200 ${viewMode === \"list\" ? \"bg-indigo-500 text-white\" : \"text-slate-400 hover:text-white\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Folder_Grid3X3_List_Loader2_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this),\n            filteredProjects.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.5,\n                    delay: 0.3\n                },\n                className: `grid gap-6 ${viewMode === \"grid\" ? \"grid-cols-1 md:grid-cols-2 lg:grid-cols-3\" : \"grid-cols-1\"}`,\n                children: filteredProjects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProjectCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        project: project,\n                        index: index,\n                        onEdit: handleEditProject,\n                        onDelete: handleDeleteProject,\n                        onView: handleViewProject,\n                        onDuplicate: handleDuplicateProject,\n                        onArchive: handleArchiveProject,\n                        onShare: handleShareProject\n                    }, project.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                lineNumber: 252,\n                columnNumber: 9\n            }, this) : /* Empty State */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5,\n                    delay: 0.3\n                },\n                className: \"text-center py-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-24 h-24 bg-gradient-to-r from-slate-700 to-slate-600 rounded-2xl flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Folder_Grid3X3_List_Loader2_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-12 h-12 text-slate-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-white font-vazirmatn mb-2\",\n                        children: searchQuery || statusFilter !== \"all\" ? \"لا توجد مشاريع مطابقة\" : \"لا توجد مشاريع حتى الآن\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-slate-400 font-vazirmatn mb-6\",\n                        children: searchQuery || statusFilter !== \"all\" ? \"جرب تغيير معايير البحث أو الفلترة\" : \"ابدأ بإنشاء مشروعك الأول لتنظيم مستنداتك\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, this),\n                    !searchQuery && statusFilter === \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"gradient\",\n                        size: \"lg\",\n                        onClick: ()=>setIsCreateModalOpen(true),\n                        className: \"font-vazirmatn\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Folder_Grid3X3_List_Loader2_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5 ml-2 rtl:mr-2 rtl:ml-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 15\n                            }, this),\n                            \"إنشاء مشروع جديد\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                lineNumber: 278,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateProjectModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: isCreateModalOpen,\n                onClose: ()=>setIsCreateModalOpen(false),\n                onProjectCreated: ()=>{\n                    setIsCreateModalOpen(false);\n                    fetchProjects() // Refresh projects list\n                    ;\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n                lineNumber: 311,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\ProjectsView.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/ProjectsView.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/Sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/Sidebar.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/scroll-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/compass.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,Compass,Crown,Database,FileText,Folder,LogOut,Menu,ScrollText,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction Sidebar({ className = \"\" }) {\n    const { user, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [isMobileOpen, setIsMobileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"تم تسجيل الخروج بنجاح\");\n            router.push(\"/\");\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"حدث خطأ أثناء تسجيل الخروج\");\n        }\n    };\n    const navigationSections = [\n        {\n            title: \"الرئيسية\",\n            items: [\n                {\n                    name: \"نظرة عامة\",\n                    href: \"/dashboard\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    active: pathname === \"/dashboard\",\n                    disabled: false\n                },\n                {\n                    name: \"مشاريعي\",\n                    href: \"/dashboard/projects\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    active: pathname === \"/dashboard/projects\",\n                    disabled: false\n                }\n            ]\n        },\n        {\n            title: \"المستندات والعقود\",\n            items: [\n                {\n                    name: \"مستنداتي\",\n                    href: \"/dashboard/documents\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    active: pathname === \"/dashboard/documents\" || pathname === \"/dashboard/discover-documents\",\n                    disabled: false\n                },\n                {\n                    name: \"عقودي\",\n                    href: \"/dashboard/contracts\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    active: pathname === \"/dashboard/contracts\",\n                    disabled: false\n                }\n            ]\n        },\n        {\n            title: \"الاستكشاف والذكاء الاصطناعي\",\n            items: [\n                {\n                    name: \"اكتشف المستندات\",\n                    href: \"/dashboard/discover-documents\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                    active: pathname === \"/dashboard/discover-documents\",\n                    disabled: false\n                },\n                {\n                    name: \"اكتشف العقود\",\n                    href: \"/dashboard/discover-contracts\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                    active: pathname === \"/dashboard/discover-contracts\",\n                    disabled: false\n                },\n                {\n                    name: \"المساعد الذكي\",\n                    href: \"/dashboard/ai-assistant\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                    active: pathname === \"/dashboard/ai-assistant\",\n                    disabled: false\n                },\n                {\n                    name: \"مساحة البيانات\",\n                    href: \"/dashboard/Data-space\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                    active: pathname === \"/dashboard/Data-space\",\n                    disabled: false\n                }\n            ]\n        },\n        {\n            title: \"الإعدادات\",\n            items: [\n                {\n                    name: \"الإعدادات\",\n                    href: \"/dashboard/settings\",\n                    icon: _barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    active: pathname === \"/dashboard/settings\",\n                    disabled: false\n                }\n            ]\n        }\n    ];\n    const SidebarContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-gradient-to-b from-slate-800 via-slate-800 to-slate-900 border-r border-slate-700/50 shadow-2xl backdrop-blur-sm\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-slate-700/50 bg-gradient-to-l from-indigo-500/5 to-violet-500/5\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: \"/\",\n                        className: \"flex items-center space-x-2 rtl:space-x-reverse group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-gradient-to-r from-indigo-500 to-violet-500 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-indigo-500/25 transition-all duration-300 group-hover:scale-105\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white font-bold text-sm\",\n                                    children: \"BD\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl font-bold text-white font-vazirmatn group-hover:text-indigo-300 transition-colors duration-300\",\n                                children: \"BuildDocwithai\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-slate-700/50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3 rtl:space-x-reverse p-3 bg-slate-700/30 rounded-xl hover:bg-slate-700/50 transition-all duration-300 group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-gradient-to-r from-indigo-500 to-violet-500 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-indigo-500/25 transition-all duration-300 group-hover:scale-105\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"w-6 h-6 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-semibold text-white font-vazirmatn truncate group-hover:text-indigo-300 transition-colors duration-300\",\n                                        children: user?.user_metadata?.name || user?.email?.split(\"@\")[0] || \"المستخدم\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-slate-400 font-vazirmatn truncate\",\n                                        children: user?.email\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-green-400 rounded-full mr-2 rtl:ml-2 rtl:mr-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-400 font-vazirmatn\",\n                                                children: \"متصل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex-1 p-4 space-y-6 overflow-y-auto\",\n                    children: navigationSections.map((section, sectionIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xs font-semibold text-slate-400 uppercase tracking-wider font-vazirmatn px-4 mb-3\",\n                                    children: section.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: section.items.map((item)=>{\n                                        const Icon = item.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: item.disabled ? \"#\" : item.href,\n                                            className: `flex items-center space-x-3 rtl:space-x-reverse px-4 py-3 rounded-xl text-sm font-medium font-vazirmatn transition-all duration-300 group relative overflow-hidden ${item.active ? \"bg-gradient-to-l from-indigo-500 to-violet-500 text-white shadow-lg shadow-indigo-500/25\" : item.disabled ? \"text-slate-500 cursor-not-allowed\" : \"text-slate-300 hover:text-white hover:bg-slate-700/50 hover:shadow-lg\"}`,\n                                            onClick: (e)=>{\n                                                if (item.disabled) {\n                                                    e.preventDefault();\n                                                }\n                                            },\n                                            children: [\n                                                item.active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-l from-indigo-400/20 to-violet-400/20 rounded-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: `w-5 h-5 relative z-10 transition-transform duration-300 ${item.active ? \"text-white\" : \"group-hover:scale-110\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"relative z-10 flex-1\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 21\n                                                }, this),\n                                                item.disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs bg-slate-600/50 text-slate-400 px-2 py-1 rounded-full backdrop-blur-sm relative z-10\",\n                                                    children: \"قريباً\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                sectionIndex < navigationSections.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-px bg-gradient-to-l from-slate-700 via-slate-600 to-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, section.title, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-t border-slate-700/50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-yellow-500/10 via-orange-500/10 to-red-500/10 border border-yellow-500/20 rounded-xl p-4 space-y-4 backdrop-blur-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-semibold text-white font-vazirmatn\",\n                                        children: \"الخطة المجانية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-xs text-slate-300 font-vazirmatn\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"3 من 10 مستندات مستخدمة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-yellow-400\",\n                                                children: \"30%\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-slate-700/50 rounded-full h-2.5 overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-yellow-500 to-orange-500 h-2.5 rounded-full shadow-sm transition-all duration-500\",\n                                            style: {\n                                                width: \"30%\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"gradient\",\n                                size: \"sm\",\n                                className: \"w-full font-vazirmatn text-sm bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 shadow-lg hover:shadow-yellow-500/25 transition-all duration-300\",\n                                children: \"ترقية الخطة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-t border-slate-700/50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleSignOut,\n                        className: \"flex items-center space-x-3 rtl:space-x-reverse w-full px-4 py-3 text-sm font-medium text-slate-300 hover:text-red-300 hover:bg-red-500/10 rounded-xl transition-all duration-300 font-vazirmatn group border border-transparent hover:border-red-500/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"w-5 h-5 group-hover:scale-110 transition-transform duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"تسجيل الخروج\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n            lineNumber: 134,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsMobileOpen(true),\n                className: \"lg:hidden fixed top-4 left-4 z-50 p-2 bg-slate-800 text-white rounded-lg shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `hidden lg:flex lg:flex-col lg:w-80 lg:fixed lg:inset-y-0 lg:right-0 lg:z-30 ${className}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this),\n            isMobileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed inset-0 z-50 flex justify-end\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50\",\n                        onClick: ()=>setIsMobileOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.div, {\n                        initial: {\n                            x: \"100%\"\n                        },\n                        animate: {\n                            x: 0\n                        },\n                        exit: {\n                            x: \"100%\"\n                        },\n                        transition: {\n                            type: \"tween\",\n                            duration: 0.3\n                        },\n                        className: \"relative flex flex-col w-80 max-w-xs bg-slate-800 shadow-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMobileOpen(false),\n                                className: \"absolute top-4 left-4 p-2 text-slate-400 hover:text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_Compass_Crown_Database_FileText_Folder_LogOut_Menu_ScrollText_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 283,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            primary: \"bg-indigo-500 text-white hover:bg-indigo-600 shadow-lg hover:shadow-xl\",\n            secondary: \"bg-transparent border-2 border-white/20 text-white hover:border-white/40 hover:bg-white/5\",\n            outline: \"border border-slate-600 bg-transparent hover:bg-slate-800 hover:text-white\",\n            ghost: \"hover:bg-slate-800 hover:text-white\",\n            link: \"text-indigo-400 underline-offset-4 hover:underline\",\n            gradient: \"bg-gradient-to-r from-indigo-500 to-violet-500 text-white hover:from-indigo-600 hover:to-violet-600 shadow-lg hover:shadow-xl\"\n        },\n        size: {\n            default: \"h-12 px-6 py-3\",\n            sm: \"h-9 px-4 py-2 text-xs\",\n            lg: \"h-14 px-8 py-4 text-base\",\n            xl: \"h-16 px-10 py-5 text-lg\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"primary\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 41,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    loading: true,\n    signOut: async ()=>{}\n});\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get initial user\n        const getUser = async ()=>{\n            try {\n                const { user } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.authHelpers.getCurrentUser();\n                setUser(user);\n            } catch (error) {\n                console.error(\"Error getting user:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        getUser();\n        // Listen for auth changes\n        const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.authHelpers.onAuthStateChange(async (event, session)=>{\n            setUser(session?.user ?? null);\n            setLoading(false);\n        });\n        return ()=>subscription.unsubscribe();\n    }, []);\n    const signOut = async ()=>{\n        try {\n            await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.authHelpers.signOut();\n            setUser(null);\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n        }\n    };\n    const value = {\n        user,\n        loading,\n        signOut\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authHelpers: () => (/* binding */ authHelpers),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n\n// Environment variables validation\nconst supabaseUrl = \"https://zkjasrxgkkhfcafwilsb.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpramFzcnhna2toZmNhZndpbHNiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjE0MjIsImV4cCI6MjA2Nzc5NzQyMn0.XljfQR5z8rOqeTplTQ78llsYYeind8jMw3Nl1HqnrvQ\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables. Please check your .env.local file.\");\n}\n// Supabase client for client-side operations\nconst supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)({\n    supabaseUrl,\n    supabaseKey: supabaseAnonKey\n});\n// Auth helper functions\nconst authHelpers = {\n    signUp: async (email, password)=>{\n        const redirectUrl = `${window.location.origin}/`;\n        const { data, error } = await supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                emailRedirectTo: redirectUrl\n            }\n        });\n        return {\n            data,\n            error\n        };\n    },\n    signIn: async (email, password)=>{\n        const { data, error } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        return {\n            data,\n            error\n        };\n    },\n    signInWithGoogle: async ()=>{\n        const redirectUrl = `${window.location.origin}/`;\n        const { data, error } = await supabase.auth.signInWithOAuth({\n            provider: \"google\",\n            options: {\n                redirectTo: redirectUrl,\n                queryParams: {\n                    access_type: \"offline\",\n                    prompt: \"consent\"\n                },\n                skipBrowserRedirect: false // We'll handle popup manually\n            }\n        });\n        return {\n            data,\n            error\n        };\n    },\n    signInWithGooglePopup: async ()=>{\n        return new Promise((resolve, reject)=>{\n            const redirectUrl = `${window.location.origin}/auth/callback`;\n            // Create popup window\n            const popup = window.open(\"\", \"google-auth\", \"width=500,height=600,scrollbars=yes,resizable=yes\");\n            if (!popup) {\n                reject(new Error(\"Popup blocked\"));\n                return;\n            }\n            // Start OAuth flow\n            supabase.auth.signInWithOAuth({\n                provider: \"google\",\n                options: {\n                    redirectTo: redirectUrl,\n                    queryParams: {\n                        access_type: \"offline\",\n                        prompt: \"consent\"\n                    },\n                    skipBrowserRedirect: true\n                }\n            }).then(({ data, error })=>{\n                if (error) {\n                    popup.close();\n                    reject(error);\n                    return;\n                }\n                if (data.url) {\n                    popup.location.href = data.url;\n                }\n                // Listen for popup messages\n                const messageListener = (event)=>{\n                    if (event.origin !== window.location.origin) return;\n                    if (event.data.type === \"SUPABASE_AUTH_SUCCESS\") {\n                        window.removeEventListener(\"message\", messageListener);\n                        popup.close();\n                        resolve({\n                            data: event.data.session,\n                            error: null\n                        });\n                    } else if (event.data.type === \"SUPABASE_AUTH_ERROR\") {\n                        window.removeEventListener(\"message\", messageListener);\n                        popup.close();\n                        reject(new Error(event.data.error));\n                    }\n                };\n                window.addEventListener(\"message\", messageListener);\n                // Check if popup is closed manually\n                const checkClosed = setInterval(()=>{\n                    if (popup.closed) {\n                        clearInterval(checkClosed);\n                        window.removeEventListener(\"message\", messageListener);\n                        reject(new Error(\"Authentication cancelled\"));\n                    }\n                }, 1000);\n            });\n        });\n    },\n    signOut: async ()=>{\n        const { error } = await supabase.auth.signOut();\n        return {\n            error\n        };\n    },\n    getCurrentUser: async ()=>{\n        const { data: { user }, error } = await supabase.auth.getUser();\n        return {\n            user,\n            error\n        };\n    },\n    onAuthStateChange: (callback)=>{\n        return supabase.auth.onAuthStateChange(callback);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/projects.ts":
/*!**************************************!*\
  !*** ./src/lib/supabase/projects.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createProject: () => (/* binding */ createProject),\n/* harmony export */   deleteProject: () => (/* binding */ deleteProject),\n/* harmony export */   getProject: () => (/* binding */ getProject),\n/* harmony export */   getProjects: () => (/* binding */ getProjects),\n/* harmony export */   updateProject: () => (/* binding */ updateProject)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\n// Get all projects for the authenticated user\nasync function getProjects() {\n    try {\n        const { data: projects, error: projectsError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"projects\").select(\"*\").order(\"created_at\", {\n            ascending: false\n        });\n        if (projectsError) {\n            console.error(\"Error fetching projects:\", projectsError);\n            return {\n                data: null,\n                error: projectsError\n            };\n        }\n        // Fetch founders for each project\n        const projectsWithFounders = await Promise.all((projects || []).map(async (project)=>{\n            const { data: founders } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"project_founders\").select(\"*\").eq(\"project_id\", project.id).order(\"created_at\", {\n                ascending: true\n            });\n            return {\n                ...project,\n                founders: founders || []\n            };\n        }));\n        return {\n            data: projectsWithFounders,\n            error: null\n        };\n    } catch (error) {\n        console.error(\"Error in getProjects:\", error);\n        return {\n            data: null,\n            error\n        };\n    }\n}\n// Get a single project by ID\nasync function getProject(id) {\n    try {\n        const { data: project, error: projectError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"projects\").select(\"*\").eq(\"id\", id).single();\n        if (projectError) {\n            console.error(\"Error fetching project:\", projectError);\n            return {\n                data: null,\n                error: projectError\n            };\n        }\n        // Fetch founders for the project\n        const { data: founders } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"project_founders\").select(\"*\").eq(\"project_id\", id).order(\"created_at\", {\n            ascending: true\n        });\n        const projectWithFounders = {\n            ...project,\n            founders: founders || []\n        };\n        return {\n            data: projectWithFounders,\n            error: null\n        };\n    } catch (error) {\n        console.error(\"Error in getProject:\", error);\n        return {\n            data: null,\n            error\n        };\n    }\n}\n// Create a new project\nasync function createProject(projectData) {\n    try {\n        const { data: { user } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            return {\n                data: null,\n                error: {\n                    message: \"User not authenticated\"\n                }\n            };\n        }\n        // Create the project\n        const { data: project, error: projectError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"projects\").insert({\n            user_id: user.id,\n            name: projectData.name,\n            description: projectData.description,\n            profile: projectData.profile,\n            website: projectData.website,\n            location: projectData.location,\n            target_customers: projectData.target_customers,\n            customer_problem: projectData.customer_problem,\n            solution: projectData.solution,\n            industry: projectData.industry,\n            target_market: projectData.target_market,\n            budget: projectData.budget,\n            timeline: projectData.timeline,\n            status: projectData.status || \"draft\"\n        }).select().single();\n        if (projectError) {\n            console.error(\"Error creating project:\", projectError);\n            return {\n                data: null,\n                error: projectError\n            };\n        }\n        // Create founders if provided\n        let founders = [];\n        if (projectData.founders && projectData.founders.length > 0) {\n            const foundersData = projectData.founders.map((founder)=>({\n                    project_id: project.id,\n                    name: founder.name,\n                    ownership_percentage: founder.ownership_percentage\n                }));\n            const { data: createdFounders, error: foundersError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"project_founders\").insert(foundersData).select();\n            if (foundersError) {\n                console.error(\"Error creating founders:\", foundersError);\n            // Don't fail the entire operation, just log the error\n            } else {\n                founders = createdFounders || [];\n            }\n        }\n        return {\n            data: {\n                ...project,\n                founders\n            },\n            error: null\n        };\n    } catch (error) {\n        console.error(\"Error in createProject:\", error);\n        return {\n            data: null,\n            error\n        };\n    }\n}\n// Update an existing project\nasync function updateProject(projectData) {\n    try {\n        const { id, founders, ...updateData } = projectData;\n        // Update the project\n        const { data: project, error: projectError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"projects\").update({\n            ...updateData,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", id).select().single();\n        if (projectError) {\n            console.error(\"Error updating project:\", projectError);\n            return {\n                data: null,\n                error: projectError\n            };\n        }\n        // Update founders if provided\n        let updatedFounders = [];\n        if (founders) {\n            // Delete existing founders\n            await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"project_founders\").delete().eq(\"project_id\", id);\n            // Insert new founders\n            if (founders.length > 0) {\n                const foundersData = founders.map((founder)=>({\n                        project_id: id,\n                        name: founder.name,\n                        ownership_percentage: founder.ownership_percentage\n                    }));\n                const { data: createdFounders, error: foundersError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"project_founders\").insert(foundersData).select();\n                if (foundersError) {\n                    console.error(\"Error updating founders:\", foundersError);\n                } else {\n                    updatedFounders = createdFounders || [];\n                }\n            }\n        } else {\n            // Fetch existing founders\n            const { data: existingFounders } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"project_founders\").select(\"*\").eq(\"project_id\", id).order(\"created_at\", {\n                ascending: true\n            });\n            updatedFounders = existingFounders || [];\n        }\n        return {\n            data: {\n                ...project,\n                founders: updatedFounders\n            },\n            error: null\n        };\n    } catch (error) {\n        console.error(\"Error in updateProject:\", error);\n        return {\n            data: null,\n            error\n        };\n    }\n}\n// Delete a project\nasync function deleteProject(id) {\n    try {\n        // Delete founders first (cascade should handle this, but being explicit)\n        await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"project_founders\").delete().eq(\"project_id\", id);\n        // Delete the project\n        const { error: projectError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"projects\").delete().eq(\"id\", id);\n        if (projectError) {\n            console.error(\"Error deleting project:\", projectError);\n            return {\n                error: projectError\n            };\n        }\n        return {\n            error: null\n        };\n    } catch (error) {\n        console.error(\"Error in deleteProject:\", error);\n        return {\n            error\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/projects.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL2J1aWxkZG9jd2l0aGFpLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"916782a38227\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVpbGRkb2N3aXRoYWkvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzQ3MmUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5MTY3ODJhMzgyMjdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_dashboard_Sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/dashboard/Sidebar */ \"(rsc)/./src/components/dashboard/Sidebar.tsx\");\n\n\nconst metadata = {\n    title: \"لوحة التحكم - BuildDocwithai\",\n    description: \"لوحة تحكم BuildDocwithai لإدارة مستنداتك ومشاريعك\",\n    robots: {\n        index: false,\n        follow: false\n    }\n};\nfunction DashboardLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-slate-900 to-slate-800\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_Sidebar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:pr-80 min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"p-4 sm:p-6 lg:p-8 max-w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/projects/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/projects/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectsPage),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_dashboard_ProjectsView__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/dashboard/ProjectsView */ \"(rsc)/./src/components/dashboard/ProjectsView.tsx\");\n\n\nconst metadata = {\n    title: \"مشاريعي - BuildDocwithai\",\n    description: \"إدارة وتنظيم جميع مشاريعك في مكان واحد\",\n    robots: {\n        index: false,\n        follow: false\n    }\n};\nfunction ProjectsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_ProjectsView__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\dashboard\\\\projects\\\\page.tsx\",\n        lineNumber: 14,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2Rhc2hib2FyZC9wcm9qZWN0cy9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDOEQ7QUFFdkQsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxRQUFRO1FBQ05DLE9BQU87UUFDUEMsUUFBUTtJQUNWO0FBQ0YsRUFBQztBQUVjLFNBQVNDO0lBQ3RCLHFCQUFPLDhEQUFDUCwwRUFBWUE7Ozs7O0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVpbGRkb2N3aXRoYWkvLi9zcmMvYXBwL2Rhc2hib2FyZC9wcm9qZWN0cy9wYWdlLnRzeD9jZWMyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IFByb2plY3RzVmlldyBmcm9tICdAL2NvbXBvbmVudHMvZGFzaGJvYXJkL1Byb2plY3RzVmlldydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICfZhdi02KfYsdmK2LnZiiAtIEJ1aWxkRG9jd2l0aGFpJyxcbiAgZGVzY3JpcHRpb246ICfYpdiv2KfYsdipINmI2KrZhti42YrZhSDYrNmF2YrYuSDZhdi02KfYsdmK2LnZgyDZgdmKINmF2YPYp9mGINmI2KfYrdivJyxcbiAgcm9ib3RzOiB7XG4gICAgaW5kZXg6IGZhbHNlLFxuICAgIGZvbGxvdzogZmFsc2UsXG4gIH0sXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFByb2plY3RzUGFnZSgpIHtcbiAgcmV0dXJuIDxQcm9qZWN0c1ZpZXcgLz5cbn1cbiJdLCJuYW1lcyI6WyJQcm9qZWN0c1ZpZXciLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJyb2JvdHMiLCJpbmRleCIsImZvbGxvdyIsIlByb2plY3RzUGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/dashboard/projects/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_fonts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/fonts */ \"(rsc)/./src/lib/fonts.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: \"BuildDocwithai - أنشئ مستندات عملك بالذكاء الاصطناعي\",\n    description: \"منصة متطورة تساعدك في إنشاء خطط العمل ودراسات الجدوى والعروض التقديمية في دقائق معدودة\",\n    keywords: [\n        \"ذكاء اصطناعي\",\n        \"مستندات\",\n        \"خطط عمل\",\n        \"دراسات جدوى\",\n        \"عروض تقديمية\"\n    ],\n    authors: [\n        {\n            name: \"BuildDocwithai Team\"\n        }\n    ],\n    creator: \"BuildDocwithai\",\n    publisher: \"BuildDocwithai\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"https://builddocwithai.com\"),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        title: \"BuildDocwithai - أنشئ مستندات عملك بالذكاء الاصطناعي\",\n        description: \"منصة متطورة تساعدك في إنشاء خطط العمل ودراسات الجدوى والعروض التقديمية في دقائق معدودة\",\n        url: \"https://builddocwithai.com\",\n        siteName: \"BuildDocwithai\",\n        locale: \"ar_SA\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"BuildDocwithai - أنشئ مستندات عملك بالذكاء الاصطناعي\",\n        description: \"منصة متطورة تساعدك في إنشاء خطط العمل ودراسات الجدوى والعروض التقديمية في دقائق معدودة\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        className: _lib_fonts__WEBPACK_IMPORTED_MODULE_1__.vazirmatnFont.variable,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Vazirmatn:wght@100..900&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${_lib_fonts__WEBPACK_IMPORTED_MODULE_1__.vazirmatnFont.className} antialiased bg-slate-900 text-white`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: [\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                            position: \"bottom-right\",\n                            richColors: true,\n                            theme: \"dark\",\n                            toastOptions: {\n                                style: {\n                                    fontFamily: \"var(--font-vazirmatn)\",\n                                    direction: \"rtl\",\n                                    textAlign: \"right\"\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(rsc)/./src/components/ui/Button.tsx\");\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-slate-900 flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-6xl font-bold text-indigo-500 mb-4\",\n                    children: \"404\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold text-white mb-4\",\n                    children: \"الصفحة غير موجودة\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-slate-400 mb-8 max-w-md mx-auto\",\n                    children: \"عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    href: \"/\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"gradient\",\n                        size: \"lg\",\n                        children: \"العودة للرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\not-found.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL25vdC1mb3VuZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTRCO0FBQ21CO0FBRWhDLFNBQVNFO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQTBDOzs7Ozs7OEJBQ3pELDhEQUFDQztvQkFBR0QsV0FBVTs4QkFBcUM7Ozs7Ozs4QkFDbkQsOERBQUNFO29CQUFFRixXQUFVOzhCQUF1Qzs7Ozs7OzhCQUdwRCw4REFBQ0osaURBQUlBO29CQUFDTyxNQUFLOzhCQUNULDRFQUFDTix5REFBTUE7d0JBQUNPLFNBQVE7d0JBQVdDLE1BQUs7a0NBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPL0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idWlsZGRvY3dpdGhhaS8uL3NyYy9hcHAvbm90LWZvdW5kLnRzeD9jYWUyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9CdXR0b24nXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE5vdEZvdW5kKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLXNsYXRlLTkwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBweC00XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC02eGwgZm9udC1ib2xkIHRleHQtaW5kaWdvLTUwMCBtYi00XCI+NDA0PC9kaXY+XG4gICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+2KfZhNi12YHYrdipINi62YrYsSDZhdmI2KzZiNiv2Kk8L2gxPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTQwMCBtYi04IG1heC13LW1kIG14LWF1dG9cIj5cbiAgICAgICAgICDYudiw2LHYp9mL2Iwg2KfZhNi12YHYrdipINin2YTYqtmKINiq2KjYrdirINi52YbZh9inINi62YrYsSDZhdmI2KzZiNiv2Kkg2KPZiCDYqtmFINmG2YLZhNmH2Kcg2KXZhNmJINmF2YjZgti5INii2K7YsS5cbiAgICAgICAgPC9wPlxuICAgICAgICA8TGluayBocmVmPVwiL1wiPlxuICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImdyYWRpZW50XCIgc2l6ZT1cImxnXCI+XG4gICAgICAgICAgICDYp9mE2LnZiNiv2Kkg2YTZhNix2KbZitiz2YrYqVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L0xpbms+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkxpbmsiLCJCdXR0b24iLCJOb3RGb3VuZCIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwicCIsImhyZWYiLCJ2YXJpYW50Iiwic2l6ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/dashboard/ProjectsView.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/ProjectsView.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\سطح المكتب\builddocwithai\src\components\dashboard\ProjectsView.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/dashboard/Sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/Sidebar.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\سطح المكتب\builddocwithai\src\components\dashboard\Sidebar.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            primary: \"bg-indigo-500 text-white hover:bg-indigo-600 shadow-lg hover:shadow-xl\",\n            secondary: \"bg-transparent border-2 border-white/20 text-white hover:border-white/40 hover:bg-white/5\",\n            outline: \"border border-slate-600 bg-transparent hover:bg-slate-800 hover:text-white\",\n            ghost: \"hover:bg-slate-800 hover:text-white\",\n            link: \"text-indigo-400 underline-offset-4 hover:underline\",\n            gradient: \"bg-gradient-to-r from-indigo-500 to-violet-500 text-white hover:from-indigo-600 hover:to-violet-600 shadow-lg hover:shadow-xl\"\n        },\n        size: {\n            default: \"h-12 px-6 py-3\",\n            sm: \"h-9 px-4 py-2 text-xs\",\n            lg: \"h-14 px-8 py-4 text-base\",\n            xl: \"h-16 px-10 py-5 text-lg\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"primary\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\سطح المكتب\\\\builddocwithai\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 41,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e1),
/* harmony export */   useAuth: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\سطح المكتب\builddocwithai\src\contexts\AuthContext.tsx#useAuth`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\سطح المكتب\builddocwithai\src\contexts\AuthContext.tsx#AuthProvider`);


/***/ }),

/***/ "(rsc)/./src/lib/fonts.ts":
/*!**************************!*\
  !*** ./src/lib/fonts.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   vazirmatnFont: () => (/* binding */ vazirmatnFont)\n/* harmony export */ });\n// Vazirmatn font for Arabic text (loaded via CSS)\nconst vazirmatnFont = {\n    className: \"font-vazirmatn\",\n    variable: \"--font-vazirmatn\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2ZvbnRzLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFDQSxrREFBa0Q7QUFDM0MsTUFBTUEsZ0JBQWdCO0lBQzNCQyxXQUFXO0lBQ1hDLFVBQVU7QUFDWixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVpbGRkb2N3aXRoYWkvLi9zcmMvbGliL2ZvbnRzLnRzPzY2ZGQiXSwic291cmNlc0NvbnRlbnQiOlsiXHJcbi8vIFZhemlybWF0biBmb250IGZvciBBcmFiaWMgdGV4dCAobG9hZGVkIHZpYSBDU1MpXHJcbmV4cG9ydCBjb25zdCB2YXppcm1hdG5Gb250ID0ge1xyXG4gIGNsYXNzTmFtZTogJ2ZvbnQtdmF6aXJtYXRuJyxcclxuICB2YXJpYWJsZTogJy0tZm9udC12YXppcm1hdG4nLFxyXG59XHJcbiJdLCJuYW1lcyI6WyJ2YXppcm1hdG5Gb250IiwiY2xhc3NOYW1lIiwidmFyaWFibGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/fonts.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL2J1aWxkZG9jd2l0aGFpLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/framer-motion","vendor-chunks/tr46","vendor-chunks/tailwind-merge","vendor-chunks/motion-dom","vendor-chunks/ws","vendor-chunks/sonner","vendor-chunks/lucide-react","vendor-chunks/whatwg-url","vendor-chunks/motion-utils","vendor-chunks/set-cookie-parser","vendor-chunks/class-variance-authority","vendor-chunks/webidl-conversions","vendor-chunks/jose","vendor-chunks/isows","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fprojects%2Fpage&page=%2Fdashboard%2Fprojects%2Fpage&appPaths=%2Fdashboard%2Fprojects%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fprojects%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmoham%5COneDrive%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5Cbuilddocwithai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmoham%5COneDrive%5C%D8%B3%D8%B7%D8%AD%20%D8%A7%D9%84%D9%85%D9%83%D8%AA%D8%A8%5Cbuilddocwithai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();