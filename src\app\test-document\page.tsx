'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/Button'
import { authHelpers } from '@/lib/supabase'

export default function TestDocumentPage() {
  const router = useRouter()
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedDocId, setGeneratedDocId] = useState<string | null>(null)
  const [user, setUser] = useState<any>(null)
  const [isLoggingIn, setIsLoggingIn] = useState(false)

  // Check authentication status
  useEffect(() => {
    const checkAuth = async () => {
      const { user } = await authHelpers.getCurrentUser()
      setUser(user)
    }
    checkAuth()
  }, [])

  // Test login function
  const handleTestLogin = async () => {
    setIsLoggingIn(true)
    try {
      // Create a test user directly using Supabase client
      const { supabase } = authHelpers

      // Try to sign in first
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'TestPassword123!'
      })

      if (signInError && signInError.message.includes('Invalid login credentials')) {
        // User doesn't exist, create it
        const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
          email: '<EMAIL>',
          password: 'TestPassword123!',
          options: {
            emailRedirectTo: undefined // Skip email confirmation for testing
          }
        })

        if (signUpError) {
          console.error('Sign up error:', signUpError)
          alert(`فشل في إنشاء الحساب التجريبي: ${signUpError.message}`)
          return
        }

        if (signUpData.user) {
          setUser(signUpData.user)
          alert('تم إنشاء حساب تجريبي وتسجيل الدخول بنجاح!')
        }
      } else if (signInError) {
        console.error('Sign in error:', signInError)
        alert(`فشل في تسجيل الدخول: ${signInError.message}`)
      } else if (signInData.user) {
        setUser(signInData.user)
        alert('تم تسجيل الدخول بنجاح!')
      }
    } catch (error) {
      console.error('Login error:', error)
      alert('حدث خطأ أثناء تسجيل الدخول')
    } finally {
      setIsLoggingIn(false)
    }
  }

  const handleGenerateTestDocument = async () => {
    setIsGenerating(true)
    try {
      const response = await fetch('/api/generate-document', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          templateId: 'business-plan',
          title: 'خطة عمل تجريبية - مشروع التجارة الإلكترونية',
          description: 'خطة عمل شاملة لمشروع متجر إلكتروني متخصص في بيع المنتجات المحلية',
          tier: 'quick',
          projectData: {
            id: 'test-project-123',
            name: 'مشروع التجارة الإلكترونية التجريبي',
            description: 'متجر إلكتروني لبيع المنتجات المحلية',
            industry: 'التجارة الإلكترونية',
            target_market: 'السوق المحلي',
            budget: '500000',
            timeline: '12 شهر'
          }
        })
      })

      if (!response.ok) {
        throw new Error('Failed to generate document')
      }

      const result = await response.json()
      setGeneratedDocId(result.document.id)
      
      // Redirect to the document view page
      router.push(`/dashboard/documents/${result.document.id}`)
    } catch (error) {
      console.error('Error generating document:', error)
      alert('فشل في إنشاء المستند')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleViewDocument = () => {
    if (generatedDocId) {
      router.push(`/dashboard/documents/${generatedDocId}`)
    }
  }

  return (
    <div className="min-h-screen bg-slate-900 flex items-center justify-center">
      <div className="max-w-md mx-auto p-8 bg-slate-800 rounded-2xl">
        <h1 className="text-2xl font-bold text-white font-vazirmatn mb-6 text-center">
          اختبار إنشاء المستندات
        </h1>

        {/* Authentication Status */}
        <div className="mb-6 p-4 bg-slate-700/50 rounded-lg">
          <p className="text-white font-vazirmatn mb-2 text-sm">
            حالة المصادقة: {user ? `مسجل الدخول (${user.email})` : 'غير مسجل الدخول'}
          </p>
          {!user && (
            <Button
              onClick={handleTestLogin}
              disabled={isLoggingIn}
              variant="secondary"
              size="sm"
              className="w-full font-vazirmatn"
            >
              {isLoggingIn ? 'جاري تسجيل الدخول...' : 'تسجيل دخول تجريبي'}
            </Button>
          )}
        </div>

        <div className="space-y-4">
          <Button
            onClick={handleGenerateTestDocument}
            disabled={isGenerating}
            className="w-full bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 font-vazirmatn"
          >
            {isGenerating ? (
              <>
                <div className="w-4 h-4 animate-spin rounded-full border-2 border-white border-t-transparent ml-2" />
                جاري الإنشاء...
              </>
            ) : (
              'إنشاء مستند تجريبي'
            )}
          </Button>

          {generatedDocId && (
            <Button
              onClick={handleViewDocument}
              variant="ghost"
              className="w-full text-slate-300 hover:text-white font-vazirmatn"
            >
              عرض المستند المُنشأ
            </Button>
          )}
        </div>

        <div className="mt-6 text-sm text-slate-400 font-vazirmatn text-center">
          <p>هذه الصفحة لاختبار وظيفة إنشاء وعرض المستندات</p>
        </div>
      </div>
    </div>
  )
}
