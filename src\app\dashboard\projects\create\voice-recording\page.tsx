'use client'

import { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Mic, Square, Play, Pause, RotateCcw, Download, Trash2 } from 'lucide-react'
import { toast } from 'sonner'
import ProjectInfoChecklist from '@/components/dashboard/ProjectInfoChecklist'
import Breadcrumb from '@/components/dashboard/Breadcrumb'
import { But<PERSON> } from '@/components/ui/Button'
import { PROJECT_CHECKLIST_ITEMS, VoiceRecording } from '@/lib/project-creation-types'

export default function VoiceRecordingPage() {
  const [recording, setRecording] = useState<VoiceRecording | null>(null)
  const [isRecording, setIsRecording] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [recordingTime, setRecordingTime] = useState(0)
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null)
  const [audioChunks, setAudioChunks] = useState<Blob[]>([])

  const audioRef = useRef<HTMLAudioElement>(null)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const recorder = new MediaRecorder(stream)

      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          setAudioChunks(prev => [...prev, event.data])
        }
      }

      recorder.onstop = () => {
        stream.getTracks().forEach(track => track.stop())
      }

      setMediaRecorder(recorder)
      recorder.start()
      setIsRecording(true)
      setRecordingTime(0)

      // Start timer
      intervalRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1)
      }, 1000)

      toast.success('بدأ التسجيل')
    } catch (error) {
      toast.error('فشل في الوصول إلى الميكروفون')
    }
  }

  const pauseRecording = () => {
    if (mediaRecorder && mediaRecorder.state === 'recording') {
      mediaRecorder.pause()
      setIsPaused(true)
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
      toast.info('تم إيقاف التسجيل مؤقتاً')
    }
  }

  const resumeRecording = () => {
    if (mediaRecorder && mediaRecorder.state === 'paused') {
      mediaRecorder.resume()
      setIsPaused(false)

      // Resume timer
      intervalRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1)
      }, 1000)

      toast.success('تم استئناف التسجيل')
    }
  }

  const stopRecording = () => {
    if (mediaRecorder) {
      mediaRecorder.stop()
      setIsRecording(false)
      setIsPaused(false)

      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }

      // Create recording object
      const newRecording: VoiceRecording = {
        id: Date.now().toString(),
        duration: recordingTime,
        isRecording: false,
        isPaused: false
      }

      // Process audio chunks
      setTimeout(() => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' })
        const audioUrl = URL.createObjectURL(audioBlob)

        setRecording({
          ...newRecording,
          url: audioUrl
        })

        setAudioChunks([])
        toast.success('تم حفظ التسجيل')
      }, 100)
    }
  }

  const deleteRecording = () => {
    if (recording?.url) {
      URL.revokeObjectURL(recording.url)
    }
    setRecording(null)
    setCurrentTime(0)
    setRecordingTime(0)
    setIsPlaying(false)
    toast.success('تم حذف التسجيل')
  }

  const togglePlayback = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause()
      } else {
        audioRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  const resetPlayback = () => {
    if (audioRef.current) {
      audioRef.current.currentTime = 0
      setCurrentTime(0)
      setIsPlaying(false)
    }
  }

  const downloadRecording = () => {
    if (recording?.url) {
      const link = document.createElement('a')
      link.href = recording.url
      link.download = `voice-recording-${recording.id}.wav`
      link.click()
      toast.success('تم تحميل التسجيل')
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const handleCreateProject = async () => {
    if (!recording) {
      toast.error('يرجى تسجيل صوتي أولاً')
      return
    }

    try {
      // Simulate project creation
      toast.success('تم إنشاء المشروع بنجاح!')
      // Redirect to projects list or project details
    } catch (error) {
      toast.error('فشل في إنشاء المشروع. يرجى المحاولة مرة أخرى')
    }
  }

  const breadcrumbItems = [
    { label: 'لوحة التحكم', href: '/dashboard' },
    { label: 'المشاريع', href: '/dashboard/projects' },
    { label: 'إنشاء مشروع', href: '/dashboard/projects/create' },
    { label: 'تسجيل صوتي' }
  ]

  return (
    <div className="space-y-8">
      {/* Breadcrumb */}
      <Breadcrumb items={breadcrumbItems} />

      {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-3xl font-bold text-white font-vazirmatn mb-2">
            تسجيل صوتي للبيانات
          </h1>
          <p className="text-slate-400 font-vazirmatn">
            سجل معلومات مشروعك صوتياً وتأكد من تضمين النقاط التالية
          </p>
        </motion.div>

        {/* Recording Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8"
        >
          <h2 className="text-xl font-semibold text-white font-vazirmatn mb-6">
            تسجيل صوتي
          </h2>

          <div className="space-y-6">
            {/* Recording Controls */}
            <div className="bg-slate-700/30 rounded-xl p-6 border border-slate-600/30">
              {!recording ? (
                <div className="flex flex-col items-center space-y-6">
                  <div className="w-24 h-24 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center">
                    <Mic className="w-12 h-12 text-white" />
                  </div>

                  <div className="text-center">
                    <h3 className="text-lg font-semibold text-white font-vazirmatn mb-2">
                      تسجيل معلومات المشروع
                    </h3>
                    <p className="text-slate-400 font-vazirmatn mb-4">
                      اضغط على زر التسجيل وابدأ في وصف مشروعك
                    </p>
                  </div>

                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    {!isRecording ? (
                      <div className="relative">
                        <Button
                          onClick={startRecording}
                          className="bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 rounded-full w-20 h-20 flex items-center justify-center shadow-lg hover:shadow-red-500/25 transition-all duration-300 hover:scale-105"
                        >
                          <Mic className="w-8 h-8 text-white" />
                        </Button>
                        <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                          <span className="text-xs text-slate-400 font-vazirmatn whitespace-nowrap">
                            اضغط للتسجيل
                          </span>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-6 rtl:space-x-reverse">
                        <div className="relative">
                          {isPaused ? (
                            <Button
                              onClick={resumeRecording}
                              className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 rounded-full w-16 h-16 flex items-center justify-center shadow-lg hover:shadow-green-500/25 transition-all duration-300"
                            >
                              <Play className="w-6 h-6 text-white mr-1" />
                            </Button>
                          ) : (
                            <Button
                              onClick={pauseRecording}
                              className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 rounded-full w-16 h-16 flex items-center justify-center shadow-lg hover:shadow-amber-500/25 transition-all duration-300"
                            >
                              <Pause className="w-6 h-6 text-white" />
                            </Button>
                          )}
                          <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                            <span className="text-xs text-slate-400 font-vazirmatn whitespace-nowrap">
                              {isPaused ? 'استئناف' : 'إيقاف مؤقت'}
                            </span>
                          </div>
                        </div>

                        <div className="relative">
                          <Button
                            onClick={stopRecording}
                            className="bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 rounded-full w-16 h-16 flex items-center justify-center shadow-lg hover:shadow-red-500/25 transition-all duration-300"
                          >
                            <Square className="w-6 h-6 text-white" />
                          </Button>
                          <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                            <span className="text-xs text-slate-400 font-vazirmatn whitespace-nowrap">
                              إيقاف
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {isRecording && (
                    <div className="flex flex-col items-center space-y-2">
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <div className="w-4 h-4 rounded-full bg-red-500 animate-pulse shadow-lg shadow-red-500/50" />
                        <span className="text-red-400 font-vazirmatn font-medium text-lg">
                          {formatTime(recordingTime)}
                        </span>
                        <div className="w-4 h-4 rounded-full bg-red-500 animate-pulse shadow-lg shadow-red-500/50" />
                      </div>
                      <div className="bg-red-500/10 border border-red-500/20 rounded-full px-4 py-1">
                        <span className="text-red-300 font-vazirmatn text-sm">
                          {isPaused ? 'متوقف مؤقتاً' : 'جاري التسجيل...'}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                        <Mic className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-white font-vazirmatn">
                          تسجيل صوتي
                        </h4>
                        <p className="text-sm text-slate-400 font-vazirmatn">
                          {formatTime(recording.duration)}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={downloadRecording}
                        className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10"
                      >
                        <Download className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={deleteRecording}
                        className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Audio Player */}
                  {recording.url && (
                    <div className="space-y-4">
                      <audio
                        ref={audioRef}
                        src={recording.url}
                        onTimeUpdate={() => {
                          if (audioRef.current) {
                            setCurrentTime(audioRef.current.currentTime)
                          }
                        }}
                        onEnded={() => setIsPlaying(false)}
                      />

                      <div className="flex items-center space-x-4 rtl:space-x-reverse">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={togglePlayback}
                          className="text-indigo-400 hover:text-indigo-300"
                        >
                          {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                        </Button>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={resetPlayback}
                          className="text-slate-400 hover:text-slate-300"
                        >
                          <RotateCcw className="w-4 h-4" />
                        </Button>

                        <div className="flex-1">
                          <div className="flex items-center justify-between text-xs text-slate-400 font-vazirmatn mb-1">
                            <span>{formatTime(currentTime)}</span>
                            <span>{formatTime(recording.duration)}</span>
                          </div>
                          <div className="w-full bg-slate-600 rounded-full h-2">
                            <div
                              className="bg-gradient-to-r from-indigo-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${recording.duration > 0 ? (currentTime / recording.duration) * 100 : 0}%` }}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Create Project Button */}
            {recording && (
              <div className="flex justify-end">
                <Button
                  onClick={handleCreateProject}
                  className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600"
                >
                  إنشاء المشروع
                </Button>
              </div>
            )}
          </div>
        </motion.div>

        {/* Project Information Checklist */}
        <ProjectInfoChecklist
          items={PROJECT_CHECKLIST_ITEMS}
          completedCount={0}
        />
      </div>
  )
}
