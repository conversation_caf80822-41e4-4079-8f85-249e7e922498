'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { MapPin, Plus, X, Search, User, Loader2 } from 'lucide-react'
import { toast } from 'sonner'
import Breadcrumb from '@/components/dashboard/Breadcrumb'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Textarea } from '@/components/ui/Textarea'
import { createProject, updateProject, getProject } from '@/lib/supabase/projects'
import { CreateProjectData } from '@/types/database'

interface Founder {
  id: string
  name: string
  share?: number
}

interface ProjectCreationData {
  name: string
  description: string
  projectFile: string
  targetCustomers: string
  customerProblem: string
  solution: string
  location: {
    useCurrentLocation: boolean
    address: string
    coordinates?: { lat: number; lng: number }
  }
  founders: Founder[]
}

export default function ManualEntryPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const editProjectId = searchParams.get('edit')
  const isEditMode = !!editProjectId

  const [formData, setFormData] = useState<ProjectCreationData>({
    name: '',
    description: '',
    projectFile: '',
    targetCustomers: '',
    customerProblem: '',
    solution: '',
    location: {
      useCurrentLocation: false,
      address: '',
      coordinates: undefined
    },
    founders: [{ id: '1', name: '', share: undefined }]
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Load project data for editing
  useEffect(() => {
    if (isEditMode && editProjectId) {
      loadProjectForEdit(editProjectId)
    }
  }, [isEditMode, editProjectId])

  const loadProjectForEdit = async (projectId: string) => {
    try {
      setIsLoading(true)
      const { data: project, error } = await getProject(projectId)

      if (error) {
        console.error('Error loading project:', error)
        toast.error('حدث خطأ في تحميل بيانات المشروع')
        router.push('/dashboard/projects')
        return
      }

      if (project) {
        setFormData({
          name: project.name,
          description: project.description,
          projectFile: project.profile || '',
          targetCustomers: project.target_customers || '',
          customerProblem: project.customer_problem || '',
          solution: project.solution || '',
          location: {
            useCurrentLocation: false,
            address: project.location || '',
            coordinates: undefined
          },
          founders: project.founders?.map((founder, index) => ({
            id: founder.id,
            name: founder.name,
            share: founder.ownership_percentage || undefined
          })) || [{ id: '1', name: '', share: undefined }]
        })
      }
    } catch (error) {
      console.error('Error in loadProjectForEdit:', error)
      toast.error('حدث خطأ في تحميل بيانات المشروع')
      router.push('/dashboard/projects')
    } finally {
      setIsLoading(false)
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'اسم المشروع مطلوب'
    }

    if (!formData.description.trim()) {
      newErrors.description = 'وصف المشروع مطلوب'
    }

    if (!formData.location.useCurrentLocation && !formData.location.address?.trim()) {
      newErrors.address = 'عنوان المشروع مطلوب'
    }

    if (formData.founders.length === 0 || !formData.founders[0].name.trim()) {
      newErrors.founders = 'يجب إضافة مؤسس واحد على الأقل'
    }

    // Validate founder shares sum to 100% if any shares are provided
    const foundersWithShares = formData.founders.filter(f => f.share !== undefined && f.share > 0)
    if (foundersWithShares.length > 0) {
      const totalShares = foundersWithShares.reduce((sum, f) => sum + (f.share || 0), 0)
      if (totalShares !== 100) {
        newErrors.shares = 'مجموع حصص المؤسسين يجب أن يساوي 100%'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: keyof ProjectCreationData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const handleLocationChange = (field: keyof ProjectCreationData['location'], value: any) => {
    setFormData(prev => ({
      ...prev,
      location: {
        ...prev.location,
        [field]: value
      }
    }))

    if (errors.address) {
      setErrors(prev => ({
        ...prev,
        address: ''
      }))
    }
  }

  const addFounder = () => {
    const newFounder: Founder = {
      id: Date.now().toString(),
      name: '',
      share: undefined
    }

    setFormData(prev => ({
      ...prev,
      founders: [...prev.founders, newFounder]
    }))
  }

  const removeFounder = (id: string) => {
    if (formData.founders.length > 1) {
      setFormData(prev => ({
        ...prev,
        founders: prev.founders.filter(f => f.id !== id)
      }))
    }
  }

  const updateFounder = (id: string, field: keyof Founder, value: any) => {
    setFormData(prev => ({
      ...prev,
      founders: prev.founders.map(f =>
        f.id === id ? { ...f, [field]: value } : f
      )
    }))

    if (errors.founders || errors.shares) {
      setErrors(prev => ({
        ...prev,
        founders: '',
        shares: ''
      }))
    }
  }

  const handleCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setFormData(prev => ({
            ...prev,
            location: {
              ...prev.location,
              useCurrentLocation: true,
              coordinates: {
                lat: position.coords.latitude,
                lng: position.coords.longitude
              }
            }
          }))
          toast.success('تم تحديد موقعك الحالي')
        },
        (error) => {
          toast.error('فشل في تحديد الموقع الحالي')
        }
      )
    } else {
      toast.error('المتصفح لا يدعم تحديد الموقع')
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      toast.error('يرجى تصحيح الأخطاء في النموذج')
      return
    }

    setIsSubmitting(true)

    try {
      const projectData: CreateProjectData = {
        name: formData.name,
        description: formData.description,
        profile: formData.projectFile || undefined,
        location: formData.location.address || undefined,
        target_customers: formData.targetCustomers || undefined,
        customer_problem: formData.customerProblem || undefined,
        solution: formData.solution || undefined,
        status: 'draft',
        founders: formData.founders
          .filter(founder => founder.name.trim())
          .map(founder => ({
            name: founder.name,
            ownership_percentage: founder.share
          }))
      }

      let result
      if (isEditMode && editProjectId) {
        // Update existing project
        result = await updateProject({
          id: editProjectId,
          ...projectData
        })
      } else {
        // Create new project
        result = await createProject(projectData)
      }

      if (result.error) {
        console.error('Error saving project:', result.error)
        toast.error(isEditMode ? 'حدث خطأ أثناء تحديث المشروع' : 'حدث خطأ أثناء إنشاء المشروع')
        return
      }

      toast.success(isEditMode ? 'تم تحديث المشروع بنجاح!' : 'تم إنشاء المشروع بنجاح!')

      // Redirect to projects page
      router.push('/dashboard/projects')
    } catch (error) {
      console.error('Error in handleSubmit:', error)
      toast.error(isEditMode ? 'حدث خطأ أثناء تحديث المشروع' : 'حدث خطأ أثناء إنشاء المشروع')
    } finally {
      setIsSubmitting(false)
    }
  }

  const breadcrumbItems = [
    { label: 'لوحة التحكم', href: '/dashboard' },
    { label: 'المشاريع', href: '/dashboard/projects' },
    ...(isEditMode
      ? [{ label: 'تعديل المشروع' }]
      : [
          { label: 'إنشاء مشروع', href: '/dashboard/projects/create' },
          { label: 'إدخال يدوي' }
        ]
    )
  ]

  // Show loading state while loading project data for edit
  if (isEditMode && isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-indigo-500 mx-auto mb-4" />
          <p className="text-slate-400 font-vazirmatn">جاري تحميل بيانات المشروع...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Breadcrumb */}
      <Breadcrumb items={breadcrumbItems} />

      {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-3xl font-bold text-white font-vazirmatn mb-2">
            {isEditMode ? 'تعديل المشروع' : 'إنشاء مشروع جديد'}
          </h1>
          <p className="text-slate-400 font-vazirmatn">
            {isEditMode ? 'قم بتعديل معلومات مشروعك' : 'أدخل معلومات مشروعك يدوياً بشكل مفصل'}
          </p>
        </motion.div>

        {/* Form */}
        <motion.form
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          onSubmit={handleSubmit}
          className="space-y-8"
        >
          {/* Project Details Section */}
          <div className="bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8">
            <h2 className="text-xl font-semibold text-white font-vazirmatn mb-6">
              تفاصيل المشروع
            </h2>

            <div className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-slate-300 font-vazirmatn mb-2">
                  اسم المشروع <span className="text-red-400">*</span>
                </label>
                <Input
                  id="name"
                  placeholder="أدخل اسم المشروع"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  error={errors.name}
                  required
                />
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-slate-300 font-vazirmatn mb-2">
                  وصف المشروع <span className="text-red-400">*</span>
                </label>
                <Textarea
                  id="description"
                  placeholder="صف مشروعك بإيجاز"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  error={errors.description}
                  required
                />
              </div>

              <div>
                <label htmlFor="projectFile" className="block text-sm font-medium text-slate-300 font-vazirmatn mb-2">
                  ملف المشروع <span className="text-slate-500">(اختياري ولكن موصى به)</span>
                </label>
                <Textarea
                  id="projectFile"
                  placeholder="صف الجوانب الأساسية لفكرة مشروعك"
                  value={formData.projectFile || ''}
                  onChange={(e) => handleInputChange('projectFile', e.target.value)}
                  rows={4}
                />
                <p className="mt-1 text-xs text-slate-500 font-vazirmatn">
                  وصف تفصيلي للمشروع يساعد في إنشاء مستندات أكثر دقة
                </p>
              </div>

              <div>
                <label htmlFor="targetCustomers" className="block text-sm font-medium text-slate-300 font-vazirmatn mb-2">
                  الشريحة المستهدفة من العملاء <span className="text-slate-500">(اختياري)</span>
                </label>
                <Input
                  id="targetCustomers"
                  placeholder="من هم عملاؤك المستهدفون؟"
                  value={formData.targetCustomers || ''}
                  onChange={(e) => handleInputChange('targetCustomers', e.target.value)}
                />
              </div>

              <div>
                <label htmlFor="customerProblem" className="block text-sm font-medium text-slate-300 font-vazirmatn mb-2">
                  مشكلة العميل <span className="text-slate-500">(اختياري)</span>
                </label>
                <Input
                  id="customerProblem"
                  placeholder="ما هي المشكلة التي تحلها لهم؟"
                  value={formData.customerProblem || ''}
                  onChange={(e) => handleInputChange('customerProblem', e.target.value)}
                />
              </div>

              <div>
                <label htmlFor="solution" className="block text-sm font-medium text-slate-300 font-vazirmatn mb-2">
                  الحل الخاص بك <span className="text-slate-500">(اختياري)</span>
                </label>
                <Input
                  id="solution"
                  placeholder="كيف يحل مشروعك المشكلة؟"
                  value={formData.solution || ''}
                  onChange={(e) => handleInputChange('solution', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Project Location Section */}
          <div className="bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8">
            <h2 className="text-xl font-semibold text-white font-vazirmatn mb-6">
              موقع المشروع
            </h2>

            <div className="space-y-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  type="button"
                  variant={formData.location.useCurrentLocation ? "primary" : "outline"}
                  onClick={handleCurrentLocation}
                  className="flex-1"
                >
                  <MapPin className="w-4 h-4 ml-2" />
                  استخدام موقعي
                </Button>

                <Button
                  type="button"
                  variant={!formData.location.useCurrentLocation ? "primary" : "outline"}
                  onClick={() => handleLocationChange('useCurrentLocation', false)}
                  className="flex-1"
                >
                  إدخال العنوان يدوياً
                </Button>
              </div>

              {!formData.location.useCurrentLocation && (
                <div className="space-y-4">
                  <div className="flex gap-2">
                    <Input
                      placeholder="أدخل عنوان المشروع واضغط بحث"
                      value={formData.location.address || ''}
                      onChange={(e) => handleLocationChange('address', e.target.value)}
                      error={errors.address}
                      className="flex-1"
                    />
                    <Button type="button" variant="outline">
                      <Search className="w-4 h-4 ml-2" />
                      بحث
                    </Button>
                  </div>

                  {/* Map Placeholder */}
                  <div className="bg-slate-700/30 rounded-xl p-8 border border-slate-600/30 text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                      <MapPin className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-lg font-semibold text-white font-vazirmatn mb-2">
                      خريطة تفاعلية
                    </h3>
                    <p className="text-slate-400 font-vazirmatn text-sm">
                      انقر/اسحب العلامة لتعيين الموقع. استخدم عناصر التحكم للتنقل
                    </p>
                  </div>
                </div>
              )}

              {formData.location.useCurrentLocation && formData.location.coordinates && (
                <div className="bg-green-500/10 border border-green-500/20 rounded-xl p-4">
                  <p className="text-green-300 font-vazirmatn text-sm">
                    ✅ تم تحديد موقعك الحالي بنجاح
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Founders Section */}
          <div className="bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-white font-vazirmatn">
                المؤسسون
              </h2>
              <Button
                type="button"
                variant="outline"
                onClick={addFounder}
                className="text-indigo-400 border-indigo-400 hover:bg-indigo-500/10"
              >
                <Plus className="w-4 h-4 ml-2" />
                إضافة مؤسس
              </Button>
            </div>

            <p className="text-slate-400 font-vazirmatn text-sm mb-6">
              اذكر مؤسسي المشروع
            </p>

            <div className="space-y-4">
              {formData.founders.map((founder, index) => (
                <motion.div
                  key={founder.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="bg-slate-700/30 rounded-xl p-6 border border-slate-600/30"
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <User className="w-5 h-5 text-indigo-400" />
                      <span className="text-white font-vazirmatn">
                        اسم المؤسس {index + 1}
                      </span>
                    </div>

                    {formData.founders.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFounder(founder.id)}
                        className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-300 font-vazirmatn mb-2">
                        الاسم الكامل للمؤسس <span className="text-red-400">*</span>
                      </label>
                      <Input
                        placeholder="الاسم الكامل للمؤسس"
                        value={founder.name}
                        onChange={(e) => updateFounder(founder.id, 'name', e.target.value)}
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-slate-300 font-vazirmatn mb-2">
                        الحصة (%) <span className="text-slate-500">(اختياري)</span>
                      </label>
                      <Input
                        type="number"
                        placeholder="مثال: 50"
                        min="0"
                        max="100"
                        value={founder.share || ''}
                        onChange={(e) => updateFounder(founder.id, 'share', e.target.value ? parseInt(e.target.value) : undefined)}
                      />
                    </div>
                  </div>
                </motion.div>
              ))}

              {errors.founders && (
                <p className="text-sm text-red-400 font-vazirmatn">{errors.founders}</p>
              )}

              {errors.shares && (
                <p className="text-sm text-red-400 font-vazirmatn">{errors.shares}</p>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 px-8 py-3"
            >
              {isSubmitting
                ? (isEditMode ? 'جاري التحديث...' : 'جاري الإنشاء...')
                : (isEditMode ? 'تحديث المشروع' : 'إنشاء المشروع')
              }
            </Button>
          </div>
        </motion.form>
      </div>
  )
}
